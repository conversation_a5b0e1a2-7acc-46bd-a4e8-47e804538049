@extends('layouts.admin')

@section('title', 'تقرير المصروفات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير المصروفات</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">عرض تفاصيل المصروفات وإحصائياتها</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- فلتر البحث -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <form action="{{ route('admin.reports.expenses') }}" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
            <input type="date" id="start_date" name="start_date" value="{{ $startDate }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        <div>
            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
            <input type="date" id="end_date" name="end_date" value="{{ $endDate }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        <div class="flex items-end">
            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">تطبيق الفلتر</button>
        </div>
    </form>
</div>

<!-- إجمالي المصروفات -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إجمالي المصروفات</h3>
    <div class="flex items-center">
        <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3 ml-4">
            <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
        </div>
        <div>
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ number_format($totalExpenses, 2) }} د.ل</p>
        </div>
    </div>
</div>

<!-- المصروفات حسب الفئة -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- رسم بياني للمصروفات حسب الفئة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">المصروفات حسب الفئة</h3>
        <div class="h-64">
            <canvas id="expensesByCategoryChart"></canvas>
        </div>
    </div>
    
    <!-- جدول المصروفات حسب الفئة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">تفاصيل المصروفات حسب الفئة</h3>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفئة</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">النسبة</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($expensesByCategory as $category)
                        <tr>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $category->category }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($category->total, 2) }} د.ل</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-800 dark:text-white">
                                {{ number_format(($category->total / $totalExpenses) * 100, 1) }}%
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- أعلى 10 مصروفات -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">أعلى 10 مصروفات</h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الوصف</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الفئة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">طريقة الدفع</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($topExpenses as $expense)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $expense->description }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $expense->category }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $expense->expense_date->format('Y-m-d') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($expense->amount, 2) }} د.ل</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $expense->payment_method }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // رسم بياني للمصروفات حسب الفئة
        const categoryCtx = document.getElementById('expensesByCategoryChart').getContext('2d');
        const categoryData = {
            labels: [
                @foreach($expensesByCategory as $category)
                    '{{ $category->category }}',
                @endforeach
            ],
            datasets: [{
                label: 'المصروفات حسب الفئة',
                data: [
                    @foreach($expensesByCategory as $category)
                        {{ $category->total }},
                    @endforeach
                ],
                backgroundColor: [
                    'rgba(255, 99, 132, 0.7)',
                    'rgba(54, 162, 235, 0.7)',
                    'rgba(255, 206, 86, 0.7)',
                    'rgba(75, 192, 192, 0.7)',
                    'rgba(153, 102, 255, 0.7)',
                    'rgba(255, 159, 64, 0.7)',
                    'rgba(199, 199, 199, 0.7)',
                    'rgba(83, 102, 255, 0.7)',
                    'rgba(40, 159, 64, 0.7)',
                    'rgba(210, 199, 199, 0.7)',
                ],
                borderColor: [
                    'rgba(255, 99, 132, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)',
                    'rgba(199, 199, 199, 1)',
                    'rgba(83, 102, 255, 1)',
                    'rgba(40, 159, 64, 1)',
                    'rgba(210, 199, 199, 1)',
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(categoryCtx, {
            type: 'pie',
            data: categoryData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection
