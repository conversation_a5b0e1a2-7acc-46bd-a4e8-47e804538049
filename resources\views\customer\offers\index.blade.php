@extends('customer.layouts.app')

@section('title', 'العروض المتاحة')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <nav class="text-sm text-gray-500 dark:text-gray-400 mb-4">
            <a href="{{ route('customer.dashboard') }}" class="hover:text-primary">الرئيسية</a>
            <span class="mx-2">/</span>
            <span>العروض</span>
        </nav>
        <h1 class="text-3xl font-bold text-darkText dark:text-white">العروض المتاحة</h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">اكتشف أحدث العروض والخصومات المتاحة في مطعمنا</p>
    </div>

    <!-- فلاتر العروض -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <div class="flex flex-wrap gap-4 items-center">
            <div class="flex-1 min-w-64">
                <input type="text" id="searchOffers" placeholder="البحث في العروض..."
                       class="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
            </div>
            <div class="flex gap-2">
                <select id="filterType" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">جميع الأنواع</option>
                    <option value="حجز">عروض الحجز</option>
                    <option value="طعام">عروض الطعام</option>
                </select>
                <select id="filterStatus" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    <option value="">جميع الحالات</option>
                    <option value="active">متاح</option>
                    <option value="expired">منتهي</option>
                </select>
            </div>
        </div>
    </div>

    <!-- قائمة العروض -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="offersGrid">
        @foreach($offers as $offer)
            <div class="offer-card bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition"
                 data-type="{{ $offer['type'] }}"
                 data-status="{{ $offer['is_active'] ? 'active' : 'expired' }}"
                 data-title="{{ strtolower($offer['title']) }}"
                 data-description="{{ strtolower($offer['description']) }}">

                <div class="relative">
                    <img src="{{ $offer['image_url'] ?? 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1' }}" alt="{{ $offer['title'] }}" class="w-full h-48 object-cover">

                    <!-- شارة نوع العرض -->
                    <div class="absolute top-4 left-4">
                        <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-semibold">
                            {{ $offer['type'] }}
                        </span>
                    </div>

                    <!-- شارة الخصم -->
                    @if($offer['discount'])
                        <div class="absolute top-4 right-4">
                            <span class="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                                خصم {{ $offer['discount'] }}%
                            </span>
                        </div>
                    @endif

                    <!-- شارة الحالة -->
                    <div class="absolute bottom-4 right-4">
                        <span class="px-3 py-1 rounded-full text-sm font-semibold {{ $offer['is_active'] ? 'bg-green-500 text-white' : 'bg-gray-500 text-white' }}">
                            {{ $offer['is_active'] ? 'متاح' : 'منتهي' }}
                        </span>
                    </div>
                </div>

                <div class="p-6">
                    <h3 class="text-xl font-bold text-darkText dark:text-white mb-3">{{ $offer['title'] }}</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">{{ $offer['description'] }}</p>

                    <div class="flex justify-between items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                        @if($offer['start_date'])
                        <span>من: {{ \Carbon\Carbon::parse($offer['start_date'])->format('Y/m/d') }}</span>
                        @endif
                        @if($offer['end_date'])
                        <span>إلى: {{ \Carbon\Carbon::parse($offer['end_date'])->format('Y/m/d') }}</span>
                        @else
                        <span>مفتوح</span>
                        @endif
                    </div>

                    <div class="flex gap-3">
                        <a href="{{ route('customer.offers.show', $offer['slug']) }}"
                           class="flex-1 bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-lg transition font-semibold">
                            عرض التفاصيل
                        </a>

                        @if($offer['is_active'])
                            @if($offer['type'] === 'حجز')
                                <a href="{{ route('customer.reservations.create') }}"
                                   class="flex-1 bg-accent hover:bg-accent/90 text-darkText text-center py-2 px-4 rounded-lg transition font-semibold">
                                    احجز الآن
                                </a>
                            @else
                                <a href="{{ route('customer.menu') }}"
                                   class="flex-1 bg-accent hover:bg-accent/90 text-darkText text-center py-2 px-4 rounded-lg transition font-semibold">
                                    اطلب الآن
                                </a>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <!-- رسالة عدم وجود نتائج -->
    <div id="noResults" class="text-center py-12 hidden">
        <div class="text-gray-400 text-6xl mb-4">🔍</div>
        <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-300 mb-2">لا توجد عروض مطابقة</h3>
        <p class="text-gray-500 dark:text-gray-400">جرب تغيير معايير البحث أو الفلترة</p>
    </div>

    <!-- إحصائيات العروض -->
    <div class="mt-12 bg-gradient-to-r from-primary to-primary/80 rounded-lg p-6 text-white">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
                <div class="text-3xl font-bold mb-2">{{ count($offers) }}</div>
                <div class="text-primary-light">إجمالي العروض</div>
            </div>
            <div>
                <div class="text-3xl font-bold mb-2">{{ collect($offers)->where('is_active', true)->count() }}</div>
                <div class="text-primary-light">عروض متاحة</div>
            </div>
            <div>
                <div class="text-3xl font-bold mb-2">{{ collect($offers)->whereNotNull('discount')->count() }}</div>
                <div class="text-primary-light">عروض بخصومات</div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchOffers');
    const typeFilter = document.getElementById('filterType');
    const statusFilter = document.getElementById('filterStatus');
    const offersGrid = document.getElementById('offersGrid');
    const noResults = document.getElementById('noResults');

    function filterOffers() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedType = typeFilter.value;
        const selectedStatus = statusFilter.value;

        const offerCards = document.querySelectorAll('.offer-card');
        let visibleCount = 0;

        offerCards.forEach(card => {
            const title = card.dataset.title;
            const description = card.dataset.description;
            const type = card.dataset.type;
            const status = card.dataset.status;

            const matchesSearch = !searchTerm || title.includes(searchTerm) || description.includes(searchTerm);
            const matchesType = !selectedType || type === selectedType;
            const matchesStatus = !selectedStatus || status === selectedStatus;

            if (matchesSearch && matchesType && matchesStatus) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // إظهار/إخفاء رسالة عدم وجود نتائج
        if (visibleCount === 0) {
            offersGrid.style.display = 'none';
            noResults.classList.remove('hidden');
        } else {
            offersGrid.style.display = 'grid';
            noResults.classList.add('hidden');
        }
    }

    // ربط الأحداث
    searchInput.addEventListener('input', filterOffers);
    typeFilter.addEventListener('change', filterOffers);
    statusFilter.addEventListener('change', filterOffers);
});
</script>
@endsection
