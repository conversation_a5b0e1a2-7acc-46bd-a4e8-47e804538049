<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminAccessMiddleware
{
    /**
     * Handle an incoming request.
     * يسمح للمديرين والموظفين المخولين بالوصول لصفحات الإدارة
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();
        
        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        // السماح للمديرين بالوصول لكل شيء
        if ($user->user_type === 'admin') {
            return $next($request);
        }

        // السماح للموظفين الذين لديهم أي صلاحية إدارية
        if ($user->user_type === 'employee') {
            $adminPermissions = [
                'dashboard.admin',
                'users.view', 'users.create', 'users.edit', 'users.delete', 'users.permissions',
                'menu.view', 'menu.create', 'menu.edit', 'menu.delete',
                'orders.view', 'orders.create', 'orders.edit', 'orders.delete', 'orders.status',
                'reservations.view', 'reservations.create', 'reservations.edit', 'reservations.delete', 'reservations.status',
                'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.delete', 'inventory.export',
                'ingredients.view', 'ingredients.create', 'ingredients.edit', 'ingredients.delete',
                'expenses.view', 'expenses.create', 'expenses.edit', 'expenses.delete',
                'reports.view', 'reports.financial', 'reports.sales', 'reports.inventory', 'reports.export',
                'tables.view', 'tables.create', 'tables.edit', 'tables.delete', 'tables.status',
                'payments.view', 'payments.create', 'payments.edit',
                'notifications.view', 'notifications.create', 'notifications.send', 'notifications.delete',
                'settings.view', 'settings.edit'
            ];

            // التحقق من وجود أي صلاحية إدارية
            foreach ($adminPermissions as $permission) {
                if ($user->can($permission)) {
                    return $next($request);
                }
            }

            // إذا لم يكن لديه أي صلاحية إدارية، إعادة توجيه للوحة الموظف
            return redirect()->route('employee.dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى لوحة تحكم المدير');
        }

        // إعادة توجيه العملاء
        if ($user->user_type === 'customer') {
            return redirect()->route('customer.dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى لوحة تحكم المدير');
        }

        return redirect()->route('login')->with('error', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    }
}
