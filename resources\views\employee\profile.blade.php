@extends('employee.layouts.app')

@section('title', 'الملف الشخصي - نظام إدارة المطعم')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/30 py-8">
    <div class="container mx-auto px-4">
        <!-- Header Section -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-6 shadow-2xl">
                <i class="fas fa-user-circle text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-black text-gray-900 dark:text-white mb-3">الملف الشخصي</h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">إدارة معلوماتك الشخصية وإعدادات حسابك بسهولة ومرونة</p>
        </div>

        <!-- Success/Error Messages -->
        @if(session('success'))
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 border-2 border-green-200 dark:border-green-700 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-green-800 dark:text-green-300">تم بنجاح!</h3>
                        <p class="text-green-700 dark:text-green-400">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(session('error'))
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/30 dark:to-pink-900/30 border-2 border-red-200 dark:border-red-700 rounded-2xl p-6 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-red-800 dark:text-red-300">خطأ!</h3>
                        <p class="text-red-700 dark:text-red-400">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- نموذج تحديث الملف الشخصي - اليسار -->
            <div class="lg:col-span-2 lg:order-2">
                <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20 dark:border-gray-700/50">
                    <!-- Header -->
                    <div class="bg-gradient-to-r from-indigo-500 via-purple-600 to-pink-600 p-6">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center ml-4">
                                <i class="fas fa-edit text-white"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-white">تحديث الملف الشخصي</h2>
                                <p class="text-white/80 text-sm">قم بتحديث معلوماتك الشخصية</p>
                            </div>
                        </div>
                    </div>

                    <!-- Form Content -->
                    <form action="{{ route('employee.profile.update') }}" method="POST" enctype="multipart/form-data" class="p-6 space-y-6">
                        @csrf
                        @method('PUT')

                        <!-- المعلومات الأساسية -->
                        <div class="space-y-4">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <h3 class="text-base font-bold text-gray-800 dark:text-white">المعلومات الأساسية</h3>
                            </div>

                            <div class="space-y-4">
                                <div class="space-y-2">
                                    <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                        <i class="fas fa-user-tag text-blue-500 ml-2 text-xs"></i>
                                        الاسم الأول
                                    </label>
                                    <input type="text" id="first_name" name="first_name" value="{{ old('first_name', $user->first_name) }}" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('first_name') border-red-500 dark:border-red-500 @enderror">
                                    @error('first_name')
                                    <p class="mt-1 text-xs text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                        <i class="fas fa-user-tag text-blue-500 ml-2 text-xs"></i>
                                        الاسم الأخير
                                    </label>
                                    <input type="text" id="last_name" name="last_name" value="{{ old('last_name', $user->last_name) }}" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('last_name') border-red-500 dark:border-red-500 @enderror">
                                    @error('last_name')
                                    <p class="mt-1 text-xs text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                        <i class="fas fa-envelope text-green-500 ml-2 text-xs"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('email') border-red-500 dark:border-red-500 @enderror">
                                    @error('email')
                                    <p class="mt-1 text-xs text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                        <i class="fas fa-phone text-purple-500 ml-2 text-xs"></i>
                                        رقم الهاتف
                                    </label>
                                    <input type="text" id="phone" name="phone" value="{{ old('phone', $user->phone) }}" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('phone') border-red-500 dark:border-red-500 @enderror">
                                    @error('phone')
                                    <p class="mt-1 text-xs text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- صورة الملف الشخصي -->
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-br from-pink-500 to-rose-500 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-camera text-white text-sm"></i>
                                </div>
                                <h3 class="text-base font-bold text-gray-800 dark:text-white">صورة الملف الشخصي</h3>
                            </div>

                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4">
                                <div class="flex items-center space-x-4 space-x-reverse">
                                    <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600 ring-2 ring-white dark:ring-gray-700 shadow-md">
                                        @if($user->profile_image)
                                        <img src="{{ asset('storage/' . $user->profile_image) }}" alt="{{ $user->first_name }}" class="w-full h-full object-cover">
                                        @else
                                        <div class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500">
                                            <i class="fas fa-user text-xl"></i>
                                        </div>
                                        @endif
                                    </div>
                                    <div class="flex-1">
                                        <label for="profile_image" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                                            <i class="fas fa-upload text-pink-500 ml-2 text-xs"></i>
                                            اختر صورة جديدة
                                        </label>
                                        <input type="file" id="profile_image" name="profile_image" accept="image/*" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('profile_image') border-red-500 dark:border-red-500 @enderror">
                                        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">يُفضل استخدام صور بحجم 400x400 بكسل</p>
                                        @error('profile_image')
                                        <p class="mt-1 text-xs text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle ml-1"></i>
                                            {{ $message }}
                                        </p>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تغيير كلمة المرور -->
                        <div class="border-t border-gray-200 dark:border-gray-600 pt-6">
                            <div class="flex items-center mb-4">
                                <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-pink-500 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-lock text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="text-base font-bold text-gray-800 dark:text-white">تغيير كلمة المرور</h3>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">اترك الحقول فارغة إذا كنت لا ترغب في تغيير كلمة المرور</p>
                                </div>
                            </div>

                            <div class="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 space-y-4">
                                <div class="space-y-2">
                                    <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                        <i class="fas fa-key text-orange-500 ml-2 text-xs"></i>
                                        كلمة المرور الحالية
                                    </label>
                                    <input type="password" id="current_password" name="current_password" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('current_password') border-red-500 dark:border-red-500 @enderror">
                                    @error('current_password')
                                    <p class="mt-1 text-xs text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-4">
                                    <div class="space-y-2">
                                        <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                            <i class="fas fa-lock text-red-500 ml-2 text-xs"></i>
                                            كلمة المرور الجديدة
                                        </label>
                                        <input type="password" id="password" name="password" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('password') border-red-500 dark:border-red-500 @enderror">
                                        @error('password')
                                        <p class="mt-1 text-xs text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle ml-1"></i>
                                            {{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <div class="space-y-2">
                                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                                            <i class="fas fa-shield-alt text-green-500 ml-2 text-xs"></i>
                                            تأكيد كلمة المرور
                                        </label>
                                        <input type="password" id="password_confirmation" name="password_confirmation" class="w-full px-3 py-2 border border-gray-200 dark:border-gray-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- زر الحفظ -->
                        <div class="flex justify-end pt-6">
                            <button type="submit" class="group relative px-6 py-3 bg-gradient-to-r from-blue-500 via-purple-600 to-indigo-700 text-white font-medium rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300 overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-blue-600 via-purple-700 to-indigo-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                <div class="relative flex items-center">
                                    <i class="fas fa-save ml-2 group-hover:scale-110 transition-transform text-sm"></i>
                                    <span class="text-sm">حفظ التغييرات</span>
                                </div>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- معلومات الملف الشخصي - اليمين -->
            <div class="lg:col-span-1 lg:order-1 space-y-6">
                <!-- بطاقة المعلومات الشخصية -->
                <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20 dark:border-gray-700/50">
                    <!-- Header with gradient -->
                    <div class="bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 p-6 text-center relative overflow-hidden">
                        <div class="absolute top-0 left-0 w-full h-full bg-black/10"></div>
                        <div class="relative z-10">
                            <div class="w-24 h-24 mx-auto bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center overflow-hidden mb-4 ring-4 ring-white/30 shadow-xl">
                                @if($user->profile_image)
                                <img src="{{ asset('storage/' . $user->profile_image) }}" alt="{{ $user->first_name }}" class="w-full h-full object-cover">
                                @else
                                <div class="text-3xl text-white/80">
                                    <i class="fas fa-user"></i>
                                </div>
                                @endif
                            </div>
                            <h2 class="text-xl font-bold text-white mb-2">{{ $user->first_name }} {{ $user->last_name }}</h2>
                            <div class="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full">
                                <i class="fas fa-user-tie text-white/80 ml-2 text-sm"></i>
                                <span class="text-white text-sm font-medium">{{ $user->role == 'admin' ? 'مدير' : ($user->role == 'employee' ? 'موظف' : 'عميل') }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="p-6">
                        <h3 class="text-base font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-address-card text-blue-500 ml-2 text-sm"></i>
                            معلومات الاتصال
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-envelope text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-xs text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                    <p class="text-sm font-medium text-gray-800 dark:text-white">{{ $user->email }}</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center ml-3">
                                    <i class="fas fa-phone text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <p class="text-xs text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                    <p class="text-sm font-medium text-gray-800 dark:text-white">{{ $user->phone }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إحصائيات الحساب -->
                <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/20 dark:border-gray-700/50">
                    <div class="p-6">
                        <h3 class="text-base font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                            <i class="fas fa-chart-line text-purple-500 ml-2 text-sm"></i>
                            إحصائيات الحساب
                        </h3>
                        <div class="grid grid-cols-1 gap-3">
                            <div class="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/30 dark:to-red-900/30 p-4 rounded-xl border border-orange-200 dark:border-orange-700/50">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xs text-orange-600 dark:text-orange-400 font-medium">منذ الانضمام</p>
                                        <p class="text-xl font-black text-orange-800 dark:text-orange-300">{{ (int) $user->created_at->diffInDays() }}</p>
                                        <p class="text-xs text-orange-500 dark:text-orange-400">يوم</p>
                                    </div>
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-calendar-plus text-white text-sm"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-4 rounded-xl border border-green-200 dark:border-green-700/50">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-xs text-green-600 dark:text-green-400 font-medium">آخر دخول</p>
                                        <p class="text-sm font-bold text-green-800 dark:text-green-300">{{ $user->last_login ? $user->last_login->diffForHumans() : 'غير متوفر' }}</p>
                                    </div>
                                    <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-clock text-white text-sm"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
@endsection
