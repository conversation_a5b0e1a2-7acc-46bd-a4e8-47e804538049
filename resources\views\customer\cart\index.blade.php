@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex items-center justify-between mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">سلة التسوق</h1>
                <p class="text-gray-600 dark:text-gray-400">
                    @if($itemsCount > 0)
                        لديك {{ $itemsCount }} عنصر في السلة
                    @else
                        السلة فارغة
                    @endif
                </p>
            </div>
            @if($cartItems->count() > 0)
            <button onclick="clearCart()" class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition">
                <i class="fas fa-trash ml-2"></i>
                مسح السلة
            </button>
            @endif
        </div>

        @if($cartItems->count() > 0)
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- عناصر السلة -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                    <div class="p-6">
                        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">عناصر السلة</h2>

                        <div class="space-y-4" id="cart-items">
                            @foreach($cartItems as $item)
                            <div class="cart-item flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg" data-cart-id="{{ $item->cart_id }}">
                                <!-- صورة المنتج -->
                                <div class="w-20 h-20 flex-shrink-0 ml-4">
                                    <img src="{{ $item->menuItem->image_path ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591' }}"
                                         alt="{{ $item->menuItem->name }}"
                                         class="w-full h-full object-cover rounded-lg">
                                </div>

                                <!-- تفاصيل المنتج -->
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-800 dark:text-white">{{ $item->menuItem->name }}</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ $item->menuItem->description }}</p>
                                    @if($item->special_instructions)
                                    <p class="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                        <i class="fas fa-sticky-note ml-1"></i>{{ $item->special_instructions }}
                                    </p>
                                    @endif
                                    <div class="flex items-center mt-2">
                                        <span class="text-lg font-bold text-primary">{{ number_format($item->price, 2) }} د.ل</span>
                                    </div>
                                </div>

                                <!-- التحكم في الكمية -->
                                <div class="flex items-center space-x-2 space-x-reverse ml-4">
                                    <button onclick="changeQuantity({{ $item->cart_id }}, -1)"
                                            class="w-8 h-8 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full flex items-center justify-center transition"
                                            data-cart-id="{{ $item->cart_id }}" data-action="decrease">
                                        <i class="fas fa-minus text-sm"></i>
                                    </button>
                                    <span class="quantity-display w-12 text-center font-medium text-gray-800 dark:text-white" data-cart-id="{{ $item->cart_id }}">{{ $item->quantity }}</span>
                                    <button onclick="changeQuantity({{ $item->cart_id }}, 1)"
                                            class="w-8 h-8 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full flex items-center justify-center transition"
                                            data-cart-id="{{ $item->cart_id }}" data-action="increase">
                                        <i class="fas fa-plus text-sm"></i>
                                    </button>
                                </div>

                                <!-- المجموع الفرعي -->
                                <div class="text-left ml-4">
                                    <div class="item-subtotal text-lg font-bold text-gray-800 dark:text-white">
                                        {{ number_format($item->subtotal, 2) }} د.ل
                                    </div>
                                    <button onclick="removeItem({{ $item->cart_id }})"
                                            class="text-red-500 hover:text-red-700 text-sm mt-1 transition">
                                        <i class="fas fa-trash ml-1"></i>حذف
                                    </button>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملخص الطلب -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6 sticky top-24">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">ملخص الطلب</h2>

                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">المجموع الفرعي:</span>
                            <span id="cart-total" class="font-medium text-gray-800 dark:text-white">{{ number_format($total, 2) }} د.ل</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">الضريبة (15%):</span>
                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($total * 0.15, 2) }} د.ل</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">رسوم التوصيل:</span>
                            <span class="font-medium text-gray-800 dark:text-white">10.00 د.ل</span>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex justify-between">
                                <span class="text-lg font-semibold text-gray-800 dark:text-white">المجموع الكلي:</span>
                                <span id="final-total" class="text-lg font-bold text-primary">{{ number_format($total + ($total * 0.15) + 10, 2) }} د.ل</span>
                            </div>
                        </div>
                    </div>

                    <!-- اختيار نوع الطلب -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">نوع الطلب:</h3>
                        <div class="grid grid-cols-1 gap-3">
                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 order-type-option">
                                <input type="radio" name="order_type_cart" value="delivery" class="ml-3" checked>
                                <div class="flex items-center">
                                    <i class="fas fa-truck text-primary text-lg ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">توصيل</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">رسوم التوصيل: 10 د.ل</div>
                                    </div>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 order-type-option">
                                <input type="radio" name="order_type_cart" value="pickup" class="ml-3">
                                <div class="flex items-center">
                                    <i class="fas fa-store text-green-500 text-lg ml-3"></i>
                                    <div>
                                        <div class="font-medium text-gray-800 dark:text-white">استلام من المطعم</div>
                                        <div class="text-sm text-gray-600 dark:text-gray-400">بدون رسوم إضافية</div>
                                    </div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <button onclick="proceedToCheckout()"
                                class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-credit-card ml-2"></i>
                            متابعة للدفع
                        </button>
                        <a href="{{ route('customer.menu') }}"
                           class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-6 rounded-lg transition text-center block">
                            <i class="fas fa-arrow-right ml-2"></i>
                            متابعة التسوق
                        </a>
                    </div>
                </div>
            </div>
        </div>

        @else
        <!-- حالة السلة الفارغة -->
        <div class="text-center py-12">
            <div class="max-w-md mx-auto">
                <i class="fas fa-shopping-cart text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">السلة فارغة</h3>
                <p class="text-gray-500 dark:text-gray-500 mb-6">لم تقم بإضافة أي عناصر للسلة بعد</p>
                <a href="{{ route('customer.menu') }}"
                   class="inline-block bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-utensils ml-2"></i>
                    تصفح القائمة
                </a>
            </div>
        </div>
        @endif
    </div>
</main>

@include('customer.partials.footer')

<!-- سكريبت إدارة السلة -->
<script>
// تغيير الكمية (زيادة أو نقصان)
function changeQuantity(cartId, change) {
    const quantityElement = document.querySelector(`[data-cart-id="${cartId}"].quantity-display`);
    const currentQuantity = parseInt(quantityElement.textContent);
    const newQuantity = currentQuantity + change;

    if (newQuantity < 1 || newQuantity > 10) return;

    updateQuantity(cartId, newQuantity);
}

// تحديث الكمية
function updateQuantity(cartId, newQuantity) {
    if (newQuantity < 1 || newQuantity > 10) return;

    fetch(`/customer/cart/update/${cartId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ quantity: newQuantity })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث العرض
            const cartItem = document.querySelector(`[data-cart-id="${cartId}"]`);
            cartItem.querySelector('.quantity-display').textContent = newQuantity;
            cartItem.querySelector('.item-subtotal').textContent = data.item_subtotal + ' د.ل';

            document.getElementById('cart-total').textContent = data.cart_total + ' د.ل';

            // تحديث المجموع النهائي
            updateCartTotal();

            // إشعار بسيط بدون نافذة منبثقة
            showSimpleNotification('تم تحديث الكمية', 'success');
        } else {
            showSimpleNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ أثناء تحديث الكمية', 'error');
    });
}

// حذف عنصر
function removeItem(cartId) {
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;

    fetch(`/customer/cart/remove/${cartId}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelector(`[data-cart-id="${cartId}"]`).remove();

            if (data.cart_count === 0) {
                location.reload();
            } else {
                document.getElementById('cart-total').textContent = data.cart_total + ' د.ل';

                // تحديث المجموع النهائي
                updateCartTotal();
            }

            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ أثناء حذف العنصر', 'error');
    });
}

// مسح السلة
function clearCart() {
    if (!confirm('هل أنت متأكد من مسح السلة بالكامل؟')) return;

    fetch('/customer/cart/clear', {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ أثناء مسح السلة', 'error');
    });
}

// عرض الإشعارات البسيطة
function showSimpleNotification(message, type) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500' : 'bg-red-500'
    }`;
    notification.textContent = message;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// عرض الإشعارات (للحالات الخاصة)
function showNotification(message, type) {
    showSimpleNotification(message, type);
}

// الانتقال لصفحة الدفع مع نوع الطلب
function proceedToCheckout() {
    const orderType = document.querySelector('input[name="order_type_cart"]:checked').value;
    window.location.href = `{{ route('customer.cart.checkout') }}?order_type=${orderType}`;
}

// تحديث المجموع الكلي عند تغيير نوع الطلب
document.addEventListener('DOMContentLoaded', function() {
    const orderTypeRadios = document.querySelectorAll('input[name="order_type_cart"]');
    const finalTotalElement = document.getElementById('final-total');

    orderTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updateCartTotal();

            // تحديث تصميم الخيارات
            document.querySelectorAll('.order-type-option').forEach(option => {
                option.classList.remove('border-primary', 'bg-primary/5');
                option.classList.add('border-gray-300', 'dark:border-gray-600');
            });

            this.closest('.order-type-option').classList.remove('border-gray-300', 'dark:border-gray-600');
            this.closest('.order-type-option').classList.add('border-primary', 'bg-primary/5');
        });
    });

    // تطبيق التصميم الأولي
    const checkedOption = document.querySelector('input[name="order_type_cart"]:checked');
    if (checkedOption) {
        checkedOption.closest('.order-type-option').classList.remove('border-gray-300', 'dark:border-gray-600');
        checkedOption.closest('.order-type-option').classList.add('border-primary', 'bg-primary/5');
    }
});

function updateCartTotal() {
    const cartTotalText = document.getElementById('cart-total').textContent;
    const cartTotal = parseFloat(cartTotalText.replace(' د.ل', ''));
    const tax = cartTotal * 0.15;

    const orderType = document.querySelector('input[name="order_type_cart"]:checked').value;
    const deliveryFee = orderType === 'pickup' ? 0 : 10;

    const finalTotal = cartTotal + tax + deliveryFee;
    document.getElementById('final-total').textContent = finalTotal.toFixed(2) + ' د.ل';
}
</script>

@include('customer.partials.scripts')

</body>
</html>
