<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- بطاقة إحصائية - الطلبات اليوم -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover relative overflow-hidden">
        <div class="absolute top-0 left-0 w-2 h-full bg-blue-500"></div>
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">الطلبات اليوم</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $todayStats['ordersCount'] ?? 0 }}</h3>
                <p class="text-green-500 text-sm mt-2 flex items-center">
                    <i class="fas fa-shopping-cart mr-1"></i>
                    <span>إجمالي المبيعات: {{ $todayStats['totalSales'] ?? 0 }} د.ل</span>
                </p>
                <div class="mt-3">
                    <a href="{{ route('employee.orders') }}" class="text-blue-500 text-xs hover:underline flex items-center">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
            </div>
        </div>
        <!-- شريط التقدم -->
        <div class="mt-4 h-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div class="h-full bg-blue-500 rounded-full" style="width: {{ min(($todayStats['ordersCount'] ?? 0) / max(($yesterdayStats['ordersCount'] ?? 1), 1) * 100, 100) }}%"></div>
        </div>
        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            @if(($todayStats['ordersCount'] ?? 0) > ($yesterdayStats['ordersCount'] ?? 0))
                <span class="text-green-500"><i class="fas fa-arrow-up mr-1"></i>{{ round((($todayStats['ordersCount'] ?? 0) - ($yesterdayStats['ordersCount'] ?? 0)) / max(($yesterdayStats['ordersCount'] ?? 1), 1) * 100) }}% زيادة</span> عن الأمس
            @elseif(($todayStats['ordersCount'] ?? 0) < ($yesterdayStats['ordersCount'] ?? 0))
                <span class="text-red-500"><i class="fas fa-arrow-down mr-1"></i>{{ round((($yesterdayStats['ordersCount'] ?? 0) - ($todayStats['ordersCount'] ?? 0)) / max(($yesterdayStats['ordersCount'] ?? 1), 1) * 100) }}% انخفاض</span> عن الأمس
            @else
                <span>نفس عدد الطلبات كالأمس</span>
            @endif
        </p>
    </div>

    <!-- بطاقة إحصائية - طلبات قيد التحضير -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover relative overflow-hidden">
        <div class="absolute top-0 left-0 w-2 h-full bg-yellow-500"></div>
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">قيد التحضير</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $todayStats['pendingOrdersCount'] ?? 0 }}</h3>
                <p class="text-yellow-500 text-sm mt-2 flex items-center">
                    <i class="fas fa-clock mr-1"></i>
                    <span>تحتاج إلى إكمال</span>
                </p>
                <div class="mt-3">
                    <a href="{{ route('employee.orders', ['status' => 'preparing']) }}" class="text-yellow-500 text-xs hover:underline flex items-center">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-utensils text-yellow-500 text-xl"></i>
            </div>
        </div>
        <!-- مؤشر الحالة -->
        <div class="mt-4 flex items-center">
            <div class="flex-1 flex items-center">
                @for($i = 0; $i < min(($todayStats['pendingOrdersCount'] ?? 0), 5); $i++)
                    <div class="h-2 w-2 rounded-full bg-yellow-500 mr-1"></div>
                @endfor
                @for($i = 0; $i < 5 - min(($todayStats['pendingOrdersCount'] ?? 0), 5); $i++)
                    <div class="h-2 w-2 rounded-full bg-gray-200 dark:bg-gray-700 mr-1"></div>
                @endfor
            </div>
            <span class="text-xs text-gray-500 dark:text-gray-400">
                @if(($todayStats['pendingOrdersCount'] ?? 0) > 0)
                    {{ $todayStats['pendingOrdersCount'] ?? 0 }} طلب ينتظر التحضير
                @else
                    لا توجد طلبات قيد التحضير
                @endif
            </span>
        </div>
    </div>

    <!-- بطاقة إحصائية - حجوزات اليوم -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover relative overflow-hidden">
        <div class="absolute top-0 left-0 w-2 h-full bg-purple-500"></div>
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">حجوزات اليوم</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $todayStats['reservationsCount'] ?? 0 }}</h3>
                <p class="text-purple-500 text-sm mt-2 flex items-center">
                    <i class="fas fa-calendar-alt mr-1"></i>
                    <span>{{ $todayReservations->count() }} حجز قادم</span>
                </p>
                <div class="mt-3">
                    <a href="{{ route('employee.reservations') }}" class="text-purple-500 text-xs hover:underline flex items-center">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                <i class="fas fa-calendar-check text-purple-500 text-xl"></i>
            </div>
        </div>
        <!-- الحجوزات القادمة -->
        <div class="mt-4">
            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>الصباح</span>
                <span>الظهر</span>
                <span>المساء</span>
            </div>
            <div class="mt-1 h-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden flex">
                <div class="h-full bg-purple-300 rounded-l-full" style="width: {{ ($morningReservations ?? 0) / max(($todayStats['reservationsCount'] ?? 1), 1) * 100 }}%"></div>
                <div class="h-full bg-purple-400" style="width: {{ ($afternoonReservations ?? 0) / max(($todayStats['reservationsCount'] ?? 1), 1) * 100 }}%"></div>
                <div class="h-full bg-purple-500 rounded-r-full" style="width: {{ ($eveningReservations ?? 0) / max(($todayStats['reservationsCount'] ?? 1), 1) * 100 }}%"></div>
            </div>
        </div>
    </div>

    <!-- بطاقة إحصائية - الطاولات المتاحة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 card-hover relative overflow-hidden">
        <div class="absolute top-0 left-0 w-2 h-full bg-green-500"></div>
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">الطاولات المتاحة</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $tableStats['available'] ?? 0 }}</h3>
                <p class="text-green-500 text-sm mt-2 flex items-center">
                    <i class="fas fa-chair mr-1"></i>
                    <span>من أصل {{ $tableStats['total'] ?? 0 }} طاولة</span>
                </p>
                <div class="mt-3">
                    <a href="{{ route('employee.tables') }}" class="text-green-500 text-xs hover:underline flex items-center">
                        <span>عرض التفاصيل</span>
                        <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-chair text-green-500 text-xl"></i>
            </div>
        </div>
        <!-- نسبة الطاولات المتاحة -->
        <div class="mt-4">
            <div class="flex justify-between items-center">
                <div class="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                    <div class="h-full bg-green-500 rounded-full" style="width: {{ ($tableStats['available'] ?? 0) / max(($tableStats['total'] ?? 1), 1) * 100 }}%"></div>
                </div>
                <span class="mr-2 text-xs font-medium text-gray-700 dark:text-gray-300">{{ round(($tableStats['available'] ?? 0) / max(($tableStats['total'] ?? 1), 1) * 100) }}%</span>
            </div>
            <div class="mt-2 flex justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>متاحة: {{ $tableStats['available'] ?? 0 }}</span>
                <span>مشغولة: {{ $tableStats['occupied'] ?? 0 }}</span>
                <span>محجوزة: {{ $tableStats['reserved'] ?? 0 }}</span>
            </div>
        </div>
    </div>
</div>