@echo off
echo ========================================
echo    Eat Hub - Restaurant Management System
echo    إعداد سريع للمشروع على جهاز جديد
echo ========================================
echo.

echo [1/10] التحقق من متطلبات النظام...
php --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: PHP غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت PHP 8.1 أو أحدث
    pause
    exit /b 1
)

composer --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Composer غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Composer
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Node.js/npm غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Node.js
    pause
    exit /b 1
)

echo ✓ جميع المتطلبات متوفرة
echo.

echo [2/10] تثبيت تبعيات PHP...
composer install --no-dev --optimize-autoloader
if errorlevel 1 (
    echo خطأ في تثبيت تبعيات PHP
    pause
    exit /b 1
)
echo ✓ تم تثبيت تبعيات PHP بنجاح
echo.

echo [3/10] تثبيت تبعيات Node.js...
npm install
if errorlevel 1 (
    echo خطأ في تثبيت تبعيات Node.js
    pause
    exit /b 1
)
echo ✓ تم تثبيت تبعيات Node.js بنجاح
echo.

echo [4/10] إعداد ملف البيئة...
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo ✓ تم نسخ ملف .env من .env.example
    ) else (
        echo تحذير: ملف .env.example غير موجود
        echo يرجى إنشاء ملف .env يدوياً
    )
) else (
    echo ✓ ملف .env موجود بالفعل
)
echo.

echo [5/10] إنشاء مفتاح التطبيق...
php artisan key:generate --force
if errorlevel 1 (
    echo خطأ في إنشاء مفتاح التطبيق
    pause
    exit /b 1
)
echo ✓ تم إنشاء مفتاح التطبيق بنجاح
echo.

echo [6/10] مسح الذاكرة المؤقتة...
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
echo ✓ تم مسح الذاكرة المؤقتة
echo.

echo [7/10] إنشاء رابط التخزين...
php artisan storage:link
echo ✓ تم إنشاء رابط التخزين
echo.

echo [8/10] بناء ملفات الواجهة الأمامية...
npm run build
if errorlevel 1 (
    echo تحذير: خطأ في بناء ملفات الواجهة الأمامية
    echo يمكنك تشغيل 'npm run dev' لاحقاً
)
echo ✓ تم بناء ملفات الواجهة الأمامية
echo.

echo [9/10] فحص الهجرات...
php fix_migrations.php
echo.

echo [10/10] التحقق من إعدادات قاعدة البيانات...
echo تحذير: تأكد من إعداد قاعدة البيانات في ملف .env
echo يجب إنشاء قاعدة البيانات يدوياً قبل تشغيل الهجرات
echo.

echo [11/11] الإعداد مكتمل!
echo.
echo ========================================
echo الخطوات التالية:
echo 1. تعديل إعدادات قاعدة البيانات في ملف .env
echo 2. إنشاء قاعدة البيانات في MySQL
echo 3. تشغيل: php artisan migrate
echo 4. تشغيل: php artisan db:seed
echo 5. تشغيل: php artisan serve
echo ========================================
echo.

set /p choice="هل تريد تشغيل الهجرات الآن؟ (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo تشغيل الهجرات (إعادة إنشاء قاعدة البيانات)...
    php artisan migrate:fresh
    if errorlevel 1 (
        echo خطأ في تشغيل الهجرات
        echo تأكد من إعدادات قاعدة البيانات في ملف .env
        echo جرب: php artisan migrate بدلاً من migrate:fresh
    ) else (
        echo ✓ تم تشغيل الهجرات بنجاح
        echo.
        set /p seed_choice="هل تريد تشغيل البذور (إدخال البيانات الأولية)؟ (y/n): "
        if /i "!seed_choice!"=="y" (
            php artisan db:seed
            if errorlevel 1 (
                echo خطأ في تشغيل البذور
            ) else (
                echo ✓ تم تشغيل البذور بنجاح
                echo.
                echo بيانات تسجيل الدخول الافتراضية:
                echo البريد الإلكتروني: <EMAIL>
                echo كلمة المرور: A178a2002
            )
        )
    )
)

echo.
set /p serve_choice="هل تريد تشغيل الخادم الآن؟ (y/n): "
if /i "%serve_choice%"=="y" (
    echo.
    echo تشغيل الخادم على http://localhost:8000
    echo اضغط Ctrl+C لإيقاف الخادم
    php artisan serve
)

echo.
echo شكراً لاستخدام نظام إدارة المطاعم Eat Hub!
pause
