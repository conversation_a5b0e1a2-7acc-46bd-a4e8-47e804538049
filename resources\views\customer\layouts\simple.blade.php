<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Eat Hub - تجربة طعام لا تنسى')</title>

    <!-- تحميل الوضع المظلم فوراً -->
    <script>
        // تحميل إعدادات الوضع المظلم قبل عرض الصفحة
        (function() {
            const savedDarkMode = localStorage.getItem('darkMode');
            if (savedDarkMode === 'true') {
                document.documentElement.classList.add('dark');
            } else if (savedDarkMode === 'false') {
                document.documentElement.classList.remove('dark');
            }
        })();
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',      // برتقالي محمر
                        secondary: '#4CAF50',    // أخضر للدلالة على الطازج
                        accent: '#FFEB3B',       // أصفر للتنبيهات والعروض
                        warmBrown: '#C8A080',    // بني دافئ للراحة
                        darkText: '#333333',     // للنصوص الداكنة
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.7s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'bounce-light': 'bounceLight 3s infinite',
                        'float': 'float 3s ease-in-out infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(50px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        bounceLight: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    @include('customer.partials.styles')
    @stack('styles')
</head>

<body class="bg-gray-50 dark:bg-gray-900">
    @include('customer.partials.header')

    <!-- المحتوى الرئيسي -->
    <main class="min-h-screen">
        @yield('content')
    </main>

    @include('customer.partials.footer')

    <!-- Scripts -->
    <script>
        // Dark Mode Toggle
        document.addEventListener('DOMContentLoaded', function() {
            const darkModeToggle = document.getElementById('darkModeToggle');
            const darkModeIcon = document.getElementById('darkModeIcon');

            if (darkModeToggle && darkModeIcon) {
                // تحديث الأيقونة عند التحميل
                const savedDarkMode = localStorage.getItem('darkMode');
                if (savedDarkMode === 'true') {
                    document.documentElement.classList.add('dark');
                    darkModeIcon.classList.remove('fa-moon');
                    darkModeIcon.classList.add('fa-sun');
                } else if (savedDarkMode === 'false') {
                    document.documentElement.classList.remove('dark');
                    darkModeIcon.classList.remove('fa-sun');
                    darkModeIcon.classList.add('fa-moon');
                }

                // إضافة مستمع الحدث
                darkModeToggle.addEventListener('click', function() {
                    if (document.documentElement.classList.contains('dark')) {
                        document.documentElement.classList.remove('dark');
                        darkModeIcon.classList.remove('fa-sun');
                        darkModeIcon.classList.add('fa-moon');
                        localStorage.setItem('darkMode', 'false');
                    } else {
                        document.documentElement.classList.add('dark');
                        darkModeIcon.classList.remove('fa-moon');
                        darkModeIcon.classList.add('fa-sun');
                        localStorage.setItem('darkMode', 'true');
                    }
                });
            }

            // Mobile Menu Toggle
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileMenu = document.getElementById('mobileMenu');

            if (mobileMenuToggle && mobileMenu) {
                mobileMenuToggle.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>

    @include('customer.partials.scripts')
    @stack('scripts')
</body>
</html>
