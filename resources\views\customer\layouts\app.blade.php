<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Eat Hub - تجربة طعام لا تُنسى')</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        secondary: '#2C3E50',
                        darkText: '#1A202C'
                    },
                    fontFamily: {
                        'arabic': ['Cairo', 'Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- تحميل الوضع المظلم فوراً -->
    <script>
        // تحميل إعدادات الوضع المظلم قبل عرض الصفحة
        (function() {
            const savedDarkMode = localStorage.getItem('darkMode');
            if (savedDarkMode === 'true') {
                document.documentElement.classList.add('dark');
            } else if (savedDarkMode === 'false') {
                document.documentElement.classList.remove('dark');
            }
        })();
    </script>

    <!-- Custom Styles -->
    <style>
        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        .animate-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .navbar-fixed {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .page {
            min-height: calc(100vh - 80px);
        }

        .hidden {
            display: none !important;
        }

        .tab-button.active {
            background-color: #FF6B35;
            color: white;
        }

        .category-btn:hover {
            background-color: #FF6B35;
            color: white;
        }

        .menu-item-card:hover .menu-item-image {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }
    </style>

    @stack('styles')
</head>
<body class="bg-gray-50 dark:bg-gray-900 font-arabic">
    <!-- Navigation -->
    <nav id="navbar" class="bg-white dark:bg-gray-800 shadow-sm fixed w-full top-0 z-50 transition-all duration-300">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center">
                    <div class="text-2xl font-bold text-primary flex items-center">
                        <i class="fas fa-utensils ml-2"></i>
                        <span>Eat Hub</span>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8 space-x-reverse">
                    <a href="{{ route('customer.dashboard') }}" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">الرئيسية</a>
                    <a href="{{ route('customer.menu') }}" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">قائمة الطعام</a>
                    <a href="{{ route('customer.offers.index') }}" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">العروض</a>
                    <a href="{{ route('customer.reservations') }}" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">الحجوزات</a>
                    <a href="{{ route('customer.orders') }}" class="nav-link text-gray-700 dark:text-gray-300 hover:text-primary transition">طلباتي</a>
                </div>

                <!-- User Actions -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Dark Mode Toggle -->
                    <button id="darkModeToggle" class="p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 transition">
                        <i id="darkModeIcon" class="fas fa-moon"></i>
                    </button>

                    @auth
                        <!-- User Menu -->
                        <div class="relative group">
                            <button class="flex items-center space-x-2 space-x-reverse text-gray-700 dark:text-gray-300 hover:text-primary transition">
                                <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                                    {{ substr(auth()->user()->first_name ?? 'أ', 0, 1) }}
                                </div>
                                <span class="hidden md:block">{{ auth()->user()->first_name ?? 'المستخدم' }}</span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>

                            <!-- Dropdown Menu -->
                            <div class="absolute left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <div class="py-2">
                                    <a href="{{ route('customer.dashboard') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                                        <i class="fas fa-tachometer-alt ml-2"></i>لوحة التحكم
                                    </a>
                                    <a href="{{ route('customer.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                                        <i class="fas fa-user ml-2"></i>الملف الشخصي
                                    </a>
                                    <a href="{{ route('customer.orders') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                                        <i class="fas fa-shopping-bag ml-2"></i>طلباتي
                                    </a>
                                    <a href="{{ route('customer.reservations') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                                        <i class="fas fa-calendar-alt ml-2"></i>حجوزاتي
                                    </a>
                                    <a href="{{ route('customer.offers.index') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition">
                                        <i class="fas fa-tags ml-2"></i>العروض
                                    </a>
                                    <div class="border-t border-gray-200 dark:border-gray-700 my-2"></div>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition">
                                            <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    @else
                        <!-- Login/Register Buttons -->
                        <a href="{{ route('login') }}" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg transition">
                            تسجيل الدخول
                        </a>
                    @endauth

                    <!-- Mobile Menu Toggle -->
                    <button id="mobileMenuToggle" class="md:hidden p-2 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobileMenu" class="md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 hidden">
            <div class="px-4 py-2 space-y-2">
                <a href="{{ route('customer.dashboard') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">الرئيسية</a>
                <a href="{{ route('customer.menu') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">قائمة الطعام</a>
                <a href="{{ route('customer.offers.index') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">العروض</a>
                <a href="{{ route('customer.reservations') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">الحجوزات</a>
                <a href="{{ route('customer.orders') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">طلباتي</a>
                @auth
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                        <a href="{{ route('customer.dashboard') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">لوحة التحكم</a>
                        <a href="{{ route('customer.profile') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">الملف الشخصي</a>
                        <a href="{{ route('customer.orders') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">طلباتي</a>
                        <a href="{{ route('customer.reservations') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">حجوزاتي</a>
                        <a href="{{ route('customer.offers.index') }}" class="mobile-nav-link block py-2 text-gray-700 dark:text-gray-300 hover:text-primary transition">العروض</a>
                    </div>
                @endauth
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 dark:bg-gray-900 text-white py-10 mt-16">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="text-2xl font-bold text-primary flex items-center mb-4">
                        <i class="fas fa-utensils ml-2"></i>
                        <span>Eat Hub</span>
                    </div>
                    <p class="text-gray-400 mb-4">تجربة طعام لا تُنسى</p>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-400 hover:text-primary transition">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary transition">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-primary transition">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('customer.dashboard') }}" class="text-gray-400 hover:text-primary transition">الرئيسية</a></li>
                        <li><a href="{{ route('customer.menu') }}" class="text-gray-400 hover:text-primary transition">قائمة الطعام</a></li>
                        <li><a href="{{ route('customer.offers.index') }}" class="text-gray-400 hover:text-primary transition">العروض</a></li>
                        <li><a href="{{ route('contact') }}" class="text-gray-400 hover:text-primary transition">اتصل بنا</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">خدماتنا</h3>
                    <ul class="space-y-2">
                        <li><a href="{{ route('customer.menu') }}" class="text-gray-400 hover:text-primary transition">تناول في المطعم</a></li>
                        <li><a href="{{ route('customer.orders') }}" class="text-gray-400 hover:text-primary transition">طلب للمنزل</a></li>
                        <li><a href="{{ route('customer.reservations') }}" class="text-gray-400 hover:text-primary transition">حجز طاولة</a></li>
                        <li><a href="{{ route('customer.offers.index') }}" class="text-gray-400 hover:text-primary transition">العروض الخاصة</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="text-lg font-bold mb-4">اتصل بنا</h3>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-map-marker-alt text-primary ml-2 mt-1"></i>
                            <span class="text-gray-400">طرابلس، ليبيا</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-phone-alt text-primary ml-2 mt-1"></i>
                            <span class="text-gray-400">+218 91 234 5678</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-envelope text-primary ml-2 mt-1"></i>
                            <span class="text-gray-400"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">© 2024 Eat Hub - جميع الحقوق محفوظة</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Dark Mode Toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        const darkModeIcon = document.getElementById('darkModeIcon');

        darkModeToggle.addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                darkModeIcon.classList.remove('fa-sun');
                darkModeIcon.classList.add('fa-moon');
                localStorage.setItem('darkMode', 'false');
            } else {
                document.documentElement.classList.add('dark');
                darkModeIcon.classList.remove('fa-moon');
                darkModeIcon.classList.add('fa-sun');
                localStorage.setItem('darkMode', 'true');
            }
        });

        // Load dark mode preference and update icon
        const savedDarkMode = localStorage.getItem('darkMode');
        if (savedDarkMode === 'true') {
            document.documentElement.classList.add('dark');
            darkModeIcon.classList.remove('fa-moon');
            darkModeIcon.classList.add('fa-sun');
        } else if (savedDarkMode === 'false') {
            document.documentElement.classList.remove('dark');
            darkModeIcon.classList.remove('fa-sun');
            darkModeIcon.classList.add('fa-moon');
        }

        // Mobile Menu Toggle
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const mobileMenu = document.getElementById('mobileMenu');

        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });

        // Navbar scroll effect
        const navbar = document.getElementById('navbar');
        window.addEventListener('scroll', function() {
            if (window.scrollY > 0) {
                navbar.classList.add('navbar-fixed', 'shadow-md');
                navbar.classList.add('bg-white/90', 'dark:bg-gray-800/90');
            } else {
                navbar.classList.remove('navbar-fixed', 'shadow-md');
                navbar.classList.remove('bg-white/90', 'dark:bg-gray-800/90');
            }
        });
    </script>

    @stack('scripts')
</body>
</html>
