<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Database\Capsule\Manager as Capsule;

// إعداد قاعدة البيانات
$capsule = new Capsule;

$capsule->addConnection([
    'driver' => 'mysql',
    'host' => '127.0.0.1',
    'database' => 'eat_hub',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_unicode_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

try {
    // اختبار الاتصال
    $pdo = $capsule->getConnection()->getPdo();
    echo "✅ تم الاتصال بقاعدة البيانات بنجاح\n";
    
    // التحقق من وجود جدول migrations
    $result = $capsule->getConnection()->select("SHOW TABLES LIKE 'migrations'");
    if (empty($result)) {
        echo "❌ جدول migrations غير موجود\n";
        echo "🔧 يجب تشغيل: php artisan migrate:install\n";
    } else {
        echo "✅ جدول migrations موجود\n";

        // عرض الهجرات المنفذة
        $migrations = $capsule->getConnection()->select("SELECT migration FROM migrations ORDER BY batch, migration");
        echo "\n📋 الهجرات المنفذة:\n";
        foreach ($migrations as $migration) {
            echo "  ✓ " . $migration->migration . "\n";
        }

        // التحقق من جدول tables
        $result = $capsule->getConnection()->select("SHOW TABLES LIKE 'tables'");
        if (!empty($result)) {
            echo "\n🪑 جدول tables موجود\n";

            // التحقق من أعمدة جدول tables
            $columns = $capsule->getConnection()->select("DESCRIBE tables");
            echo "📊 أعمدة جدول tables:\n";
            foreach ($columns as $column) {
                echo "  - " . $column->Field . " (" . $column->Type . ")\n";
            }
        } else {
            echo "\n❌ جدول tables غير موجود\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال: " . $e->getMessage() . "\n";
}

?>
