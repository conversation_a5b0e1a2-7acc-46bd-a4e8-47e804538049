<!-- القائمة الجانبية للشاشات الكبيرة -->
<aside id="sidebar" class="hidden md:flex md:flex-col w-80 glass backdrop-blur-xl shadow-glass sidebar-transition fixed right-0 top-0 h-full z-20 border-l border-white/20">
    <!-- الهيدر -->
    <div class="p-6 border-b border-white/10">
        <div class="flex items-center justify-between">
            <div class="relative">
                <span class="text-3xl font-black gradient-text">Eat Hub</span>
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
            </div>
            <div class="px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full font-bold">
                <i class="fas fa-crown mr-1"></i>مدير
            </div>
        </div>
        <p class="text-gray-300 text-sm mt-2 opacity-80">لوحة تحكم المدير</p>
    </div>

    <!-- القائمة الرئيسية -->
    <nav class="flex-1 p-4 space-y-2 overflow-y-auto">
        <!-- لوحة التحكم -->
        <a href="<?php echo e(route('admin.dashboard')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-tachometer-alt text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">لوحة التحكم</span>
                <p class="text-xs opacity-70">نظرة عامة على النظام</p>
            </div>
        </a>

        <!-- إدارة المستخدمين -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.view')): ?>
        <a href="<?php echo e(route('admin.users.index')); ?>"
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.users.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-users text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">المستخدمين</span>
                <p class="text-xs opacity-70">إدارة الحسابات</p>
            </div>
        </a>
        <?php endif; ?>

        <!-- إدارة الطلبات -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('orders.view')): ?>
        <a href="<?php echo e(route('admin.orders.index')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.orders.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-shopping-cart text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">الطلبات</span>
                <p class="text-xs opacity-70">إدارة الطلبات</p>
            </div>
        </a>
        <?php endif; ?>

        <!-- إدارة القائمة -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('menu.view')): ?>
        <a href="<?php echo e(route('admin.menu')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.menu*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-utensils text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">القائمة</span>
                <p class="text-xs opacity-70">إدارة الأطباق</p>
            </div>
        </a>
        <?php endif; ?>



        <!-- إدارة المخزون -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('inventory.view')): ?>
        <a href="<?php echo e(route('admin.inventory')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.inventory.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-boxes text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">المخزون</span>
                <p class="text-xs opacity-70">إدارة المواد</p>
            </div>
        </a>
        <?php endif; ?>

        <!-- التقارير -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('reports.view')): ?>
        <a href="<?php echo e(route('admin.reports')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.reports.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-chart-bar text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">التقارير</span>
                <p class="text-xs opacity-70">الإحصائيات</p>
            </div>
        </a>
        <?php endif; ?>

        <!-- المصروفات -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expenses.view')): ?>
        <a href="<?php echo e(route('admin.expenses')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.expenses.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-money-bill-wave text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">المصروفات</span>
                <p class="text-xs opacity-70">إدارة المصروفات</p>
            </div>
        </a>
        <?php endif; ?>

        <!-- الإعدادات -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('settings.view')): ?>
        <a href="<?php echo e(route('admin.settings')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.settings.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-gray-500 to-gray-600 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-cog text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">الإعدادات</span>
                <p class="text-xs opacity-70">إعدادات النظام</p>
            </div>
        </a>
        <?php endif; ?>

        <!-- الصلاحيات -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.permissions')): ?>
        <a href="<?php echo e(route('admin.permissions.index')); ?>" 
           class="nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group <?php echo e(request()->routeIs('admin.permissions.*') ? 'bg-gradient-to-r from-primary to-secondary text-white shadow-neon' : ''); ?>">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-amber-500 to-yellow-500 flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                <i class="fas fa-key text-white"></i>
            </div>
            <div class="flex-1">
                <span class="font-bold text-lg">الصلاحيات</span>
                <p class="text-xs opacity-70">إدارة الصلاحيات</p>
            </div>
        </a>
        <?php endif; ?>
    </nav>

    <!-- التنقل السريع -->
    <div class="p-4 border-t border-white/10">
        <div class="flex items-center justify-between mb-3">
            <span class="text-gray-300 text-sm font-bold">التنقل السريع</span>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="flex-1 p-3 rounded-xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group text-center"
               title="واجهة الموظف">
                <i class="fas fa-user-tie text-lg group-hover:scale-110 transition-transform"></i>
                <p class="text-xs mt-1">الموظف</p>
            </a>
            <a href="<?php echo e(route('home')); ?>"
               class="flex-1 p-3 rounded-xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group text-center"
               title="الموقع الرئيسي">
                <i class="fas fa-home text-lg group-hover:scale-110 transition-transform"></i>
                <p class="text-xs mt-1">الرئيسية</p>
            </a>
        </div>
    </div>
</aside>

<!-- شريط القائمة للجوال -->
<div id="mobileMenu" class="md:hidden glass backdrop-blur-xl shadow-glass sidebar-transition fixed inset-0 w-80 transform -translate-x-full z-30 border-r border-white/20">
    <div class="p-6 border-b border-white/10 flex justify-between items-center">
        <div class="flex items-center">
            <div class="relative">
                <span class="text-3xl font-black gradient-text">Eat Hub</span>
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-primary to-secondary rounded-full animate-pulse"></div>
            </div>
            <div class="mr-3 px-3 py-1 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full font-bold">
                <i class="fas fa-crown mr-1"></i>مدير
            </div>
        </div>
        <button id="closeMobileMenu" class="p-2 rounded-lg glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>

    <!-- نفس القائمة للجوال -->
    <nav class="flex-1 p-4 space-y-2 overflow-y-auto">
        <!-- نسخ نفس الروابط من الأعلى -->
        <a href="<?php echo e(route('admin.dashboard')); ?>" class="mobile-nav-link flex items-center p-4 rounded-2xl glass hover:neon-glow text-gray-300 hover:text-white transition-all duration-300 group">
            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-4">
                <i class="fas fa-tachometer-alt text-white"></i>
            </div>
            <span class="font-bold">لوحة التحكم</span>
        </a>
        <!-- باقي الروابط... -->
    </nav>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('sidebarToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    const closeMobileMenu = document.getElementById('closeMobileMenu');

    if (mobileMenuToggle && mobileMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('-translate-x-full');
        });
    }

    if (closeMobileMenu && mobileMenu) {
        closeMobileMenu.addEventListener('click', function() {
            mobileMenu.classList.add('-translate-x-full');
        });
    }

    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (mobileMenu && !mobileMenu.contains(event.target) && !mobileMenuToggle.contains(event.target)) {
            mobileMenu.classList.add('-translate-x-full');
        }
    });
});
</script>
<?php /**PATH D:\ccss450\cs450level10\resources\views/admin/layouts/sidebar.blade.php ENDPATH**/ ?>