@extends('customer.layouts.simple')

@section('title', 'حجوزاتي - Eat Hub')

@section('content')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">حجوزاتي</h1>
                <p class="text-gray-600 dark:text-gray-400">إدارة جميع حجوزاتك في مكان واحد</p>
            </div>
            <a href="{{ route('customer.reservations.create') }}"
               class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                <i class="fas fa-plus ml-2"></i>
                حجز جديد
            </a>
        </div>

        <!-- قائمة الحجوزات -->
        @if($reservations->count() > 0)
        <div class="grid gap-6">
            @foreach($reservations as $reservation)
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div class="flex-1">
                        <div class="flex items-center mb-2">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white ml-3">
                                حجز رقم #{{ $reservation->reservation_id }}
                            </h3>
                            <span class="px-3 py-1 rounded-full text-sm font-medium
                                @if($reservation->status == 'confirmed') bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                @elseif($reservation->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                @elseif($reservation->status == 'canceled') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                                @else bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 @endif">
                                @if($reservation->status == 'confirmed') مؤكد
                                @elseif($reservation->status == 'pending') في الانتظار
                                @elseif($reservation->status == 'canceled') ملغي
                                @else {{ $reservation->status }} @endif
                            </span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                            <div class="flex items-center">
                                <i class="fas fa-calendar-alt text-primary ml-2"></i>
                                <span>{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d') }}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-clock text-primary ml-2"></i>
                                <span>{{ \Carbon\Carbon::parse($reservation->reservation_time)->format('H:i') }}</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-users text-primary ml-2"></i>
                                <span>{{ $reservation->party_size ?? 'غير محدد' }} أشخاص</span>
                            </div>
                        </div>

                        @if($reservation->table)
                        <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                            <i class="fas fa-chair text-primary ml-2"></i>
                            طاولة رقم {{ $reservation->table->table_number }}
                        </div>
                        @endif

                        @if($reservation->offer_title)
                        <div class="mt-3 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-tag text-green-600 ml-2"></i>
                                <span class="text-sm font-medium text-green-800 dark:text-green-200">
                                    حجز من عرض: {{ $reservation->offer_title }}
                                </span>
                            </div>
                            @if($reservation->contact_phone)
                            <div class="mt-1 text-xs text-green-700 dark:text-green-300">
                                <i class="fas fa-phone ml-1"></i>
                                رقم التواصل: {{ $reservation->contact_phone }}
                            </div>
                            @endif
                        </div>
                        @endif

                        @if($reservation->special_requests)
                        <div class="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                <strong>ملاحظات خاصة:</strong> {{ $reservation->special_requests }}
                            </p>
                        </div>
                        @endif
                    </div>

                    <div class="mt-4 md:mt-0 md:mr-6 flex flex-col sm:flex-row gap-2">
                        @if($reservation->status == 'pending' || $reservation->status == 'confirmed')
                            @if(\Carbon\Carbon::parse($reservation->reservation_time)->isFuture())
                            <form action="{{ route('customer.reservations.cancel', $reservation->reservation_id) }}"
                                  method="POST"
                                  onsubmit="return confirm('هل أنت متأكد من إلغاء هذا الحجز؟')">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                        class="w-full sm:w-auto bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition">
                                    <i class="fas fa-times ml-1"></i>
                                    إلغاء الحجز
                                </button>
                            </form>
                            @endif
                        @endif

                        <a href="{{ route('customer.reservations.show', $reservation->reservation_id) }}"
                           class="w-full sm:w-auto bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-2 px-4 rounded-lg transition text-center">
                            <i class="fas fa-eye ml-1"></i>
                            عرض التفاصيل
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $reservations->links() }}
        </div>

        @else
        <!-- حالة عدم وجود حجوزات -->
        <div class="text-center py-12">
            <div class="max-w-md mx-auto">
                <i class="fas fa-calendar-times text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد حجوزات</h3>
                <p class="text-gray-500 dark:text-gray-500 mb-6">لم تقم بأي حجوزات بعد. احجز طاولتك الآن!</p>
                <a href="{{ route('customer.reservations.create') }}"
                   class="inline-block bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    احجز طاولة الآن
                </a>
            </div>
        </div>
        @endif
    </div>
@endsection
