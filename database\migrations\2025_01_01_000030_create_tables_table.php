<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('tables', function (Blueprint $table) {
            $table->id('table_id');
            $table->string('table_number', 20)->unique();
            $table->integer('capacity');
            $table->enum('status', ['available', 'occupied', 'reserved'])->default('available');
            $table->timestamps();
        });

        // إضافة constraint للـ capacity باستخدام raw SQL
        DB::statement('ALTER TABLE tables ADD CONSTRAINT capacity_positive CHECK (capacity > 0)');
    }

    public function down()
    {
        Schema::dropIfExists('tables');
    }
};