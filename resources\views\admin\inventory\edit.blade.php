@extends('layouts.admin')

@section('title', 'تعديل المخزون')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تعديل المخزون</h2>
    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">تعديل معلومات المخزون</p>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
        <form action="{{ route('admin.inventory.update', $inventory->inventory_id) }}" method="POST">
            @csrf
            @method('PUT')
            
            <div class="mb-6">
                <label for="ingredient_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المكون</label>
                <select id="ingredient_id" name="ingredient_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <option value="">اختر المكون</option>
                    @foreach($ingredients as $ingredient)
                        <option value="{{ $ingredient->ingredient_id }}" {{ old('ingredient_id', $inventory->ingredient_id) == $ingredient->ingredient_id ? 'selected' : '' }}>
                            {{ $ingredient->name }} ({{ $ingredient->unit }})
                        </option>
                    @endforeach
                </select>
                @error('ingredient_id')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الكمية</label>
                <div class="flex">
                    <input type="number" id="quantity" name="quantity" value="{{ old('quantity', $inventory->quantity) }}" step="0.01" min="0" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <span id="unit-display" class="inline-flex items-center px-3 py-2 text-gray-500 bg-gray-100 dark:bg-gray-600 dark:text-gray-300 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                        {{ $inventory->ingredient->unit }}
                    </span>
                </div>
                @error('quantity')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="cost_per_unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التكلفة للوحدة</label>
                <div class="flex">
                    <input type="number" id="cost_per_unit" name="cost_per_unit" value="{{ old('cost_per_unit', $inventory->cost_per_unit) }}" step="0.01" min="0.01" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <span class="inline-flex items-center px-3 py-2 text-gray-500 bg-gray-100 dark:bg-gray-600 dark:text-gray-300 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                        دينار
                    </span>
                </div>
                @error('cost_per_unit')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="mb-6">
                <label for="expiry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الانتهاء (اختياري)</label>
                <input type="date" id="expiry_date" name="expiry_date" value="{{ old('expiry_date', $inventory->expiry_date ? $inventory->expiry_date->format('Y-m-d') : '') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                @error('expiry_date')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>
            
            <div class="flex justify-end space-x-2 space-x-reverse">
                <a href="{{ route('admin.inventory') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">إلغاء</a>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">تحديث المخزون</button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ingredientSelect = document.getElementById('ingredient_id');
        const unitDisplay = document.getElementById('unit-display');
        
        // تحديث وحدة القياس عند تغيير المكون
        function updateUnit() {
            const selectedOption = ingredientSelect.options[ingredientSelect.selectedIndex];
            if (selectedOption.value) {
                const unitText = selectedOption.text.match(/\((.*?)\)/)[1];
                unitDisplay.textContent = unitText;
            } else {
                unitDisplay.textContent = 'وحدة';
            }
        }
        
        // تحديث الوحدة عند تغيير المكون
        ingredientSelect.addEventListener('change', updateUnit);
    });
</script>
@endsection
