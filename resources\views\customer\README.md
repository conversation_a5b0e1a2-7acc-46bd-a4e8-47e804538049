# هيكل ملفات العميل المنظم

تم تقسيم ملف `index.blade.php` الكبير إلى مجلدات منفصلة لتنظيم أفضل وسهولة الصيانة.

## الهيكل الجديد:

### 📁 partials/

الأجزاء المشتركة في الموقع:

-   `head.blade.php` - رأس الصفحة (HTML head, CSS, JS config)
-   `header.blade.php` - شريط التنقل العلوي
-   `mobile-menu.blade.php` - قائمة الجوال
-   `footer.blade.php` - تذييل الصفحة
-   `styles.blade.php` - ملف الأنماط CSS
-   `scripts.blade.php` - ملف JavaScript

### 📁 sections/

أقسام الصفحة الرئيسية:

-   `hero.blade.php` - قسم الترحيب الرئيسي
-   `featured-items.blade.php` - قسم الأطباق المميزة
-   `services.blade.php` - قسم الخدمات والمميزات
-   `reviews.blade.php` - قسم التقييمات
-   `reservation-offers.blade.php` - قسم الحجز والعروض

### 📁 pages/

الصفحات المختلفة:

-   `home.blade.php` - الصفحة الرئيسية (تجمع كل الأقسام)
-   `dashboard.blade.php` - لوحة تحكم المستخدم (للمستخدمين المسجلين)
-   `menu.blade.php` - صفحة القائمة الكاملة مع فلاتر
-   `about.blade.php` - صفحة من نحن مع معلومات المطعم
-   `contact.blade.php` - صفحة اتصل بنا مع نموذج التواصل
-   `profile.blade.php` - الملف الشخصي وإعدادات الحساب
-   `orders.blade.php` - صفحة طلباتي مع تتبع الطلبات
-   `reservations.blade.php` - صفحة حجوزاتي وإدارة الحجوزات

### 📁 components/

المكونات القابلة لإعادة الاستخدام:

-   `default-menu-item.blade.php` - عنصر قائمة افتراضي
-   `default-reviews.blade.php` - تقييمات افتراضية
-   `default-offers.blade.php` - عروض افتراضية

## الملف الرئيسي الجديد:

```php
@include('customer.partials.head')
@include('customer.partials.header')

<main class="min-h-screen">
    @include('customer.pages.home')

    <!-- صفحات أخرى -->
    @auth
    @include('customer.pages.dashboard')
    @endauth

    @include('customer.pages.menu')
    @include('customer.pages.about')
    @include('customer.pages.contact')
    @include('customer.pages.profile')
    @include('customer.pages.orders')
    @include('customer.pages.reservations')
</main>

@include('customer.partials.footer')
@include('customer.partials.scripts')
</body>
</html>
```

## المزايا:

1. **تنظيم أفضل**: كل جزء في ملف منفصل
2. **سهولة الصيانة**: يمكن تعديل أي قسم بشكل مستقل
3. **إعادة الاستخدام**: المكونات قابلة للاستخدام في صفحات أخرى
4. **قابلية التوسع**: سهولة إضافة صفحات وأقسام جديدة
5. **فصل الاهتمامات**: كل ملف له غرض محدد
6. **صفحات متعددة**: نظام تنقل بين الصفحات بدون إعادة تحميل
7. **تجربة مستخدم محسنة**: انتقالات سلسة وسريعة

## الصفحات المتوفرة:

### 🏠 الصفحة الرئيسية (home)

-   قسم الترحيب والعروض
-   قائمة مختارة من الأطباق
-   الخدمات والمميزات
-   تقييمات العملاء
-   نموذج الحجز

### 📊 لوحة التحكم (dashboard) - للمستخدمين المسجلين

-   إحصائيات شخصية
-   آخر الطلبات
-   الحجوزات القادمة
-   الأطباق المفضلة
-   الإشعارات

### 🍽️ قائمة الطعام (menu)

-   عرض جميع الأطباق
-   فلاتر حسب الفئة
-   تقييمات الأطباق
-   إضافة للسلة

### ℹ️ من نحن (about)

-   قصة المطعم
-   الرؤية والرسالة
-   فريق العمل
-   الإنجازات

### 📞 اتصل بنا (contact)

-   معلومات الاتصال
-   نموذج التواصل
-   الموقع على الخريطة
-   وسائل التواصل الاجتماعي

### 👤 الملف الشخصي (profile) - للمستخدمين المسجلين

-   تحديث المعلومات الشخصية
-   تغيير كلمة المرور
-   إعدادات الإشعارات
-   إحصائيات الحساب

### 🛍️ طلباتي (orders) - للمستخدمين المسجلين

-   عرض جميع الطلبات السابقة والحالية
-   تتبع حالة الطلبات
-   فلترة الطلبات حسب الحالة
-   إعادة الطلب وتحميل الفواتير

### 📅 حجوزاتي (reservations) - للمستخدمين المسجلين

-   عرض جميع الحجوزات
-   إنشاء حجوزات جديدة
-   تعديل أو إلغاء الحجوزات
-   تتبع الحجوزات القادمة

## كيفية الاستخدام:

1. استبدل `index.blade.php` القديم بـ `index_new.blade.php`
2. تأكد من وجود جميع الملفات في المجلدات الصحيحة
3. يمكنك إضافة صفحات جديدة في مجلد `pages/`
4. يمكنك إضافة مكونات جديدة في مجلد `components/`

## ملاحظات:

-   جميع البيانات الافتراضية موجودة في الكنترولر
-   الأنماط منفصلة في ملف `styles.blade.php`
-   JavaScript منفصل في ملف `scripts.blade.php`
-   يمكن إضافة المزيد من الصفحات حسب الحاجة
