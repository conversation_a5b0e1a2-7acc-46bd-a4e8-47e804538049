@extends('layouts.admin')

@section('title', 'الإعدادات - نظام إدارة المطعم')

@section('page-title', 'الإعدادات')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إعدادات الحساب</h1>
        <p class="text-gray-600 dark:text-gray-400">تخصيص إعدادات حسابك وتفضيلاتك</p>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-md dark:bg-green-900/30 dark:text-green-500 dark:border-green-500">
        <p>{{ session('success') }}</p>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- القائمة الجانبية -->
        <div class="md:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">الإعدادات</h2>
                </div>
                <div class="p-0">
                    <ul class="settings-tabs">
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none active-tab" data-tab="general">
                                <i class="fas fa-cog ml-2"></i>الإعدادات العامة
                            </button>
                        </li>
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none" data-tab="appearance">
                                <i class="fas fa-palette ml-2"></i>المظهر
                            </button>
                        </li>
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none" data-tab="notifications">
                                <i class="fas fa-bell ml-2"></i>الإشعارات
                            </button>
                        </li>
                        <li class="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                            <button class="w-full px-4 py-3 text-right text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none" data-tab="security">
                                <i class="fas fa-shield-alt ml-2"></i>الأمان
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- محتوى الإعدادات -->
        <div class="md:col-span-2">
            <form action="{{ route('employee.settings.update') }}" method="POST" class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                @csrf

                <!-- الإعدادات العامة -->
                <div id="general-tab" class="settings-content active">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-bold text-gray-800 dark:text-white">الإعدادات العامة</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">تخصيص الإعدادات العامة لحسابك</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <label for="language" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اللغة</label>
                            <select id="language" name="language" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                                <option value="ar" {{ (isset($user->settings) && json_decode($user->settings)->language ?? '') == 'ar' ? 'selected' : '' }}>العربية</option>
                                <option value="en" {{ (isset($user->settings) && json_decode($user->settings)->language ?? '') == 'en' ? 'selected' : '' }}>English</option>
                            </select>
                        </div>

                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المنطقة الزمنية</label>
                            <select id="timezone" name="timezone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                                <option value="Africa/Tripoli" selected>ليبيا (توقيت طرابلس)</option>
                                <option value="Africa/Cairo">مصر (توقيت القاهرة)</option>
                                <option value="Asia/Riyadh">السعودية (توقيت الرياض)</option>
                            </select>
                        </div>

                        <div>
                            <label for="date_format" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تنسيق التاريخ</label>
                            <select id="date_format" name="date_format" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                                <option value="Y-m-d">2023-05-22</option>
                                <option value="d/m/Y">22/05/2023</option>
                                <option value="d-m-Y">22-05-2023</option>
                                <option value="Y/m/d">2023/05/22</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- المظهر -->
                <div id="appearance-tab" class="settings-content hidden">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-bold text-gray-800 dark:text-white">المظهر</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">تخصيص مظهر التطبيق</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">وضع العرض</label>
                            <div class="grid grid-cols-3 gap-4">
                                <div class="theme-option">
                                    <input type="radio" id="theme-light" name="theme" value="light" class="hidden" {{ (isset($user->settings) && json_decode($user->settings)->theme ?? '') == 'light' ? 'checked' : '' }}>
                                    <label for="theme-light" class="block p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 text-center">
                                        <div class="w-full h-20 bg-white border border-gray-300 rounded-md mb-2 flex items-center justify-center">
                                            <i class="fas fa-sun text-2xl text-yellow-500"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">فاتح</span>
                                    </label>
                                </div>

                                <div class="theme-option">
                                    <input type="radio" id="theme-dark" name="theme" value="dark" class="hidden" {{ (isset($user->settings) && json_decode($user->settings)->theme ?? '') == 'dark' ? 'checked' : '' }}>
                                    <label for="theme-dark" class="block p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 text-center">
                                        <div class="w-full h-20 bg-gray-900 border border-gray-700 rounded-md mb-2 flex items-center justify-center">
                                            <i class="fas fa-moon text-2xl text-blue-400"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">داكن</span>
                                    </label>
                                </div>

                                <div class="theme-option">
                                    <input type="radio" id="theme-system" name="theme" value="system" class="hidden" {{ (isset($user->settings) && json_decode($user->settings)->theme ?? '') == 'system' ? 'checked' : '' }}>
                                    <label for="theme-system" class="block p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 text-center">
                                        <div class="w-full h-20 bg-gradient-to-r from-white to-gray-900 border border-gray-300 rounded-md mb-2 flex items-center justify-center">
                                            <i class="fas fa-desktop text-2xl text-gray-600"></i>
                                        </div>
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">تلقائي (النظام)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإشعارات -->
                <div id="notifications-tab" class="settings-content hidden">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-bold text-gray-800 dark:text-white">الإشعارات</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">إدارة إعدادات الإشعارات</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">تفعيل الإشعارات</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">استلام إشعارات من النظام</p>
                            </div>
                            <label class="switch">
                                <input type="checkbox" name="notifications_enabled" {{ (isset($user->settings) && json_decode($user->settings)->notifications_enabled ?? false) ? 'checked' : '' }}>
                                <span class="slider round"></span>
                            </label>
                        </div>

                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">إشعارات البريد الإلكتروني</h3>
                                <p class="text-xs text-gray-500 dark:text-gray-400">استلام إشعارات عبر البريد الإلكتروني</p>
                            </div>
                            <label class="switch">
                                <input type="checkbox" name="email_notifications" {{ (isset($user->settings) && json_decode($user->settings)->email_notifications ?? false) ? 'checked' : '' }}>
                                <span class="slider round"></span>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- الأمان -->
                <div id="security-tab" class="settings-content hidden">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <h2 class="text-lg font-bold text-gray-800 dark:text-white">الأمان</h2>
                        <p class="text-sm text-gray-600 dark:text-gray-400">إدارة إعدادات الأمان لحسابك</p>
                    </div>
                    <div class="p-6 space-y-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تغيير كلمة المرور</h3>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mb-4">يمكنك تغيير كلمة المرور الخاصة بك من خلال صفحة الملف الشخصي</p>
                            <a href="{{ route('employee.profile') }}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                                <i class="fas fa-user-circle ml-2"></i>الانتقال إلى الملف الشخصي
                            </a>
                        </div>
                    </div>
                </div>

                <div class="p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-700">
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                        حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .switch {
        position: relative;
        display: inline-block;
        width: 48px;
        height: 24px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #FF6B35;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #FF6B35;
    }

    input:checked + .slider:before {
        transform: translateX(24px);
    }

    .slider.round {
        border-radius: 24px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .theme-option input[type="radio"]:checked + label {
        border-color: #FF6B35;
        background-color: rgba(255, 107, 53, 0.1);
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabs = document.querySelectorAll('.settings-tabs button');
        const contents = document.querySelectorAll('.settings-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');

                // تحديث حالة التبويبات
                tabs.forEach(t => t.classList.remove('active-tab', 'bg-gray-100', 'dark:bg-gray-700', 'text-primary'));
                this.classList.add('active-tab', 'bg-gray-100', 'dark:bg-gray-700', 'text-primary');

                // تحديث المحتوى
                contents.forEach(content => {
                    if (content.id === tabId + '-tab') {
                        content.classList.remove('hidden');
                    } else {
                        content.classList.add('hidden');
                    }
                });
            });
        });
    });
</script>
@endsection
