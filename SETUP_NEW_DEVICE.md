# دليل إعداد المشروع على جهاز جديد
## Restaurant Management System - Eat Hub

هذا الدليل يوضح كيفية إعداد المشروع على جهاز جديد بحيث تعمل قاعدة البيانات بشكل طبيعي من البداية.

## المتطلبات الأساسية

### 1. تثبيت البرامج المطلوبة:
- **PHP 8.1 أو أحدث** مع الإضافات التالية:
  - php-mysql
  - php-mbstring
  - php-xml
  - php-curl
  - php-zip
  - php-gd
- **Composer** (مدير حزم PHP)
- **Node.js** و **npm** (لإدارة ملفات JavaScript/CSS)
- **MySQL** أو **MariaDB**
- **Git** (اختياري)

### 2. تثبيت خادم الويب:
- **XAMPP** (الأسهل للمبتدئين)
- أو **WAMP** (لنظام Windows)
- أو **MAMP** (لنظام Mac)
- أو **Apache/Nginx** منفصل

## خطوات الإعداد

### الخطوة 1: نسخ المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]

# أو انسخ مجلد المشروع كاملاً إلى الجهاز الجديد
```

### الخطوة 2: تثبيت التبعيات
```bash
# الانتقال إلى مجلد المشروع
cd cs450level10

# تثبيت تبعيات PHP
composer install

# تثبيت تبعيات Node.js
npm install
```

### الخطوة 3: إعداد ملف البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env
# أو في Windows
copy .env.example .env
```

### الخطوة 4: تعديل إعدادات قاعدة البيانات
افتح ملف `.env` وعدل الإعدادات التالية:

```env
# إعدادات قاعدة البيانات
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=eat_hub_new
DB_USERNAME=root
DB_PASSWORD=your_mysql_password

# إعدادات التطبيق
APP_NAME="Eat Hub - طرابلس"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://localhost:8000

# إعدادات اللغة
APP_LOCALE=ar
APP_FALLBACK_LOCALE=ar
```

### الخطوة 5: إنشاء قاعدة البيانات
```sql
-- اتصل بـ MySQL وأنشئ قاعدة البيانات
CREATE DATABASE eat_hub_new CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### الخطوة 6: إنشاء مفتاح التطبيق
```bash
php artisan key:generate
```

### الخطوة 7: تشغيل الهجرات (Migrations)
```bash
# تشغيل جميع الهجرات لإنشاء الجداول
php artisan migrate

# أو إذا كنت تريد إعادة إنشاء قاعدة البيانات من الصفر
php artisan migrate:fresh
```

### الخطوة 8: تشغيل البذور (Seeders)
```bash
# تشغيل البذور لإدخال البيانات الأولية
php artisan db:seed

# أو تشغيل بذرة محددة
php artisan db:seed --class=DatabaseSeeder
```

### الخطوة 9: إنشاء رابط التخزين
```bash
php artisan storage:link
```

### الخطوة 10: بناء ملفات CSS/JS
```bash
# بناء ملفات الواجهة الأمامية
npm run build

# أو للتطوير مع المراقبة المباشرة
npm run dev
```

### الخطوة 11: تشغيل الخادم
```bash
# تشغيل خادم Laravel المحلي
php artisan serve

# سيعمل على: http://localhost:8000
```

## البيانات الافتراضية

بعد تشغيل البذور، ستحصل على:

### حساب المدير الافتراضي:
- **البريد الإلكتروني:** <EMAIL>
- **كلمة المرور:** A178a2002
- **النوع:** مدير (Admin)

### الأدوار المتاحة:
- **admin** - مدير النظام
- **employee** - موظف
- **customer** - عميل

## استكشاف الأخطاء

### مشكلة الاتصال بقاعدة البيانات:
```bash
# تأكد من تشغيل MySQL
# تأكد من صحة بيانات الاتصال في ملف .env
# تأكد من وجود قاعدة البيانات

# اختبار الاتصال
php artisan tinker
DB::connection()->getPdo();
```

### مشكلة الصلاحيات:
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 775 storage bootstrap/cache
# في Windows، تأكد من تشغيل Command Prompt كمدير
```

### مشكلة مفتاح التطبيق:
```bash
# إنشاء مفتاح جديد
php artisan key:generate
```

### مشكلة الذاكرة المؤقتة:
```bash
# مسح جميع أنواع الذاكرة المؤقتة
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## ملاحظات مهمة

1. **تأكد من إصدار PHP:** يجب أن يكون 8.1 أو أحدث
2. **إعدادات MySQL:** تأكد من تفعيل `utf8mb4` للدعم الكامل للعربية
3. **الصلاحيات:** تأكد من صلاحيات الكتابة على مجلدات `storage` و `bootstrap/cache`
4. **البيئة:** استخدم `APP_ENV=local` للتطوير و `APP_ENV=production` للإنتاج

## أوامر مفيدة

```bash
# عرض حالة التطبيق
php artisan about

# عرض الطرق المتاحة
php artisan route:list

# عرض الهجرات
php artisan migrate:status

# إنشاء مستخدم جديد (في Tinker)
php artisan tinker
User::create(['first_name' => 'Test', 'last_name' => 'User', 'email' => '<EMAIL>', 'password' => bcrypt('password'), 'user_type' => 'customer']);
```

## الدعم

إذا واجهت أي مشاكل:
1. تحقق من ملفات السجل في `storage/logs/`
2. تأكد من تشغيل جميع الخدمات المطلوبة
3. راجع إعدادات ملف `.env`
4. تأكد من تثبيت جميع التبعيات بشكل صحيح
