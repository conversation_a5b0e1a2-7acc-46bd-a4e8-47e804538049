<header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 z-40 relative">
    <div class="px-6 py-4 flex justify-between items-center">
        <!-- الجانب الأيسر -->
        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- زر فتح/إغلاق القائمة الجانبية للجوال -->
            <button id="sidebarToggle" class="md:hidden p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                <i class="fas fa-bars text-lg"></i>
            </button>

            <!-- عنوان الصفحة -->
            <div class="hidden md:block">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    <?php echo $__env->yieldContent('page-title', 'لوحة التحكم'); ?>
                </h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    مرحباً بك في نظام إدارة المطعم
                </p>
            </div>
        </div>

        <!-- الجانب الأيمن -->
        <div class="flex items-center space-x-3 space-x-reverse">
            <!-- زر البحث -->
            <div class="relative">
                <form action="<?php echo e(route('search')); ?>" method="GET" class="hidden md:flex items-center">
                    <div class="relative">
                        <input type="text" name="query" placeholder="البحث في النظام..."
                               class="w-64 pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                <button id="mobileSearchBtn" class="md:hidden p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- زر الإشعارات -->
            <div class="relative">
                <button id="notificationBtn" class="header-notification-btn relative p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <i class="fas fa-bell text-lg header-bell-icon"></i>
                    <span id="notificationCount" class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full text-xs w-5 h-5 flex items-center justify-center font-medium">0</span>
                </button>
                <!-- قائمة الإشعارات - ستظهر عند النقر -->
                <div id="notificationsMenu" class="absolute left-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-xl py-1 z-50 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                        <a href="<?php echo e(route('admin.notifications')); ?>" class="text-primary text-sm hover:underline">عرض الكل</a>
                    </div>
                    <div id="notificationsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بالإشعارات من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>جاري تحميل الإشعارات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر تبديل الثيم -->
            <button id="darkModeToggle" data-theme-toggle class="theme-toggle p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300" title="تبديل الوضع المظلم">
                <i class="theme-icon fas fa-moon dark:fa-sun text-lg transition-all duration-300"></i>
            </button>

            <!-- فاصل -->
            <div class="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>

            <!-- معلومات المستخدم -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center p-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-r from-primary to-orange-500 flex items-center justify-center text-white font-bold text-lg shadow-md">
                        <?php echo e(Auth::user()->first_name[0] ?? 'أ'); ?>

                    </div>
                    <div class="mr-3 hidden md:block text-right">
                        <div class="font-medium text-gray-900 dark:text-white">
                            <?php echo e(Auth::user()->first_name ?? 'أحمد'); ?> <?php echo e(Auth::user()->last_name ?? 'محمد'); ?>

                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            <?php echo e(Auth::user()->user_type == 'admin' ? 'مدير النظام' : 'موظف'); ?>

                        </div>
                    </div>
                    <i class="fas fa-chevron-down text-sm mr-2 hidden md:block"></i>
                </button>

                <!-- قائمة المستخدم - مخفية افتراضياً -->
                <div id="userMenu" class="absolute left-0 top-full mt-2 w-56 bg-white dark:bg-gray-800 rounded-md shadow-xl py-1 z-50 hidden border border-gray-200 dark:border-gray-700">
                    <a href="<?php echo e(route('admin.profile')); ?>" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                    </a>
                    <a href="<?php echo e(route('admin.settings')); ?>" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-cog ml-2"></i>الإعدادات
                    </a>

                    <?php if(Auth::user()->user_type == 'admin' || (Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin'))): ?>
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <div class="px-3 py-1">
                        <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">التنقل السريع</span>
                    </div>

                    <?php if(Auth::user()->user_type == 'admin'): ?>
                    <a href="<?php echo e(route('employee.dashboard')); ?>" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                        <i class="fas fa-user-tie ml-2 text-blue-500"></i>واجهة الموظف
                        <span class="text-xs text-gray-500 dark:text-gray-400 block">عرض النظام كموظف</span>
                    </a>
                    <?php elseif(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin')): ?>
                    <a href="<?php echo e(route('employee.dashboard')); ?>" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                        <i class="fas fa-arrow-left ml-2 text-green-500"></i>العودة لواجهة الموظف
                        <span class="text-xs text-gray-500 dark:text-gray-400 block">العودة للواجهة الأساسية</span>
                    </a>
                    <?php endif; ?>
                    <?php endif; ?>

                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <a href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                    </a>
                    <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                        <?php echo csrf_field(); ?>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
    /* تحسين القوائم المنسدلة */
    .dropdown-menu {
        max-height: 400px;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* التأكد من أن القوائم تظهر فوق كل شيء */
    #notificationsMenu, #userMenu {
        z-index: 9999 !important;
        position: absolute !important;
        top: 100% !important;
        transform: translateY(0) !important;
        min-width: 200px;
        max-width: 400px;
    }

    /* تحسين الحاوي للقوائم */
    .relative .absolute {
        position: absolute !important;
    }

    /* إصلاح مشكلة الانقطاع */
    header {
        overflow: visible !important;
    }

    header .relative {
        overflow: visible !important;
    }

    /* تحسين الحاوي النسبي للقوائم */
    .relative {
        position: relative !important;
    }
</style><?php /**PATH D:\ccss450\cs450level10\resources\views/includes/header.blade.php ENDPATH**/ ?>