@extends('employee.layouts.app')

@section('title', 'تفاصيل الطلب #' . $order->order_id)

@section('content')
<div id="order-details-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تفاصيل الطلب <span class="text-primary">#{{ $order->order_id }}</span></h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('employee.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <a href="{{ route('employee.orders') }}" class="hover:text-primary">الطلبات</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>تفاصيل الطلب #{{ $order->order_id }}</span>
            </div>
        </div>
        <div class="mt-4 md:mt-0">
            @if($order->status == 'pending')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                    <i class="fas fa-clock ml-1.5"></i>قيد الانتظار
                </span>
            @elseif($order->status == 'preparing')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                    <i class="fas fa-utensils ml-1.5"></i>قيد التحضير
                </span>
            @elseif($order->status == 'completed')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                    <i class="fas fa-check-circle ml-1.5"></i>مكتمل
                </span>
            @elseif($order->status == 'canceled')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                    <i class="fas fa-times-circle ml-1.5"></i>ملغي
                </span>
            @endif
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('success') }}</span>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4 flex justify-between items-center">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-primary ml-2"></i>
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الطلب</h3>
                    </div>
                    <div>
                        <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="inline-flex items-center px-3 py-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors">
                            <i class="fas fa-edit ml-1.5"></i>
                            <span>تعديل الطلب</span>
                        </a>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-user-circle text-primary ml-2"></i>
                                معلومات العميل
                            </h4>
                            <div class="space-y-3 text-gray-600 dark:text-gray-300">
                                <div class="flex">
                                    <span class="font-semibold w-32">الاسم:</span>
                                    @if($order->user)
                                        <span>{{ $order->user->first_name }} {{ $order->user->last_name }}</span>
                                    @else
                                        <span>{{ $order->customer_name ?? 'زبون غير مسجل' }}</span>
                                    @endif
                                </div>
                                @if($order->user)
                                <div class="flex">
                                    <span class="font-semibold w-32">البريد الإلكتروني:</span>
                                    <span class="text-primary">{{ $order->user->email }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">رقم الهاتف:</span>
                                    <span dir="ltr">{{ $order->user->phone }}</span>
                                </div>
                                @else
                                <div class="flex">
                                    <span class="font-semibold w-32">رقم الهاتف:</span>
                                    <span dir="ltr">{{ $order->customer_phone ?? 'غير متوفر' }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">نوع العميل:</span>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">
                                        <i class="fas fa-user-alt ml-1"></i>زبون غير مسجل
                                    </span>
                                </div>
                                @endif
                            </div>
                        </div>
                        <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-clipboard-list text-primary ml-2"></i>
                                معلومات الطلب
                            </h4>
                            <div class="space-y-3 text-gray-600 dark:text-gray-300">
                                <div class="flex">
                                    <span class="font-semibold w-32">رقم الطلب:</span>
                                    <span class="text-primary font-bold">#{{ $order->order_id }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">تاريخ الطلب:</span>
                                    <span>{{ $order->created_at->format('Y-m-d H:i') }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">الطاولة:</span>
                                    <span>{{ $order->table ? 'رقم ' . $order->table->table_number : 'طلب خارجي' }}</span>
                                </div>
                                <div class="flex">
                                    <span class="font-semibold w-32">المبلغ الإجمالي:</span>
                                    <span class="text-primary font-bold">{{ number_format($order->total_amount, 2) }} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="text-lg font-semibold text-gray-800 dark:text-white mt-8 mb-4 flex items-center">
                        <i class="fas fa-shopping-basket text-primary ml-2"></i>
                        عناصر الطلب
                    </h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الصنف</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الكمية</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">السعر</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المجموع</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                @foreach($order->items as $item)
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                        <div class="flex items-center">
                                            <i class="fas fa-utensils text-primary ml-2"></i>
                                            <span>{{ $item->menuItem->name }}</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                        <span class="bg-primary/10 text-primary px-2 py-1 rounded-full font-medium">{{ $item->quantity }}</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                        {{ number_format($item->price, 2) }} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                        <span class="text-primary font-bold">{{ number_format($item->price * $item->quantity, 2) }}</span> <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr class="bg-gray-50 dark:bg-gray-700">
                                    <th colspan="3" class="px-6 py-3 text-right text-sm font-bold text-gray-800 dark:text-white">المجموع الكلي</th>
                                    <th class="px-6 py-3 text-right text-sm font-bold text-primary">{{ number_format($order->total_amount, 2) }} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span></th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                        <i class="fas fa-tasks text-primary ml-2"></i>
                        تغيير حالة الطلب
                    </h3>
                </div>
                <div class="p-6">
                    <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="mb-4">
                            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">حالة الطلب</label>
                            <select name="status" id="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="pending" {{ $order->status == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                                <option value="preparing" {{ $order->status == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                                <option value="completed" {{ $order->status == 'completed' ? 'selected' : '' }}>مكتمل</option>
                                <option value="canceled" {{ $order->status == 'canceled' ? 'selected' : '' }}>ملغي</option>
                            </select>
                        </div>
                        <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                            <i class="fas fa-sync-alt ml-2"></i>
                            <span>تحديث الحالة</span>
                        </button>
                    </form>

                    <div class="mt-6 border-t border-gray-200 dark:border-gray-700 pt-6">
                        <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-4">إجراءات سريعة</h4>
                        <div class="space-y-3">
                            <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                                <i class="fas fa-edit ml-2"></i>
                                <span>تعديل الطلب</span>
                            </a>
                            <a href="{{ route('employee.orders') }}" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                                <i class="fas fa-arrow-right ml-2"></i>
                                <span>العودة للطلبات</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
