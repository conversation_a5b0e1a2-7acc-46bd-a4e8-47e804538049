@extends('customer.layouts.app')

@section('title', 'اتصل بنا - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="text-center mb-12">
        <h1 class="text-4xl font-bold text-darkText dark:text-white mb-4">اتصل بنا</h1>
        <p class="text-gray-600 dark:text-gray-300 text-lg">نحن هنا لخدمتك، لا تتردد في التواصل معنا</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- معلومات الاتصال -->
        <div>
            <h2 class="text-2xl font-bold text-darkText dark:text-white mb-6">معلومات الاتصال</h2>

            <div class="space-y-6">
                <div class="flex items-start space-x-4 space-x-reverse">
                    <div class="bg-primary text-white p-3 rounded-lg">
                        <i class="fas fa-map-marker-alt text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">العنوان</h3>
                        <p class="text-gray-600 dark:text-gray-300">طرابلس، ليبيا<br>شارع الجمهورية، مقابل البنك المركزي</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4 space-x-reverse">
                    <div class="bg-primary text-white p-3 rounded-lg">
                        <i class="fas fa-phone-alt text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">الهاتف</h3>
                        <p class="text-gray-600 dark:text-gray-300">+218 91 234 5678<br>+218 21 123 4567</p>
                    </div>
                </div>

                <div class="flex items-start space-x-4 space-x-reverse">
                    <div class="bg-primary text-white p-3 rounded-lg">
                        <i class="fas fa-envelope text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">البريد الإلكتروني</h3>
                        <p class="text-gray-600 dark:text-gray-300"><EMAIL><br><EMAIL></p>
                    </div>
                </div>

                <div class="flex items-start space-x-4 space-x-reverse">
                    <div class="bg-primary text-white p-3 rounded-lg">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">ساعات العمل</h3>
                        <p class="text-gray-600 dark:text-gray-300">
                            السبت - الخميس: 10:00 ص - 12:00 م<br>
                            الجمعة: 2:00 م - 12:00 م
                        </p>
                    </div>
                </div>
            </div>

            <!-- وسائل التواصل الاجتماعي -->
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-darkText dark:text-white mb-4">تابعنا على</h3>
                <div class="flex space-x-4 space-x-reverse">
                    <a href="#" class="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition">
                        <i class="fab fa-facebook-f text-xl"></i>
                    </a>
                    <a href="#" class="bg-blue-400 text-white p-3 rounded-lg hover:bg-blue-500 transition">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="#" class="bg-pink-600 text-white p-3 rounded-lg hover:bg-pink-700 transition">
                        <i class="fab fa-instagram text-xl"></i>
                    </a>
                    <a href="#" class="bg-green-600 text-white p-3 rounded-lg hover:bg-green-700 transition">
                        <i class="fab fa-whatsapp text-xl"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- نموذج الاتصال -->
        <div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-darkText dark:text-white mb-6">أرسل لنا رسالة</h2>

                <!-- عرض الرسائل -->
                @if(session('success'))
                    <div class="mb-6 bg-green-100 dark:bg-green-900/20 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle ml-2"></i>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                @if(session('error'))
                    <div class="mb-6 bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            {{ session('error') }}
                        </div>
                    </div>
                @endif

                @if($errors->any())
                    <div class="mb-6 bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle ml-2 mt-1"></i>
                            <div>
                                <p class="font-semibold mb-2">يرجى تصحيح الأخطاء التالية:</p>
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                @endif

                <form action="{{ route('contact.store') }}" method="POST" class="space-y-6">
                    @csrf
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الكامل</label>
                        <input type="text" id="name" name="name" value="{{ old('name') }}" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" value="{{ old('email') }}" required
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف (اختياري)</label>
                        <input type="tel" id="phone" name="phone" value="{{ old('phone') }}"
                               placeholder="+218 91 234 5678"
                               class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموضوع</label>
                        <select id="subject" name="subject" required
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <option value="">اختر الموضوع</option>
                            <option value="حجز طاولة" {{ old('subject') == 'حجز طاولة' ? 'selected' : '' }}>حجز طاولة</option>
                            <option value="طلب طعام" {{ old('subject') == 'طلب طعام' ? 'selected' : '' }}>طلب طعام</option>
                            <option value="شكوى" {{ old('subject') == 'شكوى' ? 'selected' : '' }}>شكوى</option>
                            <option value="اقتراح" {{ old('subject') == 'اقتراح' ? 'selected' : '' }}>اقتراح</option>
                            <option value="استفسار عام" {{ old('subject') == 'استفسار عام' ? 'selected' : '' }}>استفسار عام</option>
                            <option value="أخرى" {{ old('subject') == 'أخرى' ? 'selected' : '' }}>أخرى</option>
                        </select>
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الرسالة</label>
                        <textarea id="message" name="message" rows="5" required
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="اكتب رسالتك هنا...">{{ old('message') }}</textarea>
                    </div>

                    <button type="submit"
                            class="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3 px-6 rounded-lg transition">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الرسالة
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- خريطة الموقع -->
    <div class="mt-16">
        <h2 class="text-2xl font-bold text-darkText dark:text-white mb-6 text-center">موقعنا على الخريطة</h2>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <!-- خريطة Google Maps -->
            <div id="map" class="w-full h-96"></div>

            <!-- معلومات إضافية تحت الخريطة -->
            <div class="p-6 bg-gray-50 dark:bg-gray-700">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-primary text-white p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">العنوان</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">شارع الجمهورية، طرابلس، ليبيا</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-primary text-white p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-directions"></i>
                        </div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">الاتجاهات</h3>
                        <a href="https://maps.google.com/maps?q=32.8872,13.1913" target="_blank"
                           class="text-primary hover:underline text-sm">احصل على الاتجاهات</a>
                    </div>
                    <div class="text-center">
                        <div class="bg-primary text-white p-3 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-parking"></i>
                        </div>
                        <h3 class="font-semibold text-darkText dark:text-white mb-1">مواقف السيارات</h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm">متوفرة مجاناً</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
// تحميل الخريطة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إحداثيات طرابلس، ليبيا (يمكنك تغييرها لموقعك الفعلي)
    const lat = 32.8872;
    const lng = 13.1913;

    try {
        // إنشاء الخريطة
        const map = L.map('map').setView([lat, lng], 15);

        // إضافة طبقة الخريطة من OpenStreetMap
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19
        }).addTo(map);

        // إنشاء أيقونة مخصصة للمطعم
        const restaurantIcon = L.divIcon({
            className: 'custom-marker',
            html: `
                <div style="
                    background: #ff6b35;
                    width: 40px;
                    height: 40px;
                    border-radius: 50%;
                    border: 3px solid white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                ">🍽️</div>
            `,
            iconSize: [40, 40],
            iconAnchor: [20, 20]
        });

        // إضافة علامة للموقع
        const marker = L.marker([lat, lng], { icon: restaurantIcon }).addTo(map);

        // إضافة نافذة معلومات
        const popupContent = `
            <div style="text-align: center; font-family: Arial; min-width: 200px;">
                <h3 style="margin: 0 0 10px 0; color: #ff6b35; font-size: 18px;">🍽️ Eat Hub</h3>
                <p style="margin: 5px 0; color: #666; font-size: 14px;">مطعم عائلي راقي</p>
                <p style="margin: 5px 0; color: #666; font-size: 14px;">📍 شارع الجمهورية، طرابلس</p>
                <p style="margin: 5px 0; color: #666; font-size: 14px;">📞 +218 91 234 5678</p>
                <div style="margin-top: 15px;">
                    <a href="https://maps.google.com/maps?q=${lat},${lng}" target="_blank"
                       style="background: #ff6b35; color: white; padding: 8px 15px; text-decoration: none; border-radius: 5px; font-size: 12px; display: inline-block;">
                        احصل على الاتجاهات
                    </a>
                </div>
            </div>
        `;

        marker.bindPopup(popupContent).openPopup();

        // إضافة دائرة لتوضيح منطقة الخدمة
        L.circle([lat, lng], {
            color: '#ff6b35',
            fillColor: '#ff6b35',
            fillOpacity: 0.1,
            radius: 500
        }).addTo(map);

    } catch (error) {
        console.error('خطأ في تحميل الخريطة:', error);
        // في حالة فشل تحميل الخريطة، عرض رسالة بديلة
        document.getElementById('map').innerHTML = `
            <div class="flex items-center justify-center h-full bg-gray-100 dark:bg-gray-700">
                <div class="text-center p-8">
                    <i class="fas fa-map-marked-alt text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">موقع المطعم</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">شارع الجمهورية، طرابلس، ليبيا</p>
                    <div class="space-y-2">
                        <a href="https://maps.google.com/maps?q=${lat},${lng}" target="_blank"
                           class="inline-block bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90 transition">
                            <i class="fas fa-external-link-alt ml-2"></i>
                            عرض في Google Maps
                        </a>
                        <br>
                        <a href="https://www.openstreetmap.org/?mlat=${lat}&mlon=${lng}&zoom=15" target="_blank"
                           class="inline-block bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                            <i class="fas fa-map ml-2"></i>
                            عرض في OpenStreetMap
                        </a>
                    </div>
                </div>
            </div>
        `;
    }
});
</script>

<style>
/* تخصيص شكل الخريطة */
#map {
    border-radius: 8px 8px 0 0;
}

/* تخصيص نافذة المعلومات */
.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.leaflet-popup-tip {
    background: white;
}

/* تحسين شكل الأزرار في الخريطة */
.leaflet-control-zoom a {
    background-color: #ff6b35 !important;
    color: white !important;
    border: none !important;
}

.leaflet-control-zoom a:hover {
    background-color: #e55a2b !important;
}

/* تحسين شكل العلامة المخصصة */
.custom-marker {
    background: transparent !important;
    border: none !important;
}
</style>

@endsection
