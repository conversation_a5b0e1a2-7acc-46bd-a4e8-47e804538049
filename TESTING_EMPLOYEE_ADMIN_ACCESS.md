# اختبار وصول الموظفين للوحة الإدارة

## حسابات الاختبار المتوفرة

تم إنشاء 4 موظفين مع صلاحيات مختلفة للوصول للوحة الإدارة:

### 1. مشرف الوردية
- **الإيميل**: `<EMAIL>`
- **كلمة المرور**: `supervisor123`
- **الصلاحيات**: 
  - إدارة الطلبات (عرض، إضافة، تعديل، تغيير الحالة)
  - إدارة الحجوزات (عرض، إضافة، تعديل، تغيير الحالة)
  - عرض حالة الطاولات وتغييرها
  - عرض وإنشاء المدفوعات
  - عرض تقارير المبيعات

### 2. مدير المخزون
- **الإيميل**: `<EMAIL>`
- **كلمة المرور**: `inventory123`
- **الصلاحيات**:
  - إدارة المخزون الكاملة (عرض، إضافة، تعديل، تصدير)
  - إدارة المكونات (عرض، إضافة، تعديل)
  - عرض وتعديل قائمة الطعام
  - عرض تقارير المخزون

### 3. مدير المبيعات
- **الإيميل**: `<EMAIL>`
- **كلمة المرور**: `sales123`
- **الصلاحيات**:
  - إدارة الطلبات الكاملة
  - إدارة قائمة الطعام الكاملة
  - عرض التقارير المالية وتقارير المبيعات
  - عرض وإضافة المصروفات
  - إدارة الإشعارات

### 4. المدير المساعد
- **الإيميل**: `<EMAIL>`
- **كلمة المرور**: `assistant123`
- **الصلاحيات**: صلاحيات شاملة تقريباً (كل شيء ما عدا إدارة صلاحيات المستخدمين)

## خطوات الاختبار

### الخطوة 1: اختبار تسجيل الدخول كموظف
1. انتقل إلى صفحة تسجيل الدخول
2. استخدم أحد الحسابات أعلاه
3. ستصل إلى لوحة تحكم الموظف

### الخطوة 2: التحقق من ظهور رابط لوحة الإدارة
- في القائمة الجانبية، يجب أن ترى رابط "لوحة الإدارة" باللون البرتقالي
- الرابط يحتوي على أيقونة تاج وأيقونة رابط خارجي

### الخطوة 3: الوصول للوحة الإدارة
1. اضغط على رابط "لوحة الإدارة"
2. ستنتقل إلى `/admin/dashboard`
3. ستشاهد لوحة تحكم المدير لكن مع قائمة جانبية محدودة

### الخطوة 4: اختبار الصلاحيات
- جرب الوصول للأقسام المختلفة
- ستجد أن بعض الروابط مخفية حسب صلاحيات المستخدم
- محاولة الوصول لصفحة غير مصرح بها ستعيد توجيهك مع رسالة خطأ

## أمثلة للاختبار

### اختبار مشرف الوردية:
```
1. سجل دخول بـ <EMAIL>
2. انتقل للوحة الإدارة
3. يجب أن ترى: الطلبات، الحجوزات، التقارير
4. لا يجب أن ترى: إدارة المستخدمين، إدارة الصلاحيات
```

### اختبار مدير المخزون:
```
1. سجل دخول بـ <EMAIL>
2. انتقل للوحة الإدارة
3. يجب أن ترى: المخزون، قائمة الطعام، التقارير
4. لا يجب أن ترى: الطلبات، المصروفات
```

## اختبار الأوامر

### منح صلاحية جديدة لموظف:
```bash
php artisan user:grant-admin-access <EMAIL> --permissions=expenses.view
```

### إزالة صلاحية الوصول للإدارة:
```bash
php artisan user:revoke-admin-access <EMAIL>
```

### التحقق من الصلاحيات:
```bash
php artisan tinker
>>> $user = App\Models\User::where('email', '<EMAIL>')->first();
>>> $user->getAllPermissions()->pluck('name');
```

## المشاكل المحتملة وحلولها

### المشكلة: الموظف لا يرى رابط لوحة الإدارة
**السبب**: لا يملك صلاحية `dashboard.admin`
**الحل**:
```php
$user = User::where('email', '<EMAIL>')->first();
$user->givePermissionTo('dashboard.admin');
```

### المشكلة: خطأ 403 عند محاولة الوصول
**السبب**: مشكلة في الـ middleware أو cache الصلاحيات
**الحل**:
```bash
php artisan permission:cache-reset
php artisan config:clear
```

### المشكلة: الموظف يرى صفحات فارغة
**السبب**: لا يملك صلاحيات كافية لعرض المحتوى
**الحل**: منح صلاحيات إضافية حسب الحاجة

## نصائح للاختبار

1. **اختبر كل نوع موظف** للتأكد من عمل الصلاحيات
2. **جرب الوصول المباشر** للصفحات عبر URL
3. **اختبر الأزرار والروابط** للتأكد من إخفائها حسب الصلاحيات
4. **اختبر التبديل** بين لوحة الموظف ولوحة الإدارة
5. **تأكد من الرسائل** عند منع الوصول

## روابط مفيدة للاختبار

- لوحة تحكم الموظف: `/employee`
- لوحة تحكم المدير: `/admin`
- إدارة الصلاحيات: `/admin/permissions`
- تسجيل الخروج: `/logout`

## مثال كامل للاختبار

```bash
# 1. تشغيل الخادم
php artisan serve

# 2. في المتصفح، انتقل إلى:
http://localhost:8000/login

# 3. سجل دخول بـ:
# الإيميل: <EMAIL>
# كلمة المرور: supervisor123

# 4. ستصل إلى: /employee/dashboard
# 5. اضغط على "لوحة الإدارة" في القائمة الجانبية
# 6. ستنتقل إلى: /admin/dashboard
# 7. جرب الوصول للأقسام المختلفة
```

## النتيجة المتوقعة

- الموظفون المخولون يمكنهم الوصول للوحة الإدارة
- كل موظف يرى فقط الأقسام التي لديه صلاحية للوصول إليها
- الأزرار والروابط تظهر/تختفي حسب الصلاحيات
- رسائل خطأ واضحة عند محاولة الوصول لصفحات غير مصرح بها
- إمكانية التبديل بسهولة بين لوحة الموظف ولوحة الإدارة
