<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('inventory_transactions', function (Blueprint $table) {
            $table->id('transaction_id');
            $table->foreignId('inventory_id')->constrained('inventory')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('transaction_type', ['purchase', 'consumption']);
            $table->decimal('quantity', 10, 2);
            $table->timestamps();
        });

        // استبدال check() بـ:
        DB::statement('ALTER TABLE inventory_transactions ADD CONSTRAINT chk_inv_trans_quantity CHECK (quantity > 0)');
    }

    public function down()
    {
        Schema::dropIfExists('inventory_transactions');
    }
};