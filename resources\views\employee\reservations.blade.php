@extends('employee.layouts.app')

@section('content')
<div id="reservations-page" class="page">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة الحجوزات</h2>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <div class="relative">
                <input type="text" placeholder="بحث..." class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                <button class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            <button class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة حجز</span>
            </button>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="flex border-b border-gray-200 dark:border-gray-700">
            <button class="px-6 py-3 border-b-2 border-primary text-primary font-medium">جميع الحجوزات</button>
            <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">اليوم</button>
            <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">غداً</button>
            <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">هذا الأسبوع</button>
        </div>

        <div class="p-6">
            <div class="mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 md:mb-0">سجل الحجوزات</h3>
                <div class="flex space-x-2 space-x-reverse">
                    <div class="relative">
                        <input type="date" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700">
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                رقم الحجز
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                العميل
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الطاولة
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                تاريخ ووقت الحجز
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                المدة (دقيقة)
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الحالة
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        <!-- محتوى الجدول هنا -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    عرض <span class="font-medium">1</span> إلى <span class="font-medium">5</span> من <span class="font-medium">15</span> حجز
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50">
                        السابق
                    </button>
                    <button class="px-3 py-1 rounded-md bg-primary text-white hover:bg-primary/90">
                        1
                    </button>
                    <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                        2
                    </button>
                    <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                        3
                    </button>
                    <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                        التالي
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection