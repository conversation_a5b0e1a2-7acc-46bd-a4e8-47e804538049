<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Eat Hub - نظام إدارة المطعم للموظفين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',      // بنفسجي عصري
                        secondary: '#10b981',    // أخضر زمردي
                        accent: '#f59e0b',       // برتقالي ذهبي
                        warmBrown: '#C8A080',    // بني دافئ للراحة
                        darkText: '#1f2937',     // رمادي داكن للنصوص
                        glass: 'rgba(255, 255, 255, 0.1)',
                        'gradient-start': '#667eea',
                        'gradient-end': '#764ba2',
                        'neon-blue': '#00d4ff',
                        'neon-purple': '#b794f6',
                        'cyber-green': '#00ff88',
                    },
                    fontFamily: {
                        sans: ['Inter', 'Cairo', 'sans-serif'],
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-in': 'slideIn 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(99, 102, 241, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(99, 102, 241, 0.8)' }
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(-100%)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' }
                        },
                        fadeInUp: {
                            '0%': { transform: 'translateY(30px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    },
                    backdropBlur: {
                        xs: '2px',
                    },
                    boxShadow: {
                        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
                        'neon': '0 0 20px rgba(99, 102, 241, 0.6)',
                        'cyber': '0 0 30px rgba(0, 255, 136, 0.3)',
                    }
                }
            }
        };

        // ضمان ثبات الثيم - حل قوي ومضمون
        (function() {
            // تحديد الثيم الافتراضي
            let savedTheme = localStorage.getItem('theme_preference');

            // إذا لم يكن هناك ثيم محفوظ، استخدم light كافتراضي
            if (!savedTheme) {
                savedTheme = 'light';
                localStorage.setItem('theme_preference', savedTheme);
            }

            // تحديد الثيم الفعال
            let themeToApply = savedTheme;
            if (savedTheme === 'auto') {
                themeToApply = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            }

            // تطبيق الثيم فوراً على HTML
            const html = document.documentElement;
            html.className = ''; // مسح جميع الكلاسات
            html.classList.add(themeToApply);
            html.setAttribute('data-theme', themeToApply);
            html.style.setProperty('--theme-applied', themeToApply);

            // حفظ الثيم في localStorage
            localStorage.setItem('theme_preference', savedTheme);
            localStorage.setItem('effective_theme', themeToApply);
            localStorage.setItem('theme_timestamp', Date.now().toString());

            // إضافة CSS مباشر لضمان التطبيق
            const style = document.createElement('style');
            style.id = 'theme-enforcer';
            style.textContent = `
                html.light { color-scheme: light; }
                html.dark { color-scheme: dark; }
                html:not(.light):not(.dark) { color-scheme: light; }
                body { transition: background-color 0.3s ease, color 0.3s ease; }
            `;
            document.head.appendChild(style);

            // مراقبة مستمرة لضمان عدم تغيير الثيم
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const currentTheme = localStorage.getItem('effective_theme');
                        if (currentTheme && !html.classList.contains(currentTheme)) {
                            html.className = '';
                            html.classList.add(currentTheme);
                        }
                    }
                });
            });

            observer.observe(html, {
                attributes: true,
                attributeFilter: ['class']
            });
        })();
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.css" rel="stylesheet">
    <link href="{{ asset('css/theme.css') }}" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Cairo:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Cairo', sans-serif;
            background: #ffffff;
            min-height: 100vh;
            color: #1f2937;
        }

        /* الوضع المظلم */
        .dark body {
            background: #1f2937;
            color: #f9fafb;
        }

        /* تأثيرات بسيطة للتصميم النظيف */
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* تنسيق السكرولبار البسيط */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* تحريك القائمة الجانبية بتأثير سلس */
        .sidebar-transition {
            transition: all 0.3s ease;
        }

        /* تنسيق الأزرار البسيط */
        .status-btn {
            transition: all 0.3s ease;
        }

        .status-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* خاص بلوحة الموظف - الطاولات العصرية */
        .table-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 1.5rem;
        }

        .restaurant-table {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: 1rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            cursor: pointer;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .restaurant-table::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .restaurant-table:hover::before {
            transform: translateX(100%);
        }

        .restaurant-table:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .table-available {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.3) 100%);
            border: 2px solid #10b981;
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        }

        .table-occupied {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.3) 100%);
            border: 2px solid #ef4444;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
        }

        .table-reserved {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.3) 100%);
            border: 2px solid #f59e0b;
            box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
        }

        .table-number {
            font-size: 2rem;
            font-weight: 800;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            z-index: 2;
        }

        .table-status {
            font-size: 0.75rem;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            margin-top: 0.5rem;
            backdrop-filter: blur(10px);
            z-index: 2;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* تأثيرات الإضاءة للطاولات */
        .table-available:hover {
            box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
        }

        .table-occupied:hover {
            box-shadow: 0 0 30px rgba(239, 68, 68, 0.5);
        }

        .table-reserved:hover {
            box-shadow: 0 0 30px rgba(245, 158, 11, 0.5);
        }

        /* تأثيرات النبض للطاولات النشطة */
        .table-occupied {
            animation: pulse-red 2s infinite;
        }

        @keyframes pulse-red {
            0%, 100% {
                box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
            }
            50% {
                box-shadow: 0 0 30px rgba(239, 68, 68, 0.6);
            }
        }

        /* إصلاح مشكلة العرض */
        .sidebar-transition {
            transition: all 0.3s ease;
        }

        /* التأكد من ظهور المحتوى */
        main {
            display: block !important;
            visibility: visible !important;
        }

        .page {
            display: block !important;
            visibility: visible !important;
        }
    </style>
</head>
<body class="min-h-screen bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100">

    <div class="flex h-screen overflow-hidden">
        <!-- استدعاء القائمة الجانبية -->
        @include('employee.layouts.sidebar')

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- استدعاء الهيدر -->
            @include('employee.layouts.header')

            <!-- المحتوى الرئيسي -->
            <main class="flex-1 overflow-y-auto p-6 bg-gray-50 dark:bg-gray-900">
                <div class="max-w-7xl mx-auto">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- استدعاء ملف السكريبتات -->
    @include('employee.layouts.scripts')
</body>
</html>