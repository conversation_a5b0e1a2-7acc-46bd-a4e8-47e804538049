@extends('auth.layouts.auth')

@section('title', 'إنشاء حساب جديد')

@section('content')
<div id="authContainer" class="w-full max-w-md fade-in">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-primary mb-2">Eat Hub</h1>
            <p class="text-gray-600 dark:text-gray-400">إنشاء حساب جديد</p>
        </div>

        <form action="{{ route('register') }}{{ request()->has('redirect') ? '?redirect='.request()->query('redirect') : '' }}" method="POST">
            @csrf
            @if(request()->has('redirect'))
                <input type="hidden" name="redirect" value="{{ request()->query('redirect') }}">
            @endif
            <div class="grid grid-cols-2 gap-4 mb-6">
                <div>
                    <label for="first_name" class="block text-gray-700 dark:text-gray-300 mb-2">الاسم الأول</label>
                    <input type="text" id="first_name" name="first_name" value="{{ old('first_name') }}" class="w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="الاسم الأول" required>
                    @error('first_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
                <div>
                    <label for="last_name" class="block text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير</label>
                    <input type="text" id="last_name" name="last_name" value="{{ old('last_name') }}" class="w-full px-4 py-3 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="الاسم الأخير" required>
                    @error('last_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mb-6">
                <label for="email" class="block text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" id="email" name="email" value="{{ old('email') }}" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل بريدك الإلكتروني" required>
                </div>
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="phone" class="block text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-phone"></i>
                    </span>
                    <input type="tel" id="phone" name="phone" value="{{ old('phone') }}" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل رقم هاتفك" required>
                </div>
                @error('phone')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="password" class="block text-gray-700 dark:text-gray-300 mb-2">كلمة المرور</label>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" id="password" name="password" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أدخل كلمة المرور" required>
                </div>
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="password_confirmation" class="block text-gray-700 dark:text-gray-300 mb-2">تأكيد كلمة المرور</label>
                <div class="relative">
                    <span class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" id="password_confirmation" name="password_confirmation" class="w-full px-4 py-3 pr-10 border rounded-lg bg-white dark:bg-gray-700 text-gray-800 dark:text-white border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-primary text-base" placeholder="أعد إدخال كلمة المرور" required>
                </div>
            </div>

            <div class="flex items-center mb-6">
                <input type="checkbox" id="terms" name="terms" class="w-5 h-5 text-primary bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-primary" required>
                <label for="terms" class="mr-2 text-gray-700 dark:text-gray-300">
                    أوافق على <a href="#" class="text-primary hover:underline">الشروط والأحكام</a>
                </label>
                @error('terms')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-4 rounded-lg transition duration-200">
                إنشاء حساب
            </button>
        </form>

        <!-- فاصل -->
        <div class="mt-6 mb-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300 dark:border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">أو التسجيل بـ</span>
                </div>
            </div>
        </div>

        <!-- أزرار التسجيل بالحسابات الاجتماعية -->
        <div class="space-y-3">
            <!-- Google -->
            <button onclick="showSocialLoginMessage('Google')" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed transition duration-200" disabled>
                <svg class="w-5 h-5 ml-2" viewBox="0 0 24 24">
                    <path fill="#9CA3AF" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#9CA3AF" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#9CA3AF" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#9CA3AF" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                التسجيل بـ Google (قريباً)
            </button>

            <!-- Facebook -->
            <button onclick="showSocialLoginMessage('Facebook')" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed transition duration-200" disabled>
                <svg class="w-5 h-5 ml-2" fill="#9CA3AF" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                التسجيل بـ Facebook (قريباً)
            </button>

            <!-- Apple -->
            <button onclick="showSocialLoginMessage('Apple')" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed transition duration-200" disabled>
                <svg class="w-5 h-5 ml-2" fill="#9CA3AF" viewBox="0 0 24 24">
                    <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
                التسجيل بـ Apple (قريباً)
            </button>
        </div>

        <div class="mt-6 text-center">
            <p class="text-gray-700 dark:text-gray-300">
                لديك حساب بالفعل؟
                <a href="{{ route('login') }}{{ request()->has('redirect') ? '?redirect='.request()->query('redirect') : '' }}" class="text-primary hover:underline font-medium">تسجيل الدخول</a>
            </p>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // رسالة تسجيل الدخول بالحسابات الاجتماعية
    function showSocialLoginMessage(provider) {
        alert(`التسجيل بـ ${provider} غير متاح حالياً.\n\nيرجى استخدام النموذج أعلاه للتسجيل.\n\nسيتم تفعيل هذه الخدمة قريباً.`);
    }
</script>
@endsection