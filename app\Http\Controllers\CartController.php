<?php

namespace App\Http\Controllers;

use App\Models\Cart;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class CartController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // عرض سلة التسوق
    public function index()
    {
        $cartItems = Cart::with('menuItem')
            ->where('user_id', Auth::id())
            ->get();

        $total = Cart::getCartTotal(Auth::id());
        $itemsCount = Cart::getCartItemsCount(Auth::id());

        return view('customer.cart.index', compact('cartItems', 'total', 'itemsCount'));
    }

    // إضافة عنصر للسلة
    public function add(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'menu_item_id' => 'required|exists:menu_items,item_id',
            'quantity' => 'required|integer|min:1|max:10',
            'special_instructions' => 'nullable|string|max:500'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $menuItem = MenuItem::findOrFail($request->menu_item_id);

            // التحقق من توفر العنصر
            if (!$menuItem->is_available) {
                return response()->json([
                    'success' => false,
                    'message' => 'هذا العنصر غير متوفر حالياً'
                ], 400);
            }

            // البحث عن العنصر في السلة
            $cartItem = Cart::where('user_id', Auth::id())
                ->where('menu_item_id', $request->menu_item_id)
                ->first();

            if ($cartItem) {
                // تحديث الكمية إذا كان العنصر موجود
                $cartItem->update([
                    'quantity' => $cartItem->quantity + $request->quantity,
                    'special_instructions' => $request->special_instructions
                ]);
            } else {
                // إضافة عنصر جديد
                Cart::create([
                    'user_id' => Auth::id(),
                    'menu_item_id' => $request->menu_item_id,
                    'quantity' => $request->quantity,
                    'price' => $menuItem->price,
                    'special_instructions' => $request->special_instructions
                ]);
            }

            $cartCount = Cart::getCartItemsCount(Auth::id());
            $cartTotal = Cart::getCartTotal(Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'تم إضافة العنصر للسلة بنجاح',
                'cart_count' => $cartCount,
                'cart_total' => number_format($cartTotal, 2)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة العنصر للسلة'
            ], 500);
        }
    }

    // تحديث كمية عنصر في السلة
    public function update(Request $request, $cartId)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'كمية غير صحيحة'
            ], 422);
        }

        try {
            $cartItem = Cart::where('cart_id', $cartId)
                ->where('user_id', Auth::id())
                ->firstOrFail();

            $cartItem->update(['quantity' => $request->quantity]);

            $cartTotal = Cart::getCartTotal(Auth::id());
            $itemSubtotal = $cartItem->subtotal;

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الكمية بنجاح',
                'item_subtotal' => number_format($itemSubtotal, 2),
                'cart_total' => number_format($cartTotal, 2)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الكمية'
            ], 500);
        }
    }

    // حذف عنصر من السلة
    public function remove($cartId)
    {
        try {
            $cartItem = Cart::where('cart_id', $cartId)
                ->where('user_id', Auth::id())
                ->firstOrFail();

            $cartItem->delete();

            $cartCount = Cart::getCartItemsCount(Auth::id());
            $cartTotal = Cart::getCartTotal(Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'تم حذف العنصر من السلة',
                'cart_count' => $cartCount,
                'cart_total' => number_format($cartTotal, 2)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف العنصر'
            ], 500);
        }
    }

    // مسح السلة بالكامل
    public function clear()
    {
        try {
            Cart::clearCart(Auth::id());

            return response()->json([
                'success' => true,
                'message' => 'تم مسح السلة بالكامل'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء مسح السلة'
            ], 500);
        }
    }

    // الحصول على عدد عناصر السلة (للـ AJAX)
    public function getCartCount()
    {
        $count = Cart::getCartItemsCount(Auth::id());

        return response()->json([
            'count' => $count
        ]);
    }

    // الانتقال لصفحة الدفع
    public function checkout(Request $request)
    {
        $cartItems = Cart::with('menuItem')
            ->where('user_id', Auth::id())
            ->get();

        if ($cartItems->isEmpty()) {
            return redirect()->route('customer.cart')->with('error', 'السلة فارغة');
        }

        $total = Cart::getCartTotal(Auth::id());
        $tax = $total * 0.15; // ضريبة 15%

        // رسوم التوصيل حسب نوع الطلب
        $orderType = $request->get('order_type', 'delivery'); // افتراضي: توصيل
        $delivery = ($orderType === 'pickup') ? 0.00 : 10.00;

        $finalTotal = $total + $tax + $delivery;

        return view('customer.cart.checkout', compact('cartItems', 'total', 'tax', 'delivery', 'finalTotal', 'orderType'));
    }
}
