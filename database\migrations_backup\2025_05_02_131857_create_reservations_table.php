<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up()
    {
        Schema::create('reservations', function (Blueprint $table) {
            $table->id('reservation_id');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('table_id')->constrained('tables')->onDelete('cascade');
            $table->dateTime('reservation_time');
            $table->integer('duration');
            $table->enum('status', ['confirmed', 'canceled', 'completed'])->default('confirmed');
            $table->timestamps();
        });

        // نقل شرط CHECK إلى بعد إنشاء الجدول مباشرة
        DB::statement('ALTER TABLE reservations ADD CONSTRAINT reservations_duration_check CHECK (duration > 0)');
    }

    public function down()
    {
        Schema::dropIfExists('reservations');
    }
};