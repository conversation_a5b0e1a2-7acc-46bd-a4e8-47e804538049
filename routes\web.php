<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\EmployeeController;
use App\Http\Controllers\PublicSearchController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\ReservationController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\ReviewController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\OfferController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\ThemeController;

// مسارات الثيم (متاحة للجميع)
Route::post('/theme/toggle', [ThemeController::class, 'toggle'])->name('theme.toggle');
Route::get('/theme/current', [ThemeController::class, 'current'])->name('theme.current');

// الصفحة الرئيسية للموقع
Route::get('/', [CustomerController::class, 'index'])->name('home');
Route::get('/welcome', [CustomerController::class, 'index'])->name('welcome');
Route::get('/customer', [CustomerController::class, 'index'])->name('customer.index');

// مسار البحث العام
Route::get('/search', [App\Http\Controllers\SearchController::class, 'search'])->name('search');

// مسار قائمة الطعام العامة
Route::get('/menu', [MenuController::class, 'publicIndex'])->name('menu');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');

// مسار صفحة الاتصال
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// مسارات العروض العامة (لا تتطلب تسجيل دخول)
Route::prefix('offers')->group(function () {
    Route::get('/', [OfferController::class, 'index'])->name('customer.offers.index');
    Route::get('/{slug}', [OfferController::class, 'show'])->name('customer.offers.show');
});

// مسارات API للإشعارات
Route::prefix('api/notifications')->middleware('auth')->group(function () {
    Route::get('/count', [App\Http\Controllers\NotificationController::class, 'getUnreadCount']);
    Route::get('/latest', [App\Http\Controllers\NotificationController::class, 'getLatestNotifications']);
});

// مسارات إضافية للإشعارات (للموظفين)
Route::middleware(['auth'])->group(function () {
    Route::get('/employee/api/notifications/count', [App\Http\Controllers\NotificationController::class, 'getUnreadCount']);
    Route::get('/employee/api/notifications/latest', [App\Http\Controllers\NotificationController::class, 'getLatestNotifications']);
});

// مسارات الاختبار
Route::get('/test-login', [App\Http\Controllers\TestController::class, 'testLogin']);
Route::get('/test-users', [App\Http\Controllers\TestController::class, 'listUsers']);
Route::get('/create-admin', [App\Http\Controllers\TestController::class, 'createAdmin']);
Route::get('/login-as-admin', [App\Http\Controllers\TestController::class, 'loginAsAdmin']);

// مسارات المصادقة
Route::controller(AuthController::class)->group(function () {
    // Login
    Route::get('/login', 'showLoginForm')->name('login');
    Route::post('/login', 'login');

    // Registration
    Route::get('/register', 'showRegistrationForm')->name('register');
    Route::post('/register', 'register');

    // Password Reset
    Route::get('/forgot-password', 'showForgotPasswordForm')->name('password.request');
    Route::post('/forgot-password', 'sendResetLinkEmail')->name('password.email');
    Route::get('/reset-password/{token}', 'showResetForm')->name('password.reset');
    Route::post('/reset-password', 'resetPassword')->name('password.update');

    // Password Change
    Route::get('/change-password', 'showChangePasswordForm')->name('password.change')->middleware('auth');
    Route::post('/change-password', 'changePassword')->middleware('auth');

    // Logout
    Route::post('/logout', 'logout')->name('logout')->middleware('auth');
});

// مسارات تسجيل الدخول بالحسابات الاجتماعية
Route::prefix('auth')->group(function () {
    // Google
    Route::get('/google', [\App\Http\Controllers\Auth\SocialAuthController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('/google/callback', [\App\Http\Controllers\Auth\SocialAuthController::class, 'handleGoogleCallback'])->name('auth.google.callback');

    // Facebook
    Route::get('/facebook', [\App\Http\Controllers\Auth\SocialAuthController::class, 'redirectToFacebook'])->name('auth.facebook');
    Route::get('/facebook/callback', [\App\Http\Controllers\Auth\SocialAuthController::class, 'handleFacebookCallback'])->name('auth.facebook.callback');

    // Apple
    Route::get('/apple', [\App\Http\Controllers\Auth\SocialAuthController::class, 'redirectToApple'])->name('auth.apple');
    Route::get('/apple/callback', [\App\Http\Controllers\Auth\SocialAuthController::class, 'handleAppleCallback'])->name('auth.apple.callback');
});

// مسارات المدير
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    // Dashboard
    Route::get('/', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    Route::get('/dashboard-new', function() {
        // نفس البيانات من الكونترولر الأصلي
        $totalOrders = \App\Models\Order::count();
        $totalCustomers = \App\Models\User::where('user_type', 'customer')->count();
        $totalRevenue = \App\Models\Payment::sum('amount');
        $averageRating = \Illuminate\Support\Facades\DB::table('reviews')->avg('rating') ?? 4.8;

        // بيانات وهمية للاختبار
        $revenuePercentChange = 12.5;
        $ordersPercentChange = 8.3;
        $salesData = [
            ['amount' => 1200], ['amount' => 1500], ['amount' => 1100],
            ['amount' => 1800], ['amount' => 1600], ['amount' => 2000], ['amount' => 1900]
        ];
        $recentOrders = \App\Models\Order::with('user')->latest()->take(5)->get();
        $topMenuItems = \App\Models\MenuItem::withCount('orderItems')->orderBy('order_items_count', 'desc')->take(5)->get();

        return view('admin.dashboard-new', compact(
            'totalOrders', 'totalCustomers', 'totalRevenue', 'averageRating',
            'revenuePercentChange', 'ordersPercentChange', 'salesData', 'recentOrders', 'topMenuItems'
        ));
    })->name('admin.dashboard.new');

    // صفحة الطلبات الجديدة للاختبار
    Route::get('/orders-new', function() {
        $orders = \App\Models\Order::with(['user', 'items.menuItem'])->latest()->paginate(15);
        $orderStats = [
            'total' => \App\Models\Order::count(),
            'pending' => \App\Models\Order::where('status', 'pending')->count(),
            'preparing' => \App\Models\Order::where('status', 'preparing')->count(),
            'completed' => \App\Models\Order::where('status', 'completed')->count(),
        ];

        return view('admin.orders.index-new', compact('orders', 'orderStats'));
    })->name('admin.orders.new');

    // الإشعارات
    Route::get('/notifications', [NotificationController::class, 'adminIndex'])->name('admin.notifications');
    Route::get('/notifications/create', [NotificationController::class, 'create'])->name('admin.notifications.create');
    Route::post('/notifications/send', [NotificationController::class, 'send'])->name('admin.notifications.send');
    Route::get('/notifications/{id}', [NotificationController::class, 'show'])->name('admin.notifications.show');
    Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('admin.notifications.mark-as-read');
    Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('admin.notifications.mark-all-as-read');
    Route::delete('/notifications/{id}', [NotificationController::class, 'delete'])->name('admin.notifications.delete');
    Route::delete('/notifications', [NotificationController::class, 'deleteAll'])->name('admin.notifications.delete-all');

    // إدارة المستخدمين (مؤقتاً بدون صلاحيات للاختبار)
    Route::get('/users', [AdminController::class, 'users'])->name('admin.users');
    Route::get('/users/create', [AdminController::class, 'createUser'])->name('admin.users.create');
    Route::post('/users/store', [AdminController::class, 'storeUser'])->name('admin.users.store');
    Route::get('/users/{id}', [AdminController::class, 'showUser'])->name('admin.users.show');
    Route::get('/users/{id}/edit', [AdminController::class, 'editUser'])->name('admin.users.edit');
    Route::put('/users/{id}', [AdminController::class, 'updateUser'])->name('admin.users.update');
    Route::delete('/users/{id}', [AdminController::class, 'deleteUser'])->name('admin.users.delete');

    // إدارة المصروفات (استخدام ExpenseController الموجود)
    Route::get('/expenses', [ExpenseController::class, 'index'])->name('admin.expenses');
    Route::get('/expenses/create', [ExpenseController::class, 'create'])->name('admin.expenses.create');
    Route::post('/expenses/store', [ExpenseController::class, 'store'])->name('admin.expenses.store');
    Route::get('/expenses/{id}', [ExpenseController::class, 'show'])->name('admin.expenses.show');
    Route::get('/expenses/{id}/edit', [ExpenseController::class, 'edit'])->name('admin.expenses.edit');
    Route::put('/expenses/{id}', [ExpenseController::class, 'update'])->name('admin.expenses.update');
    Route::delete('/expenses/{id}', [ExpenseController::class, 'delete'])->name('admin.expenses.delete');
    Route::get('/expenses/export', [ExpenseController::class, 'export'])->name('admin.expenses.export');
    Route::get('/expenses/stats', [ExpenseController::class, 'stats'])->name('admin.expenses.stats');

    // إدارة القائمة (مع صلاحيات مفصلة)
    Route::middleware('permission:menu.view')->group(function () {
        Route::get('/menu', [MenuController::class, 'adminIndex'])->name('admin.menu');

        Route::middleware('permission:menu.view.details')->group(function () {
            Route::get('/menu/{id}/show', [MenuController::class, 'adminShow'])->name('admin.menu.show');
        });

        Route::middleware('permission:menu.create')->group(function () {
            Route::get('/menu/create', [MenuController::class, 'create'])->name('admin.menu.create');
            Route::post('/menu/store', [MenuController::class, 'store'])->name('admin.menu.store');
        });

        Route::middleware('permission:menu.edit')->group(function () {
            Route::get('/menu/{id}/edit', [MenuController::class, 'edit'])->name('admin.menu.edit');
            Route::put('/menu/{id}', [MenuController::class, 'update'])->name('admin.menu.update');
        });

        Route::middleware('permission:menu.delete')->group(function () {
            Route::delete('/menu/{id}', [MenuController::class, 'delete'])->name('admin.menu.delete');
        });
    });

    // الإعدادات والملف الشخصي
    Route::get('/settings', [AdminController::class, 'settings'])->name('admin.settings');
    Route::post('/settings', [AdminController::class, 'updateSettings'])->name('admin.settings.update');
    Route::get('/profile', [AdminController::class, 'profile'])->name('admin.profile');
    Route::put('/profile/update', [AdminController::class, 'updateProfile'])->name('admin.profile.update');
    Route::delete('/profile/delete', [AdminController::class, 'deleteProfile'])->name('admin.profile.delete');

    // إدارة المخزون
    Route::get('/inventory', [InventoryController::class, 'index'])->name('admin.inventory');
    Route::get('/inventory/create', [InventoryController::class, 'create'])->name('admin.inventory.create');
    Route::post('/inventory/store', [InventoryController::class, 'store'])->name('admin.inventory.store');
    Route::get('/inventory/{id}/edit', [InventoryController::class, 'edit'])->name('admin.inventory.edit');
    Route::put('/inventory/{id}', [InventoryController::class, 'update'])->name('admin.inventory.update');
    Route::delete('/inventory/{id}', [InventoryController::class, 'delete'])->name('admin.inventory.delete');
    Route::get('/inventory/transactions', [InventoryController::class, 'transactions'])->name('admin.inventory.transactions');
    Route::get('/inventory/low-stock', [InventoryController::class, 'lowStock'])->name('admin.inventory.low-stock');
    Route::get('/inventory/export', [InventoryController::class, 'export'])->name('admin.inventory.export');

    // إدارة المكونات
    Route::get('/ingredients/create', [InventoryController::class, 'createIngredient'])->name('admin.ingredients.create');
    Route::post('/ingredients/store', [InventoryController::class, 'storeIngredient'])->name('admin.ingredients.store');
    Route::get('/ingredients/{id}/edit', [InventoryController::class, 'editIngredient'])->name('admin.ingredients.edit');
    Route::put('/ingredients/{id}', [InventoryController::class, 'updateIngredient'])->name('admin.ingredients.update');
    Route::delete('/ingredients/{id}', [InventoryController::class, 'deleteIngredient'])->name('admin.ingredients.delete');

    // إدارة الطلبات
    Route::get('/orders', [OrderController::class, 'index'])->name('admin.orders');
    Route::get('/orders/create', [OrderController::class, 'create'])->name('admin.orders.create');
    Route::post('/orders/store', [OrderController::class, 'store'])->name('admin.orders.store');
    Route::get('/orders/{id}', [OrderController::class, 'show'])->name('admin.orders.show');
    Route::get('/orders/{id}/edit', [OrderController::class, 'edit'])->name('admin.orders.edit');
    Route::put('/orders/{id}', [OrderController::class, 'update'])->name('admin.orders.update');
    Route::put('/orders/{id}/status', [OrderController::class, 'updateStatus'])->name('admin.orders.update-status');

    // إدارة المصروفات
    Route::get('/expenses', [ExpenseController::class, 'index'])->name('admin.expenses');
    Route::get('/expenses/create', [ExpenseController::class, 'create'])->name('admin.expenses.create');
    Route::post('/expenses/store', [ExpenseController::class, 'store'])->name('admin.expenses.store');
    Route::get('/expenses/{id}/edit', [ExpenseController::class, 'edit'])->name('admin.expenses.edit');
    Route::put('/expenses/{id}', [ExpenseController::class, 'update'])->name('admin.expenses.update');
    Route::delete('/expenses/{id}', [ExpenseController::class, 'delete'])->name('admin.expenses.delete');
    Route::get('/expenses/monthly-report', [ExpenseController::class, 'monthlyReport'])->name('admin.expenses.monthly-report');
    Route::get('/expenses/compare-report', [ExpenseController::class, 'compareReport'])->name('admin.expenses.compare-report');



    // التقارير (بدون صلاحيات مفصلة - الأدمن يرى كل شيء)
    Route::get('/reports', [ReportController::class, 'index'])->name('admin.reports');
    Route::get('/reports/financial', [ReportController::class, 'financial'])->name('admin.reports.financial');
    Route::get('/reports/profit-loss', [ReportController::class, 'profitLossReport'])->name('admin.reports.profit-loss');
    Route::get('/reports/sales', [ReportController::class, 'salesReport'])->name('admin.reports.sales');
    Route::get('/reports/performance', [ReportController::class, 'performanceReport'])->name('admin.reports.performance');
    Route::get('/reports/inventory', [ReportController::class, 'inventoryReport'])->name('admin.reports.inventory');
    Route::get('/reports/customers', [ReportController::class, 'customersReport'])->name('admin.reports.customers');
    Route::get('/reports/customer-reviews', [ReportController::class, 'customerReviewsReport'])->name('admin.reports.customer-reviews');
    Route::get('/reports/employee-performance', [ReportController::class, 'employeePerformanceReport'])->name('admin.reports.employee-performance');
    Route::get('/reports/expenses', [ReportController::class, 'expensesReport'])->name('admin.reports.expenses');
    Route::get('/reports/orders', [ReportController::class, 'ordersReport'])->name('admin.reports.orders');
    Route::get('/reports/profitability', [ReportController::class, 'profitabilityReport'])->name('admin.reports.profitability');
    Route::get('/reports/period-comparison', [ReportController::class, 'periodComparisonReport'])->name('admin.reports.period-comparison');

    // راوت لجلب بيانات المخططات
    Route::get('/reports/financial-chart-data', [ReportController::class, 'getFinancialChartData'])->name('admin.reports.financial-chart-data');
    Route::get('/dashboard-chart-data', [AdminController::class, 'getDashboardChartData'])->name('admin.dashboard-chart-data');

    // راوتس التصدير الشاملة
    Route::prefix('reports/export')->group(function () {
        Route::get('/financial', [ReportController::class, 'exportFinancialReport'])->name('admin.reports.export.financial');
        Route::get('/employee-performance', [ReportController::class, 'exportEmployeePerformance'])->name('admin.reports.export.employee-performance');
        Route::get('/sales', [ReportController::class, 'exportSalesReport'])->name('admin.reports.export.sales');
        Route::get('/inventory', [ReportController::class, 'exportInventoryReport'])->name('admin.reports.export.inventory');
        Route::get('/customers', [ReportController::class, 'exportCustomersReport'])->name('admin.reports.export.customers');
        Route::get('/master', [ReportController::class, 'exportMasterReport'])->name('admin.reports.export.master');
    });

    // إدارة الصلاحيات
    Route::prefix('permissions')->group(function () {
        Route::get('/', [App\Http\Controllers\PermissionController::class, 'index'])->name('admin.permissions.index');
        Route::get('/user/{user}/edit', [App\Http\Controllers\PermissionController::class, 'editUser'])->name('admin.permissions.edit-user');
        Route::put('/user/{user}', [App\Http\Controllers\PermissionController::class, 'updateUser'])->name('admin.permissions.update-user');
        Route::post('/copy', [App\Http\Controllers\PermissionController::class, 'copyPermissions'])->name('admin.permissions.copy');

        Route::get('/roles', [App\Http\Controllers\PermissionController::class, 'roles'])->name('admin.permissions.roles');
        Route::post('/roles', [App\Http\Controllers\PermissionController::class, 'createRole'])->name('admin.permissions.create-role');
        Route::put('/roles/{role}', [App\Http\Controllers\PermissionController::class, 'updateRole'])->name('admin.permissions.update-role');
        Route::delete('/roles/{role}', [App\Http\Controllers\PermissionController::class, 'deleteRole'])->name('admin.permissions.delete-role');
    });
});

// مسارات الموظف
Route::prefix('employee')->middleware(['auth', 'employee'])->group(function () {
    // لوحة التحكم
    Route::get('/', [EmployeeController::class, 'dashboard'])->name('employee.dashboard');

    // إدارة الطلبات - استخدام نفس كونترولرات الأدمن
    Route::get('/orders', [OrderController::class, 'index'])->name('employee.orders');
    Route::get('/orders/create', [OrderController::class, 'create'])->name('employee.orders.create');
    Route::post('/orders/store', [OrderController::class, 'store'])->name('employee.orders.store');
    Route::get('/orders/{id}', [OrderController::class, 'show'])->name('employee.orders.show');
    Route::get('/orders/{id}/edit', [OrderController::class, 'edit'])->name('employee.orders.edit');
    Route::put('/orders/{id}', [OrderController::class, 'update'])->name('employee.orders.update');
    Route::put('/orders/{id}/status', [OrderController::class, 'updateStatus'])->name('employee.orders.update-status');

    // سلة التسوق
    Route::post('/cart/add', [OrderController::class, 'addToCart'])->name('employee.cart.add');

    // إدارة الحجوزات - استخدام نفس كونترولرات الأدمن
    Route::get('/reservations', [ReservationController::class, 'index'])->name('employee.reservations');
    Route::get('/reservations/create', [ReservationController::class, 'create'])->name('employee.reservations.create');
    Route::post('/reservations/store', [ReservationController::class, 'store'])->name('employee.reservations.store');
    Route::get('/reservations/{id}', [ReservationController::class, 'show'])->name('employee.reservations.show');
    Route::get('/reservations/{id}/edit', [ReservationController::class, 'edit'])->name('employee.reservations.edit');
    Route::put('/reservations/{id}', [ReservationController::class, 'update'])->name('employee.reservations.update');
    Route::delete('/reservations/{id}', [ReservationController::class, 'delete'])->name('employee.reservations.delete');
    Route::put('/reservations/{id}/status', [ReservationController::class, 'updateStatus'])->name('employee.reservations.update-status');

    // إدارة المخزون - استخدام نفس كونترولرات الأدمن
    Route::get('/inventory', [InventoryController::class, 'index'])->name('employee.inventory');
    Route::get('/inventory/low-stock', [InventoryController::class, 'lowStock'])->name('employee.inventory.low-stock');

    // قائمة الطعام
    Route::get('/menu', [EmployeeController::class, 'menu'])->name('employee.menu');
    Route::get('/menu/{id}', [MenuController::class, 'show'])->name('employee.menu.show');

    // إدارة الإشعارات
    Route::get('/notifications', [EmployeeController::class, 'notifications'])->name('employee.notifications');
    Route::get('/notifications/{id}', [EmployeeController::class, 'showNotification'])->name('employee.notifications.show');
    Route::post('/notifications/{id}/mark-read', [EmployeeController::class, 'markNotificationAsRead'])->name('employee.notifications.mark-read');
    Route::post('/notifications/mark-all-read', [EmployeeController::class, 'markAllNotificationsAsRead'])->name('employee.notifications.mark-all-read');
    Route::delete('/notifications/{id}', [EmployeeController::class, 'deleteNotification'])->name('employee.notifications.delete');

    // إدارة الطاولات
    Route::get('/tables', [EmployeeController::class, 'tables'])->name('employee.tables');
    Route::get('/tables/create', [EmployeeController::class, 'createTable'])->name('employee.tables.create');
    Route::post('/tables', [EmployeeController::class, 'storeTable'])->name('employee.tables.store');
    Route::get('/tables/view-order', [EmployeeController::class, 'viewTableOrder'])->name('employee.tables.view-order');
    Route::get('/tables/free', [EmployeeController::class, 'freeTable'])->name('employee.tables.free');
    Route::get('/tables/cancel-reservation', [EmployeeController::class, 'cancelTableReservation'])->name('employee.tables.cancel-reservation');
    Route::get('/tables/{id}', [EmployeeController::class, 'showTable'])->name('employee.tables.show');
    Route::get('/tables/{id}/edit', [EmployeeController::class, 'editTable'])->name('employee.tables.edit');
    Route::put('/tables/{id}', [EmployeeController::class, 'updateTable'])->name('employee.tables.update');
    Route::delete('/tables/{id}', [EmployeeController::class, 'destroyTable'])->name('employee.tables.destroy');
    Route::put('/tables/{id}/status', [EmployeeController::class, 'updateTableStatus'])->name('employee.tables.update-status');

    // المدفوعات
    Route::get('/payments', [PaymentController::class, 'employeeIndex'])->name('employee.payments');
    Route::get('/payments/create/{order_id}', [PaymentController::class, 'create'])->name('employee.payments.create');
    Route::post('/payments/store', [PaymentController::class, 'store'])->name('employee.payments.store');

    // الملف الشخصي
    Route::get('/profile', [EmployeeController::class, 'profile'])->name('employee.profile');
    Route::put('/profile/update', [EmployeeController::class, 'updateProfile'])->name('employee.profile.update');

    // التقارير
    Route::get('/reports', [EmployeeController::class, 'reports'])->name('employee.reports');
    Route::get('/reports/daily', [EmployeeController::class, 'dailyReport'])->name('employee.reports.daily');
    Route::get('/reports/weekly', [EmployeeController::class, 'weeklyReport'])->name('employee.reports.weekly');
    Route::get('/reports/monthly', [EmployeeController::class, 'monthlyReport'])->name('employee.reports.monthly');

    // العملاء
    Route::get('/customers', [EmployeeController::class, 'customers'])->name('employee.customers');
    Route::get('/customers/{id}', [EmployeeController::class, 'showCustomer'])->name('employee.customers.show');

    // الإعدادات
    Route::get('/settings', [EmployeeController::class, 'settings'])->name('employee.settings');
    Route::post('/settings/update', [EmployeeController::class, 'updateSettings'])->name('employee.settings.update');

    // المساعدة
    Route::get('/help', [EmployeeController::class, 'help'])->name('employee.help');
});

// مسارات العميل
Route::prefix('customer')->middleware(['auth'])->group(function () {
    Route::get('/', [CustomerController::class, 'dashboard'])->name('customer.dashboard');
    Route::get('/index', [CustomerController::class, 'index'])->name('customer.index');

    // قائمة الطعام
    Route::get('/menu', [MenuController::class, 'customerIndex'])->name('customer.menu');
    Route::get('/menu/{id}', [MenuController::class, 'customerShow'])->name('customer.menu.show');

    // الطلبات
    Route::get('/orders', [CustomerController::class, 'orders'])->name('customer.orders');
    Route::get('/orders/index', [OrderController::class, 'customerIndex'])->name('customer.orders.index');
    Route::post('/orders/store', [OrderController::class, 'customerStore'])->name('customer.orders.store');
    Route::get('/orders/{id}', [OrderController::class, 'customerShow'])->name('customer.orders.show');
    Route::get('/orders/{id}/cancel', [OrderController::class, 'customerCancelPage'])->name('customer.orders.cancel.page');
    Route::delete('/orders/{id}/cancel', [OrderController::class, 'customerCancel'])->name('customer.orders.cancel');
    Route::post('/orders/{id}/reorder', [OrderController::class, 'customerReorder'])->name('customer.orders.reorder');
    Route::get('/orders/{id}/invoice', [OrderController::class, 'customerInvoice'])->name('customer.orders.invoice');

    // الحجوزات
    Route::get('/reservations', [CustomerController::class, 'reservations'])->name('customer.reservations');
    Route::get('/reservations/create', [ReservationController::class, 'customerCreate'])->name('customer.reservations.create');
    Route::post('/reservations/check-availability', [ReservationController::class, 'checkAvailability'])->name('customer.reservations.check-availability');
    Route::post('/reservations/store', [ReservationController::class, 'customerStore'])->name('customer.reservations.store');
    Route::get('/reservations/{id}', [ReservationController::class, 'customerShow'])->name('customer.reservations.show');
    Route::get('/reservations/{id}/edit', [ReservationController::class, 'customerEdit'])->name('customer.reservations.edit');
    Route::get('/reservations/{id}/delete', [ReservationController::class, 'customerDelete'])->name('customer.reservations.delete');
    Route::put('/reservations/{id}', [ReservationController::class, 'customerUpdate'])->name('customer.reservations.update');
    Route::delete('/reservations/{id}', [ReservationController::class, 'customerCancel'])->name('customer.reservations.cancel');

    // التقييمات
    Route::get('/reviews', [ReviewController::class, 'customerIndex'])->name('customer.reviews');
    Route::post('/reviews/store', [ReviewController::class, 'customerStore'])->name('customer.reviews.store');

    // الإشعارات
    Route::get('/notifications', [NotificationController::class, 'customerIndex'])->name('customer.notifications');
    Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('customer.notifications.read');
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('customer.notifications.read-all');
    Route::delete('/notifications/{id}', [NotificationController::class, 'delete'])->name('customer.notifications.delete');

    // الملف الشخصي
    Route::get('/profile', [CustomerController::class, 'profile'])->name('customer.profile');
    Route::put('/profile/update', [CustomerController::class, 'updateProfile'])->name('customer.profile.update');

    // لوحة التحكم
    Route::get('/dashboard', [CustomerController::class, 'dashboard'])->name('customer.dashboard');

    // سلة التسوق
    Route::prefix('cart')->group(function () {
        Route::get('/', [App\Http\Controllers\CartController::class, 'index'])->name('customer.cart');
        Route::post('/add', [App\Http\Controllers\CartController::class, 'add'])->name('customer.cart.add');
        Route::put('/update/{cartId}', [App\Http\Controllers\CartController::class, 'update'])->name('customer.cart.update');
        Route::delete('/remove/{cartId}', [App\Http\Controllers\CartController::class, 'remove'])->name('customer.cart.remove');
        Route::delete('/clear', [App\Http\Controllers\CartController::class, 'clear'])->name('customer.cart.clear');
        Route::get('/count', [App\Http\Controllers\CartController::class, 'getCartCount'])->name('customer.cart.count');
        Route::get('/checkout', [App\Http\Controllers\CartController::class, 'checkout'])->name('customer.cart.checkout');
    });


});

// روت تجريبي لاختبار صفحة الملف الشخصي
Route::get('/test-profile', function () {
    return view('customer.test-profile');
})->name('test.profile');

// روت تجريبي لصفحة تسجيل الدخول
Route::get('/test-login', function () {
    return view('test-login');
})->name('test.login');

// مسار اختبار نظام التقييم
Route::get('/test-rating', function () {
    return view('test-rating');
});

// مسار تسجيل دخول تلقائي للاختبار
Route::get('/test-customer-login', function () {
    $user = App\Models\User::where('user_type', 'customer')->first();
    if (!$user) {
        // إنشاء مستخدم تجريبي
        $user = App\Models\User::create([
            'first_name' => 'عميل',
            'last_name' => 'تجريبي',
            'email' => '<EMAIL>',
            'password' => bcrypt('123456'),
            'phone' => '0912345678',
            'user_type' => 'customer'
        ]);
    }
    Auth::login($user);
    return redirect('/customer/reservations/create');
});

// اختبار route للطاولات
Route::get('/test-tables', function () {
    $tables = App\Models\Table::all();
    return response()->json([
        'success' => true,
        'tables' => $tables,
        'count' => $tables->count()
    ]);
});

// اختبار route للتحقق من توفر الطاولات
Route::post('/test-check-availability', function (Request $request) {
    return response()->json([
        'success' => true,
        'message' => 'تم العثور على طاولات متاحة',
        'available_tables' => [
            [
                'table_id' => 1,
                'table_number' => '1',
                'capacity' => 4,
                'location' => 'منطقة النافذة'
            ],
            [
                'table_id' => 2,
                'table_number' => '2',
                'capacity' => 2,
                'location' => 'منطقة هادئة'
            ]
        ]
    ]);
});

// البحث العام
Route::get('/search', [PublicSearchController::class, 'searchResults'])->name('search');
Route::post('/search/quick', [PublicSearchController::class, 'quickSearch'])->name('search.quick');
Route::get('/search/suggestions', [PublicSearchController::class, 'suggestions'])->name('search.suggestions');

// API الإشعارات (تتطلب تسجيل دخول)
Route::middleware('auth')->group(function () {
    Route::get('/customer/notifications/count', [NotificationController::class, 'getUnreadCount'])->name('customer.notifications.count');
    Route::get('/customer/notifications/latest', [NotificationController::class, 'getLatestNotifications'])->name('customer.notifications.latest');
});

// روت تجريبي للحجوزات
Route::get('/test-reservation', function () {
    return view('test-reservation');
})->name('test.reservation');
Route::post('/test-reservation/check-availability', [ReservationController::class, 'checkAvailability'])->name('test.reservation.check-availability');
Route::post('/test-reservation/store', function (Request $request) {
    // إنشاء حجز تجريبي بـ user_id = 1
    $validator = Validator::make($request->all(), [
        'reservation_date' => 'required|date|after_or_equal:today',
        'reservation_time' => 'required',
        'party_size' => 'required|integer|min:1|max:10',
        'selected_table' => 'required|exists:tables,table_id',
        'special_requests' => 'nullable|string|max:500',
    ]);

    if ($validator->fails()) {
        return redirect()->back()->withErrors($validator)->withInput();
    }

    $reservationDateTime = $request->reservation_date . ' ' . $request->reservation_time;

    try {
        $reservation = App\Models\Reservation::create([
            'user_id' => 1, // مستخدم تجريبي
            'table_id' => $request->selected_table,
            'reservation_time' => $reservationDateTime,
            'duration' => 120,
            'party_size' => $request->party_size,
            'special_requests' => $request->special_requests,
            'status' => 'pending',
        ]);

        return redirect()->back()->with('success', 'تم إنشاء الحجز بنجاح! رقم الحجز: ' . $reservation->reservation_id);
    } catch (\Exception $e) {
        return redirect()->back()->with('error', 'حدث خطأ: ' . $e->getMessage())->withInput();
    }
})->name('test.reservation.store');

// إنشاء مدير تجريبي مع جميع الصلاحيات
Route::get('/create-admin', function () {
    try {
        // إنشاء مدير رئيسي
        $admin = App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'أحمد',
                'last_name' => 'المدير',
                'phone' => '+218912345000',
                'address' => 'طرابلس، ليبيا',
                'password' => bcrypt('admin123'),
                'user_type' => 'admin',
                'is_active' => true
            ]
        );

        // التأكد من وجود دور المدير
        $adminRole = Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);

        // إعطاء دور المدير للمستخدم
        if (!$admin->hasRole('admin')) {
            $admin->assignRole('admin');
        }

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء المدير بنجاح!',
            'admin' => [
                'email' => $admin->email,
                'password' => 'admin123',
                'name' => $admin->first_name . ' ' . $admin->last_name,
                'roles' => $admin->getRoleNames(),
                'permissions_count' => $admin->getAllPermissions()->count()
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->name('create.admin');

// روت تجريبي لتسجيل الدخول كمدير لرؤية الإشعارات
Route::get('/test-admin-login', function () {
    // البحث عن مدير موجود أو إنشاء واحد
    $admin = App\Models\User::where('email', '<EMAIL>')->first();

    if (!$admin) {
        $admin = App\Models\User::create([
            'first_name' => 'مدير',
            'last_name' => 'تجريبي',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'admin',
            'phone' => '+218 91 111 1111',
            'is_active' => true
        ]);

        // إعطاء دور المدير
        $adminRole = Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin', 'guard_name' => 'web']);
        $admin->assignRole('admin');
    }

    // تسجيل الدخول التلقائي
    Auth::login($admin);

    return redirect()->route('admin.dashboard')->with('success', 'تم تسجيل الدخول كمدير بنجاح');
})->name('test.admin.login');

// مسار مؤقت لإنشاء المستخدمين بدون صلاحيات (للاختبار فقط)
Route::get('/admin/users/create-temp', [AdminController::class, 'createUser'])->name('admin.users.create.temp');
Route::post('/admin/users/store-temp', [AdminController::class, 'storeUser'])->name('admin.users.store.temp');

// مسار لتسجيل الدخول التلقائي والانتقال لإنشاء المستخدمين
Route::get('/quick-login-and-create-user', function () {
    // البحث عن المدير
    $admin = App\Models\User::where('email', '<EMAIL>')->first();

    if (!$admin) {
        return response()->json(['error' => 'المدير غير موجود'], 404);
    }

    // تسجيل الدخول التلقائي
    Auth::login($admin);

    // إعادة التوجيه لصفحة إنشاء المستخدمين
    return redirect()->route('admin.users.create')->with('success', 'تم تسجيل الدخول بنجاح');
})->name('quick.login.create.user');

// صفحة تشخيص المشاكل
Route::get('/debug-permissions', function () {
    $html = '<html><head><meta charset="utf-8"><title>تشخيص الصلاحيات</title></head><body style="font-family: Arial; direction: rtl; padding: 20px;">';
    $html .= '<h1>🔍 تشخيص مشاكل الصلاحيات</h1>';

    // التحقق من تسجيل الدخول
    if (Auth::check()) {
        $user = Auth::user();
        $html .= '<div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        $html .= '<h2>✅ المستخدم مسجل دخول</h2>';
        $html .= '<p><strong>الاسم:</strong> ' . $user->first_name . ' ' . $user->last_name . '</p>';
        $html .= '<p><strong>الإيميل:</strong> ' . $user->email . '</p>';
        $html .= '<p><strong>النوع:</strong> ' . $user->user_type . '</p>';
        $html .= '<p><strong>نشط:</strong> ' . ($user->is_active ? 'نعم' : 'لا') . '</p>';

        // الأدوار
        $roles = $user->getRoleNames();
        $html .= '<p><strong>الأدوار:</strong> ' . ($roles->count() > 0 ? $roles->implode(', ') : 'لا توجد أدوار') . '</p>';

        // الصلاحيات
        $permissions = $user->getAllPermissions();
        $html .= '<p><strong>عدد الصلاحيات:</strong> ' . $permissions->count() . '</p>';

        // اختبار صلاحيات محددة
        $testPermissions = [
            'users.view' => 'عرض المستخدمين',
            'users.create' => 'إنشاء المستخدمين',
            'dashboard.admin' => 'لوحة تحكم المدير'
        ];

        $html .= '<h3>اختبار الصلاحيات:</h3><ul>';
        foreach ($testPermissions as $permission => $description) {
            $hasPermission = $user->can($permission);
            $status = $hasPermission ? '✅' : '❌';
            $html .= '<li>' . $status . ' ' . $description . ' (' . $permission . ')</li>';
        }
        $html .= '</ul>';

        $html .= '</div>';

        // روابط سريعة
        $html .= '<div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        $html .= '<h3>🔗 روابط سريعة:</h3>';
        $html .= '<p><a href="' . route('admin.dashboard') . '" style="color: #007bff;">لوحة التحكم</a></p>';
        $html .= '<p><a href="' . route('admin.users') . '" style="color: #007bff;">قائمة المستخدمين</a></p>';
        $html .= '<p><a href="' . route('admin.users.create') . '" style="color: #007bff;">إنشاء مستخدم جديد</a></p>';
        $html .= '<p><a href="' . route('admin.users.create.temp') . '" style="color: #28a745;">إنشاء مستخدم (مؤقت)</a></p>';
        $html .= '</div>';

    } else {
        $html .= '<div style="background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        $html .= '<h2>❌ المستخدم غير مسجل دخول</h2>';
        $html .= '<p><a href="' . route('test.admin.login') . '" style="color: #dc3545;">تسجيل دخول تلقائي</a></p>';
        $html .= '</div>';
    }

    $html .= '</body></html>';
    return $html;
})->name('debug.permissions');

// إعطاء صلاحيات التقارير المالية للأدمن
Route::get('/give-financial-permissions', function () {
    try {
        $admin = App\Models\User::where('user_type', 'admin')->first();

        if (!$admin) {
            return 'لا يوجد مدير في النظام';
        }

        return 'تم إعطاء صلاحيات التقارير للمدير: ' . $admin->first_name . ' ' . $admin->last_name . ' - الأدمن لديه صلاحيات كاملة';
    } catch (\Exception $e) {
        return 'خطأ: ' . $e->getMessage();
    }
});

// إنشاء موظف بصلاحيات إدارية للاختبار
Route::get('/create-admin-employee', function () {
    try {
        // إنشاء موظف بصلاحيات إدارية
        $employee = App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'سارة',
                'last_name' => 'المشرفة',
                'phone' => '+218912345200',
                'address' => 'طرابلس، ليبيا',
                'password' => bcrypt('employee123'),
                'user_type' => 'employee',
                'is_active' => true
            ]
        );

        // إعطاء صلاحيات إدارية محددة
        $permissions = [
            'dashboard.admin', // الوصول للوحة الإدارة
            'orders.view', 'orders.view.details', 'orders.view.all', 'orders.create', 'orders.edit', 'orders.status',
            'menu.view', 'menu.view.details',
            'reports.view', 'reports.sales', // تقارير المبيعات فقط
            'tables.view', 'tables.view.details', 'tables.status'
        ];

        $employee->givePermissionTo($permissions);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الموظف بصلاحيات إدارية بنجاح!',
            'employee' => [
                'email' => $employee->email,
                'password' => 'employee123',
                'name' => $employee->first_name . ' ' . $employee->last_name,
                'user_type' => $employee->user_type,
                'permissions_count' => $employee->getAllPermissions()->count(),
                'can_access_admin' => $employee->can('dashboard.admin')
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->name('create.admin.employee');

// روت تجريبي لتسجيل الدخول كموظف لرؤية الإشعارات
Route::get('/test-employee-login', function () {
    // البحث عن موظف موجود أو إنشاء واحد
    $employee = App\Models\User::where('user_type', 'employee')->first();

    if (!$employee) {
        $employee = App\Models\User::create([
            'first_name' => 'موظف',
            'last_name' => 'تجريبي',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'employee',
            'phone' => '+218 91 222 2222',
            'is_active' => true
        ]);
    }

    // تسجيل الدخول التلقائي
    Auth::login($employee);

    return redirect()->route('employee.notifications')->with('success', 'تم تسجيل الدخول كموظف بنجاح');
})->name('test.employee.login');

// صفحة اختبار نظام الاتصال
Route::get('/test-contact-system', function () {
    return view('test-contact-system');
})->name('test.contact.system');

// إنشاء بيانات اختبار للطلبات
Route::get('/create-test-orders', function () {
    try {
        // البحث عن مستخدم للطلب أو إنشاء واحد
        $user = App\Models\User::where('user_type', 'customer')->first();
        if (!$user) {
            $user = App\Models\User::create([
                'first_name' => 'أحمد',
                'last_name' => 'محمد',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'user_type' => 'customer',
                'phone' => '+218 91 234 5678',
                'is_active' => true
            ]);
        }

        // البحث عن عناصر القائمة أو إنشاء بعضها
        $menuItems = App\Models\MenuItem::take(3)->get();
        if ($menuItems->count() == 0) {
            $menuItems = collect([
                App\Models\MenuItem::create([
                    'name' => 'بيتزا مارجريتا',
                    'description' => 'بيتزا كلاسيكية بالطماطم والجبن',
                    'price' => 45.00,
                    'category' => 'البيتزا',
                    'is_available' => true,
                    'preparation_time' => 20
                ]),
                App\Models\MenuItem::create([
                    'name' => 'برجر لحم',
                    'description' => 'برجر لحم طازج مع الخضار',
                    'price' => 35.00,
                    'category' => 'البرجر',
                    'is_available' => true,
                    'preparation_time' => 15
                ])
            ]);
        }

        // إنشاء طلب مكتمل
        $completedOrder = App\Models\Order::create([
            'user_id' => $user->user_id,
            'customer_name' => $user->first_name . ' ' . $user->last_name,
            'customer_phone' => $user->phone,
            'total_amount' => 105.00,
            'status' => 'completed',
            'order_type' => 'delivery',
            'delivery_address' => 'طرابلس، شارع الجمهورية',
            'payment_method' => 'card',
            'notes' => 'طلب تجريبي مكتمل'
        ]);

        // إضافة عناصر للطلب المكتمل
        foreach ($menuItems as $index => $item) {
            App\Models\OrderItem::create([
                'order_id' => $completedOrder->order_id,
                'menu_item_id' => $item->item_id,
                'quantity' => $index + 1,
                'price' => $item->price
            ]);
        }

        // إنشاء طلب قيد التحضير
        $pendingOrder = App\Models\Order::create([
            'user_id' => $user->user_id,
            'customer_name' => $user->first_name . ' ' . $user->last_name,
            'customer_phone' => $user->phone,
            'total_amount' => 80.00,
            'status' => 'pending',
            'order_type' => 'pickup',
            'payment_method' => 'cash',
            'notes' => 'طلب تجريبي قيد التحضير'
        ]);

        App\Models\OrderItem::create([
            'order_id' => $pendingOrder->order_id,
            'menu_item_id' => $menuItems->first()->item_id,
            'quantity' => 2,
            'price' => $menuItems->first()->price
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء البيانات التجريبية بنجاح',
            'user' => [
                'id' => $user->user_id,
                'email' => $user->email,
                'password' => 'password'
            ],
            'orders' => [
                'completed' => $completedOrder->order_id,
                'pending' => $pendingOrder->order_id
            ]
        ]);

    } catch (Exception $e) {
        return response()->json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
})->name('create.test.orders');

// تسجيل دخول تلقائي للعميل التجريبي
Route::get('/test-customer-login', function () {
    $user = App\Models\User::where('email', '<EMAIL>')->first();

    if (!$user) {
        return redirect('/create-test-orders')->with('error', 'يجب إنشاء البيانات التجريبية أولاً');
    }

    Auth::login($user);

    return redirect()->route('customer.orders')->with('success', 'تم تسجيل الدخول بنجاح');
})->name('test.customer.login');




