<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;

class CreateSuperAdmin extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create-super 
                            {email : البريد الإلكتروني للمدير}
                            {password : كلمة المرور}
                            {--first-name= : الاسم الأول}
                            {--last-name= : الاسم الأخير}
                            {--phone= : رقم الهاتف}
                            {--address= : العنوان}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'إنشاء مدير جديد مع صلاحيات كاملة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $password = $this->argument('password');

        // التحقق من وجود المستخدم
        if (User::where('email', $email)->exists()) {
            $this->error("المستخدم موجود بالفعل: {$email}");
            return;
        }

        // جمع البيانات
        $firstName = $this->option('first-name') ?: $this->ask('الاسم الأول');
        $lastName = $this->option('last-name') ?: $this->ask('الاسم الأخير');
        $phone = $this->option('phone') ?: $this->ask('رقم الهاتف (اختياري)', null);
        $address = $this->option('address') ?: $this->ask('العنوان (اختياري)', null);

        // إنشاء المدير
        $admin = User::create([
            'email' => $email,
            'password' => Hash::make($password),
            'first_name' => $firstName,
            'last_name' => $lastName,
            'phone' => $phone,
            'address' => $address,
            'user_type' => 'admin',
            'is_active' => true
        ]);

        // منح جميع الصلاحيات
        $this->grantAllPermissions($admin);

        $this->info('تم إنشاء المدير بنجاح!');
        $this->info("الاسم: {$firstName} {$lastName}");
        $this->info("البريد الإلكتروني: {$email}");
        $this->info("تم منح جميع الصلاحيات المتاحة");

        // عرض رابط تسجيل الدخول
        $loginUrl = route('login');
        $this->info("رابط تسجيل الدخول: {$loginUrl}");
    }

    /**
     * منح جميع الصلاحيات المتاحة للمستخدم
     */
    private function grantAllPermissions(User $user)
    {
        // الحصول على جميع الصلاحيات المتاحة
        $allPermissions = Permission::all();

        if ($allPermissions->isNotEmpty()) {
            // منح جميع الصلاحيات
            $user->givePermissionTo($allPermissions);
            $this->info("تم منح {$allPermissions->count()} صلاحية");
        } else {
            $this->warn('لا توجد صلاحيات في النظام. تأكد من تشغيل: php artisan db:seed --class=PermissionSeeder');
        }

        // مسح cache الصلاحيات
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
}
