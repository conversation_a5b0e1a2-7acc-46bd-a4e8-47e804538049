@extends('employee.layouts.app')

@section('title', 'تعديل الطاولة #' . $table->table_number)

@section('content')
<div id="edit-table-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تعديل الطاولة <span class="text-primary">#{{ $table->table_number }}</span></h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('employee.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <a href="{{ route('employee.tables') }}" class="hover:text-primary">حالة الطاولات</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>تعديل الطاولة #{{ $table->table_number }}</span>
            </div>
        </div>
        <div class="mt-4 md:mt-0">
            @if($table->status == 'available')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                    <i class="fas fa-check-circle ml-1.5"></i>متاح
                </span>
            @elseif($table->status == 'occupied')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                    <i class="fas fa-times-circle ml-1.5"></i>مشغول
                </span>
            @elseif($table->status == 'reserved')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                    <i class="fas fa-clock ml-1.5"></i>محجوز
                </span>
            @endif
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                <i class="fas fa-edit text-primary ml-2"></i>
                تعديل معلومات الطاولة
            </h3>
        </div>
        <div class="p-6">
            <form action="{{ route('employee.tables.update', $table->table_id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label for="table_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الطاولة <span class="text-red-500">*</span></label>
                        <input type="number" name="table_number" id="table_number" value="{{ old('table_number', $table->table_number) }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('table_number') border-red-500 @enderror" required>
                        @error('table_number')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="capacity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">السعة (عدد الأشخاص) <span class="text-red-500">*</span></label>
                        <input type="number" name="capacity" id="capacity" value="{{ old('capacity', $table->capacity) }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('capacity') border-red-500 @enderror" required>
                        @error('capacity')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="location" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الموقع <span class="text-red-500">*</span></label>
                        <select name="location" id="location" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('location') border-red-500 @enderror" required>
                            <option value="">اختر الموقع</option>
                            <option value="داخلي" {{ old('location', $table->location) == 'داخلي' ? 'selected' : '' }}>داخلي</option>
                            <option value="خارجي" {{ old('location', $table->location) == 'خارجي' ? 'selected' : '' }}>خارجي</option>
                            <option value="شرفة" {{ old('location', $table->location) == 'شرفة' ? 'selected' : '' }}>شرفة</option>
                            <option value="طابق علوي" {{ old('location', $table->location) == 'طابق علوي' ? 'selected' : '' }}>طابق علوي</option>
                        </select>
                        @error('location')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة <span class="text-red-500">*</span></label>
                        <select name="status" id="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('status') border-red-500 @enderror" required>
                            <option value="available" {{ old('status', $table->status) == 'available' ? 'selected' : '' }}>متاح</option>
                            <option value="occupied" {{ old('status', $table->status) == 'occupied' ? 'selected' : '' }}>مشغول</option>
                            <option value="reserved" {{ old('status', $table->status) == 'reserved' ? 'selected' : '' }}>محجوز</option>
                        </select>
                        @error('status')
                            <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <a href="{{ route('employee.tables') }}" class="px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors">
                        إلغاء
                    </a>
                    <button type="submit" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors">
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
