/**
 * نظام تقييم النجوم التفاعلي
 */

class StarRating {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            maxStars: options.maxStars || 5,
            initialRating: options.initialRating || 0,
            readonly: options.readonly || false,
            size: options.size || 'medium', // small, medium, large
            onRate: options.onRate || null,
            allowHalf: options.allowHalf || false
        };
        
        this.currentRating = this.options.initialRating;
        this.hoveredRating = 0;
        
        this.init();
    }
    
    init() {
        if (!this.container) return;
        
        this.container.classList.add('star-rating');
        if (this.options.size !== 'medium') {
            this.container.classList.add(this.options.size);
        }
        
        this.createStars();
        this.updateDisplay();
        
        if (!this.options.readonly) {
            this.bindEvents();
        }
    }
    
    createStars() {
        this.container.innerHTML = '';
        this.stars = [];
        
        for (let i = 1; i <= this.options.maxStars; i++) {
            const star = document.createElement('i');
            star.className = 'star far fa-star';
            star.dataset.rating = i;
            this.container.appendChild(star);
            this.stars.push(star);
        }
    }
    
    bindEvents() {
        this.stars.forEach(star => {
            star.addEventListener('click', (e) => this.handleClick(e));
            star.addEventListener('mouseenter', (e) => this.handleMouseEnter(e));
            star.addEventListener('mouseleave', () => this.handleMouseLeave());
        });
        
        this.container.addEventListener('mouseleave', () => this.resetHover());
    }
    
    handleClick(e) {
        if (this.options.readonly) return;
        
        const rating = parseInt(e.target.dataset.rating);
        this.setRating(rating);
        
        // إضافة تأثير النبض
        e.target.classList.add('pulse');
        setTimeout(() => e.target.classList.remove('pulse'), 500);
        
        if (this.options.onRate) {
            this.options.onRate(rating);
        }
    }
    
    handleMouseEnter(e) {
        if (this.options.readonly) return;
        
        const rating = parseInt(e.target.dataset.rating);
        this.hoveredRating = rating;
        this.updateHoverDisplay();
    }
    
    handleMouseLeave() {
        if (this.options.readonly) return;
        
        this.updateHoverDisplay();
    }
    
    resetHover() {
        if (this.options.readonly) return;
        
        this.hoveredRating = 0;
        this.updateDisplay();
    }
    
    updateHoverDisplay() {
        const displayRating = this.hoveredRating || this.currentRating;
        this.updateStarsDisplay(displayRating);
    }
    
    updateDisplay() {
        this.updateStarsDisplay(this.currentRating);
    }
    
    updateStarsDisplay(rating) {
        this.stars.forEach((star, index) => {
            const starValue = index + 1;
            
            if (starValue <= rating) {
                star.className = 'star fas fa-star filled';
            } else if (this.options.allowHalf && starValue - 0.5 <= rating) {
                star.className = 'star fas fa-star-half-alt filled';
            } else {
                star.className = 'star far fa-star';
            }
        });
    }
    
    setRating(rating) {
        this.currentRating = Math.max(0, Math.min(rating, this.options.maxStars));
        this.updateDisplay();
    }
    
    getRating() {
        return this.currentRating;
    }
    
    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
            this.container.classList.remove('star-rating', this.options.size);
        }
    }
}

/**
 * إنشاء نظام تقييم بسيط
 */
function createStarRating(selector, options = {}) {
    const containers = document.querySelectorAll(selector);
    const ratings = [];
    
    containers.forEach(container => {
        const rating = new StarRating(container, options);
        ratings.push(rating);
    });
    
    return ratings.length === 1 ? ratings[0] : ratings;
}

/**
 * عرض تقييم للقراءة فقط
 */
function displayStarRating(container, rating, maxStars = 5) {
    const element = typeof container === 'string' ? document.querySelector(container) : container;
    if (!element) return;
    
    element.classList.add('star-display');
    element.innerHTML = '';
    
    for (let i = 1; i <= maxStars; i++) {
        const star = document.createElement('i');
        
        if (i <= rating) {
            star.className = 'star fas fa-star';
        } else if (i - 0.5 <= rating) {
            star.className = 'star fas fa-star-half-alt';
        } else {
            star.className = 'star far fa-star empty';
        }
        
        element.appendChild(star);
    }
}

/**
 * تهيئة جميع أنظمة التقييم في الصفحة
 */
function initializeRatings() {
    // تهيئة أنظمة التقييم التفاعلية
    document.querySelectorAll('.rating-input').forEach(container => {
        const maxStars = parseInt(container.dataset.maxStars) || 5;
        const initialRating = parseInt(container.dataset.initialRating) || 0;
        const readonly = container.dataset.readonly === 'true';
        
        new StarRating(container, {
            maxStars,
            initialRating,
            readonly,
            onRate: (rating) => {
                // حفظ التقييم في input مخفي إذا وجد
                const hiddenInput = container.parentElement.querySelector('input[type="hidden"]');
                if (hiddenInput) {
                    hiddenInput.value = rating;
                }
                
                // إرسال حدث مخصص
                container.dispatchEvent(new CustomEvent('ratingChanged', {
                    detail: { rating }
                }));
            }
        });
    });
    
    // تهيئة عرض التقييمات
    document.querySelectorAll('.rating-display').forEach(container => {
        const rating = parseFloat(container.dataset.rating) || 0;
        const maxStars = parseInt(container.dataset.maxStars) || 5;
        displayStarRating(container, rating, maxStars);
    });
}

// تهيئة تلقائية عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', initializeRatings);

// تصدير للاستخدام العام
window.StarRating = StarRating;
window.createStarRating = createStarRating;
window.displayStarRating = displayStarRating;
window.initializeRatings = initializeRatings;
