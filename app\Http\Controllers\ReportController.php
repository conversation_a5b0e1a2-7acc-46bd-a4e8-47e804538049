<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Expense;
use App\Models\Ingredient;
use App\Models\Inventory;
use App\Models\Payment;
use App\Models\User;
use App\Models\Table;
use App\Models\Reservation;
use App\Models\Review;
use App\Models\MenuItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Routing\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\Response;
use Barryvdh\DomPDF\Facade\Pdf;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // إزالة middleware admin لأن routes تحتوي على middleware permission
    }

    // الصفحة الرئيسية للتقارير
    public function index()
    {
        return view('admin.reports.index');
    }

    // التقرير المالي المحسن والدقيق
    public function financial(Request $request)
    {
        // تحديد الفترة الزمنية (افتراضياً الشهر الحالي)
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        // تحويل التواريخ إلى Carbon instances
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // حساب إجمالي المبيعات الفعلية (من المدفوعات المكتملة فقط)
        $totalSales = Payment::whereHas('order', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->whereIn('status', ['completed', 'preparing']); // الطلبات المؤكدة فقط
        })->sum('amount');

        // حساب إجمالي المصروفات الفعلية
        $totalExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->sum('amount');

        // حساب تكلفة المواد الخام للمنتجات المباعة (COGS - Cost of Goods Sold)
        $costOfGoodsSold = $this->calculateCostOfGoodsSold($startDate, $endDate);

        // حساب إجمالي الربح (المبيعات - تكلفة المواد)
        $grossProfit = $totalSales - $costOfGoodsSold;

        // حساب صافي الربح (إجمالي الربح - المصروفات التشغيلية)
        $netProfit = $grossProfit - $totalExpenses;

        // حساب هامش الربح الإجمالي
        $grossProfitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;

        // حساب هامش الربح الصافي
        $netProfitMargin = $totalSales > 0 ? ($netProfit / $totalSales) * 100 : 0;

        // حساب متوسط قيمة الطلب للطلبات المكتملة فقط
        $completedOrders = Order::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', ['completed', 'preparing'])
            ->count();
        $averageOrderValue = $completedOrders > 0 ? $totalSales / $completedOrders : 0;

        // بيانات المبيعات والمصروفات والأرباح لآخر 12 شهر
        $monthlyData = [];
        for ($i = 11; $i >= 0; $i--) {
            $monthStart = Carbon::now()->subMonths($i)->startOfMonth();
            $monthEnd = Carbon::now()->subMonths($i)->endOfMonth();

            $monthlySales = Payment::whereHas('order', function($query) use ($monthStart, $monthEnd) {
                $query->whereBetween('created_at', [$monthStart, $monthEnd])
                      ->whereIn('status', ['completed', 'preparing']);
            })->sum('amount');

            $monthlyExpenses = Expense::whereBetween('expense_date', [$monthStart, $monthEnd])
                ->sum('amount');

            $monthlyCOGS = $this->calculateCostOfGoodsSold($monthStart, $monthEnd);
            $monthlyGrossProfit = $monthlySales - $monthlyCOGS;
            $monthlyNetProfit = $monthlyGrossProfit - $monthlyExpenses;

            $monthlyData[] = [
                'month' => $monthStart->format('M'),
                'sales' => $monthlySales,
                'expenses' => $monthlyExpenses,
                'cogs' => $monthlyCOGS,
                'gross_profit' => $monthlyGrossProfit,
                'net_profit' => $monthlyNetProfit
            ];
        }

        // توزيع المصروفات حسب الفئة مع ترجمة الفئات
        $expensesByCategory = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->select('category', DB::raw('SUM(amount) as total'))
            ->groupBy('category')
            ->get()
            ->map(function($expense) {
                $categoryNames = [
                    'ingredients' => 'المكونات',
                    'utilities' => 'المرافق',
                    'salaries' => 'الرواتب',
                    'maintenance' => 'الصيانة',
                    'other' => 'أخرى'
                ];
                $expense->category_name = $categoryNames[$expense->category] ?? $expense->category;
                return $expense;
            });

        // أفضل المنتجات مبيعاً مع حساب الربحية
        $topProducts = OrderItem::whereHas('order', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->whereIn('status', ['completed', 'preparing']);
        })
        ->select('menu_item_id',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('SUM(price * quantity) as total_revenue'),
                DB::raw('AVG(price) as avg_price'))
        ->with('menuItem')
        ->groupBy('menu_item_id')
        ->orderBy('total_revenue', 'desc')
        ->limit(10)
        ->get()
        ->map(function($product) use ($startDate, $endDate) {
            // حساب تكلفة المنتج
            $product->cost_per_unit = $this->calculateProductCost($product->menu_item_id);
            $product->total_cost = $product->total_quantity * $product->cost_per_unit;
            $product->profit = $product->total_revenue - $product->total_cost;
            $product->profit_margin = $product->total_revenue > 0 ?
                ($product->profit / $product->total_revenue) * 100 : 0;
            return $product;
        });

        // أداء الموظفين (المستخدمين الذين أدخلوا الطلبات)
        $employeePerformance = Order::whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('user_id')
            ->whereIn('status', ['completed', 'preparing'])
            ->select('user_id',
                    DB::raw('COUNT(*) as orders_count'),
                    DB::raw('SUM(total_amount) as total_sales'),
                    DB::raw('AVG(total_amount) as avg_order_value'))
            ->with('user')
            ->groupBy('user_id')
            ->orderBy('total_sales', 'desc')
            ->limit(10)
            ->get();

        // إحصائيات شاملة ومفصلة
        $stats = [
            'total_orders' => Order::whereBetween('created_at', [$startDate, $endDate])->count(),
            'completed_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')->count(),
            'preparing_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'preparing')->count(),
            'pending_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'pending')->count(),
            'cancelled_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'canceled')->count(),
            'unique_customers' => Order::whereBetween('created_at', [$startDate, $endDate])
                ->whereNotNull('user_id')
                ->distinct('user_id')->count('user_id'),
            'guest_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
                ->whereNull('user_id')->count(),
        ];

        return view('admin.reports.financial', compact(
            'totalSales', 'totalExpenses', 'costOfGoodsSold', 'grossProfit', 'netProfit',
            'grossProfitMargin', 'netProfitMargin', 'averageOrderValue',
            'monthlyData', 'expensesByCategory', 'topProducts', 'employeePerformance',
            'stats', 'startDate', 'endDate'
        ));
    }

    /**
     * حساب تكلفة المواد الخام للمنتجات المباعة (COGS)
     */
    private function calculateCostOfGoodsSold($startDate, $endDate)
    {
        $totalCOGS = 0;

        // جلب جميع المنتجات المباعة في الفترة المحددة
        $soldItems = OrderItem::whereHas('order', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->whereIn('status', ['completed', 'preparing']);
        })->get();

        foreach ($soldItems as $item) {
            $productCost = $this->calculateProductCost($item->menu_item_id);
            $totalCOGS += $productCost * $item->quantity;
        }

        return $totalCOGS;
    }

    /**
     * حساب تكلفة منتج واحد بناءً على المكونات
     */
    private function calculateProductCost($menuItemId)
    {
        $totalCost = 0;

        // جلب جميع المكونات المطلوبة للمنتج من جدول الوصفات
        $recipes = DB::table('recipes')
            ->join('inventory', 'recipes.ingredient_id', '=', 'inventory.ingredient_id')
            ->where('recipes.menu_item_id', $menuItemId)
            ->select('recipes.quantity', 'inventory.cost_per_unit')
            ->get();

        foreach ($recipes as $recipe) {
            $totalCost += $recipe->quantity * $recipe->cost_per_unit;
        }

        return $totalCost;
    }

    // تقرير المبيعات
    public function salesReport(Request $request)
    {
        $startDate = $request->start_date ? $request->start_date : now()->subDays(30)->format('Y-m-d');
        $endDate = $request->end_date ? $request->end_date : now()->format('Y-m-d');

        // بيانات المبيعات اليومية
        $dailySales = Order::where('status', 'completed')
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(total_amount) as total'))
            ->orderBy('date')
            ->get();

        // المبيعات حسب الفئة
        $salesByCategory = DB::table('orders')
            ->join('order_items', 'orders.order_id', '=', 'order_items.order_id')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.item_id')
            ->where('orders.status', 'completed')
            ->whereBetween(DB::raw('DATE(orders.created_at)'), [$startDate, $endDate])
            ->groupBy('menu_items.category')
            ->select('menu_items.category', DB::raw('SUM(order_items.price * order_items.quantity) as total'))
            ->get();

        // أفضل 10 منتجات مبيعاً
        $topProducts = DB::table('orders')
            ->join('order_items', 'orders.order_id', '=', 'order_items.order_id')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.item_id')
            ->where('orders.status', 'completed')
            ->whereBetween(DB::raw('DATE(orders.created_at)'), [$startDate, $endDate])
            ->groupBy('menu_items.name')
            ->select('menu_items.name', DB::raw('SUM(order_items.quantity) as quantity'), DB::raw('SUM(order_items.price * order_items.quantity) as total'))
            ->orderBy('quantity', 'desc')
            ->limit(10)
            ->get();

        // إجمالي المبيعات
        $totalSales = Order::where('status', 'completed')
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->sum('total_amount');

        // عدد الطلبات
        $ordersCount = Order::where('status', 'completed')
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->count();

        // متوسط قيمة الطلب
        $averageOrderValue = $ordersCount > 0 ? $totalSales / $ordersCount : 0;

        return view('admin.reports.sales', compact(
            'startDate',
            'endDate',
            'dailySales',
            'salesByCategory',
            'topProducts',
            'totalSales',
            'ordersCount',
            'averageOrderValue'
        ));
    }

    // تقرير المصروفات
    public function expensesReport(Request $request)
    {
        $startDate = $request->start_date ? $request->start_date : now()->subDays(30)->format('Y-m-d');
        $endDate = $request->end_date ? $request->end_date : now()->format('Y-m-d');

        // بيانات المصروفات اليومية
        $dailyExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->groupBy('expense_date')
            ->select('expense_date as date', DB::raw('SUM(amount) as total'))
            ->orderBy('date')
            ->get();

        // المصروفات حسب الفئة
        $expensesByCategory = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->groupBy('category')
            ->select('category', DB::raw('SUM(amount) as total'))
            ->get();

        // إجمالي المصروفات
        $totalExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->sum('amount');

        // أعلى 10 مصروفات
        $topExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->orderBy('amount', 'desc')
            ->limit(10)
            ->get();

        return view('admin.reports.expenses', compact(
            'startDate',
            'endDate',
            'dailyExpenses',
            'expensesByCategory',
            'totalExpenses',
            'topExpenses'
        ));
    }

    // تقرير الربح والخسارة
    public function profitLossReport(Request $request)
    {
        $startDate = $request->start_date ? $request->start_date : now()->subMonth()->format('Y-m-d');
        $endDate = $request->end_date ? $request->end_date : now()->format('Y-m-d');

        // إجمالي المبيعات
        $totalSales = Order::where('status', 'completed')
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->sum('total_amount');

        // إجمالي المصروفات
        $totalExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->sum('amount');

        // صافي الربح
        $netProfit = $totalSales - $totalExpenses;

        // هامش الربح
        $profitMargin = $totalSales > 0 ? ($netProfit / $totalSales) * 100 : 0;

        // بيانات الربح اليومية
        $dailyProfit = DB::table(function($query) use ($startDate, $endDate) {
                $query->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(total_amount) as sales'))
                    ->from('orders')
                    ->where('status', 'completed')
                    ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                    ->groupBy(DB::raw('DATE(created_at)'));
            }, 'sales_table')
            ->leftJoin(DB::raw("(SELECT expense_date as date, SUM(amount) as expenses
                                FROM expenses
                                WHERE expense_date BETWEEN '$startDate' AND '$endDate'
                                GROUP BY expense_date) as expense_table"),
                'sales_table.date', '=', 'expense_table.date')
            ->select('sales_table.date', 'sales_table.sales', 'expense_table.expenses',
                DB::raw('IFNULL(sales_table.sales, 0) - IFNULL(expense_table.expenses, 0) as profit'))
            ->orderBy('sales_table.date')
            ->get();

        // الأرباح حسب الفئة
        $profitByCategory = DB::table('orders')
            ->join('order_items', 'orders.order_id', '=', 'order_items.order_id')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.item_id')
            ->where('orders.status', 'completed')
            ->whereBetween(DB::raw('DATE(orders.created_at)'), [$startDate, $endDate])
            ->groupBy('menu_items.category')
            ->select('menu_items.category', DB::raw('SUM(order_items.price * order_items.quantity) as sales'))
            ->get();

        return view('admin.reports.profit_loss', compact(
            'startDate',
            'endDate',
            'totalSales',
            'totalExpenses',
            'netProfit',
            'profitMargin',
            'dailyProfit',
            'profitByCategory'
        ));
    }

    // تقرير المخزون
    public function inventoryReport()
    {
        // مخزون المكونات
        $ingredients = Ingredient::with(['inventory' => function($query) {
                $query->where('quantity', '>', 0)
                    ->orderBy('expiry_date');
            }])
            ->orderBy('name')
            ->get();

        // إجمالي عدد المكونات
        $totalIngredients = Ingredient::count();

        // المكونات منخفضة المخزون
        $lowStockItems = DB::table('inventory')
            ->join('ingredients', function($join) {
                $join->on('inventory.ingredient_id', '=', 'ingredients.ingredient_id');
            })
            ->select('ingredients.name', 'ingredients.unit', DB::raw('SUM(inventory.quantity) as total_quantity'))
            ->groupBy('ingredients.name', 'ingredients.unit')
            ->having('total_quantity', '<', 10) // قيمة الحد الأدنى
            ->get();

        // عدد المكونات منخفضة المخزون
        $lowStockCount = $lowStockItems->count();

        // عدد المكونات غير المتوفرة
        $outOfStockCount = DB::table('ingredients')
            ->leftJoin('inventory', function($join) {
                $join->on('ingredients.ingredient_id', '=', 'inventory.ingredient_id');
            })
            ->select('ingredients.ingredient_id')
            ->groupBy('ingredients.ingredient_id')
            ->havingRaw('COALESCE(SUM(inventory.quantity), 0) <= 0')
            ->count();

        // عدد المكونات المتوفرة
        $inStockCount = $totalIngredients - $lowStockCount - $outOfStockCount;

        // المكونات القريبة من انتهاء الصلاحية
        $nearExpiryItems = Inventory::with('ingredient')
            ->where('quantity', '>', 0)
            ->where('expiry_date', '<=', now()->addDays(30))
            ->where('expiry_date', '>=', now())
            ->orderBy('expiry_date')
            ->get();

        // قيمة المخزون الحالي
        $inventoryValue = DB::table('inventory')
            ->where('quantity', '>', 0)
            ->sum(DB::raw('quantity * cost_per_unit'));

        // أعلى 5 مكونات من حيث القيمة
        $topValueIngredients = DB::table('inventory')
            ->join('ingredients', function($join) {
                $join->on('inventory.ingredient_id', '=', 'ingredients.ingredient_id');
            })
            ->select(
                'ingredients.name',
                'ingredients.unit',
                DB::raw('SUM(inventory.quantity) as quantity'),
                DB::raw('AVG(inventory.cost_per_unit) as unit_price'),
                DB::raw('SUM(inventory.quantity * inventory.cost_per_unit) as value')
            )
            ->where('inventory.quantity', '>', 0)
            ->groupBy('ingredients.name', 'ingredients.unit')
            ->orderBy('value', 'desc')
            ->limit(5)
            ->get();

        // قائمة المخزون للعرض في الجدول
        $inventory = DB::table('inventory')
            ->join('ingredients', function($join) {
                $join->on('inventory.ingredient_id', '=', 'ingredients.ingredient_id');
            })
            ->select(
                'ingredients.name',
                'ingredients.unit',
                DB::raw('SUM(inventory.quantity) as quantity'),
                DB::raw('AVG(inventory.cost_per_unit) as unit_price')
            )
            ->groupBy('ingredients.name', 'ingredients.unit')
            ->orderBy('ingredients.name')
            ->paginate(15);

        // إجمالي قيمة المخزون
        $totalInventoryValue = $inventoryValue;

        return view('admin.reports.inventory', compact(
            'ingredients',
            'totalIngredients',
            'lowStockItems',
            'lowStockCount',
            'outOfStockCount',
            'inStockCount',
            'nearExpiryItems',
            'inventoryValue',
            'topValueIngredients',
            'inventory',
            'totalInventoryValue'
        ));
    }

    // تقرير أداء الموظفين المحسن
    public function employeePerformanceReport(Request $request)
    {
        // تحديد الفترة الزمنية (افتراضياً الشهر الحالي)
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        // تحويل التواريخ إلى Carbon instances
        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // جلب جميع المستخدمين الموظفين النشطين
        $employees = User::where('user_type', 'employee')
            ->where('is_active', true)
            ->orderBy('first_name')
            ->get();

        $employeePerformance = [];

        foreach ($employees as $employee) {
            // عدد الطلبات التي تم إدخالها من قبل الموظف
            $ordersCreated = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            // عدد الطلبات المكتملة
            $ordersCompleted = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->count();

            // عدد الطلبات قيد التحضير
            $ordersInProgress = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'preparing')
                ->count();

            // إجمالي قيمة المبيعات
            $totalSales = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereIn('status', ['completed', 'preparing'])
                ->sum('total_amount');

            // متوسط قيمة الطلب
            $avgOrderValue = $ordersCreated > 0 ? $totalSales / $ordersCreated : 0;

            // معدل إكمال الطلبات
            $completionRate = $ordersCreated > 0 ? ($ordersCompleted / $ordersCreated) * 100 : 0;

            // متوسط وقت إكمال الطلب (بالدقائق)
            $avgCompletionTime = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->whereNotNull('updated_at')
                ->select(DB::raw('AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)) as avg_time'))
                ->value('avg_time');

            // عدد الطلبات الملغية
            $cancelledOrders = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'canceled')
                ->count();

            $employeePerformance[] = [
                'employee_id' => $employee->user_id,
                'employee_name' => $employee->first_name . ' ' . $employee->last_name,
                'employee_email' => $employee->email,
                'orders_created' => $ordersCreated,
                'orders_completed' => $ordersCompleted,
                'orders_in_progress' => $ordersInProgress,
                'cancelled_orders' => $cancelledOrders,
                'total_sales' => $totalSales,
                'avg_order_value' => $avgOrderValue,
                'completion_rate' => round($completionRate, 1),
                'avg_completion_time' => $avgCompletionTime ? round($avgCompletionTime) : 0,
            ];
        }

        // ترتيب الموظفين حسب إجمالي المبيعات
        usort($employeePerformance, function($a, $b) {
            return $b['total_sales'] <=> $a['total_sales'];
        });

        // إحصائيات عامة
        $totalEmployees = count($employees);
        $totalOrdersAllEmployees = array_sum(array_column($employeePerformance, 'orders_created'));
        $totalSalesAllEmployees = array_sum(array_column($employeePerformance, 'total_sales'));
        $avgOrdersPerEmployee = $totalEmployees > 0 ? $totalOrdersAllEmployees / $totalEmployees : 0;

        // أفضل موظف
        $topEmployee = !empty($employeePerformance) ? $employeePerformance[0] : null;

        return view('admin.reports.employee_performance', compact(
            'employeePerformance', 'startDate', 'endDate', 'totalEmployees',
            'totalOrdersAllEmployees', 'totalSalesAllEmployees', 'avgOrdersPerEmployee',
            'topEmployee'
        ));
    }

    // تقرير تقييمات العملاء
    public function customerReviewsReport()
    {
        // متوسط التقييم
        $averageRating = DB::table('reviews')->avg('rating');

        // توزيع التقييمات
        $ratingDistribution = DB::table('reviews')
            ->select('rating', DB::raw('COUNT(*) as count'))
            ->groupBy('rating')
            ->orderBy('rating', 'desc')
            ->get();

        // أحدث التقييمات
        $latestReviews = DB::table('reviews')
            ->join('users', 'reviews.user_id', '=', 'users.user_id')
            ->select('reviews.*', 'users.first_name', 'users.last_name')
            ->orderBy('reviews.created_at', 'desc')
            ->limit(10)
            ->get();

        // تقييم المنتجات
        $menuItemRatings = DB::table('reviews')
            ->join('orders', 'reviews.order_id', '=', 'orders.order_id')
            ->join('order_items', 'orders.order_id', '=', 'order_items.order_id')
            ->join('menu_items', 'order_items.menu_item_id', '=', 'menu_items.item_id')
            ->select('menu_items.name', DB::raw('AVG(reviews.rating) as avg_rating'), DB::raw('COUNT(DISTINCT reviews.review_id) as reviews_count'))
            ->groupBy('menu_items.name')
            ->orderBy('avg_rating', 'desc')
            ->get();

        return view('admin.reports.customer_reviews', compact(
            'averageRating',
            'ratingDistribution',
            'latestReviews',
            'menuItemRatings'
        ));
    }

    // تقرير الطلبات
    public function ordersReport(Request $request)
    {
        $startDate = $request->start_date ? $request->start_date : now()->subDays(30)->format('Y-m-d');
        $endDate = $request->end_date ? $request->end_date : now()->format('Y-m-d');

        // جميع الطلبات في الفترة المحددة
        $orders = Order::with(['user', 'items.menuItem'])
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        // إحصائيات الطلبات
        $totalOrders = Order::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])->count();
        $completedOrders = Order::where('status', 'completed')->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])->count();
        $pendingOrders = Order::where('status', 'pending')->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])->count();
        $cancelledOrders = Order::where('status', 'cancelled')->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])->count();

        // متوسط قيمة الطلب
        $averageOrderValue = Order::where('status', 'completed')
            ->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->avg('total_amount');

        // الطلبات حسب اليوم
        $ordersByDay = Order::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->orderBy('date')
            ->get();

        // الطلبات حسب الحالة
        $ordersByStatus = Order::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->groupBy('status')
            ->select('status', DB::raw('COUNT(*) as count'))
            ->get();

        return view('admin.reports.orders', compact(
            'orders',
            'startDate',
            'endDate',
            'totalOrders',
            'completedOrders',
            'pendingOrders',
            'cancelledOrders',
            'averageOrderValue',
            'ordersByDay',
            'ordersByStatus'
        ));
    }

    // تقرير مقارنة الأداء
    public function performanceReport(Request $request)
    {
        $currentYearStart = now()->startOfYear()->format('Y-m-d');
        $currentYearEnd = now()->format('Y-m-d');
        $previousYearStart = now()->subYear()->startOfYear()->format('Y-m-d');
        $previousYearEnd = now()->subYear()->endOfYear()->format('Y-m-d');

        // مبيعات العام الحالي
        $currentYearSales = Order::where('status', 'completed')
            ->whereBetween('created_at', [$currentYearStart, $currentYearEnd])
            ->sum('total_amount');

        // مبيعات العام السابق
        $previousYearSales = Order::where('status', 'completed')
            ->whereBetween('created_at', [$previousYearStart, $previousYearEnd])
            ->sum('total_amount');

        // مصروفات العام الحالي
        $currentYearExpenses = Expense::whereBetween('expense_date', [$currentYearStart, $currentYearEnd])
            ->sum('amount');

        // مصروفات العام السابق
        $previousYearExpenses = Expense::whereBetween('expense_date', [$previousYearStart, $previousYearEnd])
            ->sum('amount');

        // أرباح العام الحالي
        $currentYearProfit = $currentYearSales - $currentYearExpenses;

        // أرباح العام السابق
        $previousYearProfit = $previousYearSales - $previousYearExpenses;

        // نسبة النمو في المبيعات
        $salesGrowthRate = $previousYearSales > 0 ? (($currentYearSales - $previousYearSales) / $previousYearSales) * 100 : 0;

        // نسبة النمو في الأرباح
        $profitGrowthRate = $previousYearProfit > 0 ? (($currentYearProfit - $previousYearProfit) / $previousYearProfit) * 100 : 0;

        // مبيعات كل شهر للعام الحالي
        $currentYearMonthlySales = Order::where('status', 'completed')
            ->whereBetween('created_at', [$currentYearStart, $currentYearEnd])
            ->selectRaw('MONTH(created_at) as month, SUM(total_amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month');

        // مبيعات كل شهر للعام السابق
        $previousYearMonthlySales = Order::where('status', 'completed')
            ->whereBetween('created_at', [$previousYearStart, $previousYearEnd])
            ->selectRaw('MONTH(created_at) as month, SUM(total_amount) as total')
            ->groupBy('month')
            ->orderBy('month')
            ->get()
            ->keyBy('month');

        // تحضير بيانات المبيعات الشهرية للرسم البياني
        $monthlyData = [];
        $monthNames = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل', 5 => 'مايو', 6 => 'يونيو',
            7 => 'يوليو', 8 => 'أغسطس', 9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];

        for ($i = 1; $i <= 12; $i++) {
            $monthlyData[] = [
                'month' => $monthNames[$i],
                'current_year' => $currentYearMonthlySales->has($i) ? $currentYearMonthlySales[$i]->total : 0,
                'previous_year' => $previousYearMonthlySales->has($i) ? $previousYearMonthlySales[$i]->total : 0,
            ];
        }

        return view('admin.reports.performance', compact(
            'currentYearSales',
            'previousYearSales',
            'currentYearExpenses',
            'previousYearExpenses',
            'currentYearProfit',
            'previousYearProfit',
            'salesGrowthRate',
            'profitGrowthRate',
            'monthlyData'
        ));
    }

    // تقرير العملاء
    public function customersReport(Request $request)
    {
        // إجمالي عدد العملاء
        $totalCustomers = DB::table('users')
            ->where('user_type', 'customer')
            ->count();

        // العملاء الجدد في آخر 30 يوم
        $newCustomers = DB::table('users')
            ->where('user_type', 'customer')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        // العملاء النشطين (الذين لديهم طلبات في آخر 90 يوم)
        $activeCustomers = DB::table('users')
            ->join('orders', 'users.user_id', '=', 'orders.user_id')
            ->where('users.user_type', 'customer')
            ->where('orders.created_at', '>=', now()->subDays(90))
            ->distinct('users.user_id')
            ->count('users.user_id');

        // أفضل 10 عملاء من حيث قيمة الطلبات
        $topCustomers = DB::table('users')
            ->join('orders', 'users.user_id', '=', 'orders.user_id')
            ->where('users.user_type', 'customer')
            ->where('orders.status', 'completed')
            ->select('users.user_id', 'users.first_name', 'users.last_name', 'users.email', 'users.phone',
                DB::raw('COUNT(orders.order_id) as orders_count'),
                DB::raw('SUM(orders.total_amount) as total_spent'))
            ->groupBy('users.user_id', 'users.first_name', 'users.last_name', 'users.email', 'users.phone')
            ->orderBy('total_spent', 'desc')
            ->limit(10)
            ->get();

        // متوسط قيمة الطلب لكل عميل
        $averageOrderValuePerCustomer = DB::table('orders')
            ->join('users', 'orders.user_id', '=', 'users.user_id')
            ->where('users.user_type', 'customer')
            ->where('orders.status', 'completed')
            ->avg('orders.total_amount');

        return view('admin.reports.customers', compact(
            'totalCustomers',
            'newCustomers',
            'activeCustomers',
            'topCustomers',
            'averageOrderValuePerCustomer'
        ));
    }

    // ==================== دوال التصدير الشاملة ====================

    /**
     * تصدير التقرير المالي الشامل
     */
    public function exportFinancialReport(Request $request)
    {
        $format = $request->get('format', 'excel'); // excel, pdf, csv
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // جمع البيانات المالية الشاملة
        $data = $this->getComprehensiveFinancialData($startDate, $endDate);

        switch ($format) {
            case 'pdf':
                return $this->exportFinancialToPDF($data, $startDate, $endDate);
            case 'csv':
                return $this->exportFinancialToCSV($data, $startDate, $endDate);
            default:
                return $this->exportFinancialToExcel($data, $startDate, $endDate);
        }
    }

    /**
     * تصدير تقرير أداء الموظفين
     */
    public function exportEmployeePerformance(Request $request)
    {
        $format = $request->get('format', 'excel');
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // جمع بيانات أداء الموظفين
        $data = $this->getEmployeePerformanceData($startDate, $endDate);

        switch ($format) {
            case 'pdf':
                return $this->exportEmployeeToPDF($data, $startDate, $endDate);
            case 'csv':
                return $this->exportEmployeeToCSV($data, $startDate, $endDate);
            default:
                return $this->exportEmployeeToExcel($data, $startDate, $endDate);
        }
    }

    /**
     * تصدير تقرير المبيعات الشامل
     */
    public function exportSalesReport(Request $request)
    {
        $format = $request->get('format', 'excel');
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // جمع بيانات المبيعات الشاملة
        $data = $this->getComprehensiveSalesData($startDate, $endDate);

        switch ($format) {
            case 'pdf':
                return $this->exportSalesToPDF($data, $startDate, $endDate);
            case 'csv':
                return $this->exportSalesToCSV($data, $startDate, $endDate);
            default:
                return $this->exportSalesToExcel($data, $startDate, $endDate);
        }
    }

    /**
     * تصدير تقرير المخزون الشامل
     */
    public function exportInventoryReport(Request $request)
    {
        $format = $request->get('format', 'excel');

        // جمع بيانات المخزون الشاملة
        $data = $this->getComprehensiveInventoryData();

        switch ($format) {
            case 'pdf':
                return $this->exportInventoryToPDF($data);
            case 'csv':
                return $this->exportInventoryToCSV($data);
            default:
                return $this->exportInventoryToExcel($data);
        }
    }

    /**
     * تصدير تقرير العملاء الشامل
     */
    public function exportCustomersReport(Request $request)
    {
        $format = $request->get('format', 'excel');
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // جمع بيانات العملاء الشاملة
        $data = $this->getComprehensiveCustomersData($startDate, $endDate);

        switch ($format) {
            case 'pdf':
                return $this->exportCustomersToPDF($data, $startDate, $endDate);
            case 'csv':
                return $this->exportCustomersToCSV($data, $startDate, $endDate);
            default:
                return $this->exportCustomersToExcel($data, $startDate, $endDate);
        }
    }

    /**
     * تصدير تقرير شامل لكل شيء (التقرير الرئيسي)
     */
    public function exportMasterReport(Request $request)
    {
        $format = $request->get('format', 'excel');
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // جمع جميع البيانات الشاملة
        $data = [
            'financial' => $this->getComprehensiveFinancialData($startDate, $endDate),
            'sales' => $this->getComprehensiveSalesData($startDate, $endDate),
            'employees' => $this->getEmployeePerformanceData($startDate, $endDate),
            'inventory' => $this->getComprehensiveInventoryData(),
            'customers' => $this->getComprehensiveCustomersData($startDate, $endDate),
            'period' => ['start' => $startDate, 'end' => $endDate]
        ];

        switch ($format) {
            case 'pdf':
                return $this->exportMasterToPDF($data);
            case 'csv':
                return $this->exportMasterToCSV($data);
            default:
                return $this->exportMasterToExcel($data);
        }
    }

    // ==================== دوال جمع البيانات الشاملة ====================

    /**
     * جمع البيانات المالية الشاملة
     */
    private function getComprehensiveFinancialData($startDate, $endDate)
    {
        // المبيعات
        $totalSales = Payment::whereHas('order', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->whereIn('status', ['completed', 'preparing']);
        })->sum('amount');

        // المصروفات
        $totalExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->sum('amount');

        // تكلفة المواد الخام
        $costOfGoodsSold = $this->calculateCostOfGoodsSold($startDate, $endDate);

        // الأرباح
        $grossProfit = $totalSales - $costOfGoodsSold;
        $netProfit = $grossProfit - $totalExpenses;
        $grossProfitMargin = $totalSales > 0 ? ($grossProfit / $totalSales) * 100 : 0;
        $netProfitMargin = $totalSales > 0 ? ($netProfit / $totalSales) * 100 : 0;

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'sales' => [
                'total' => $totalSales,
                'orders_count' => Order::whereBetween('created_at', [$startDate, $endDate])
                    ->whereIn('status', ['completed', 'preparing'])->count(),
                'average_order' => $totalSales > 0 ? $totalSales / max(1, Order::whereBetween('created_at', [$startDate, $endDate])
                    ->whereIn('status', ['completed', 'preparing'])->count()) : 0,
            ],
            'expenses' => [
                'total' => $totalExpenses,
                'by_category' => Expense::whereBetween('expense_date', [$startDate, $endDate])
                    ->select('category', DB::raw('SUM(amount) as total'))
                    ->groupBy('category')
                    ->get(),
            ],
            'costs' => [
                'cogs' => $costOfGoodsSold,
            ],
            'profits' => [
                'gross' => $grossProfit,
                'net' => $netProfit,
                'gross_margin' => $grossProfitMargin,
                'net_margin' => $netProfitMargin,
            ]
        ];
    }

    /**
     * جمع بيانات المبيعات الشاملة
     */
    private function getComprehensiveSalesData($startDate, $endDate)
    {
        // أفضل المنتجات مبيعاً
        $topProducts = OrderItem::whereHas('order', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->whereIn('status', ['completed', 'preparing']);
        })
        ->select('menu_item_id',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('SUM(price * quantity) as total_revenue'),
                DB::raw('AVG(price) as avg_price'))
        ->with('menuItem')
        ->groupBy('menu_item_id')
        ->orderBy('total_revenue', 'desc')
        ->limit(20)
        ->get();

        // المبيعات اليومية
        $dailySales = Order::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', ['completed', 'preparing'])
            ->select(DB::raw('DATE(created_at) as date'),
                    DB::raw('COUNT(*) as orders_count'),
                    DB::raw('SUM(total_amount) as total_sales'))
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get();

        // المبيعات الشهرية
        $monthlySales = Order::whereBetween('created_at', [$startDate, $endDate])
            ->whereIn('status', ['completed', 'preparing'])
            ->select(DB::raw('YEAR(created_at) as year'),
                    DB::raw('MONTH(created_at) as month'),
                    DB::raw('COUNT(*) as orders_count'),
                    DB::raw('SUM(total_amount) as total_sales'))
            ->groupBy(DB::raw('YEAR(created_at)'), DB::raw('MONTH(created_at)'))
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'summary' => [
                'total_sales' => Order::whereBetween('created_at', [$startDate, $endDate])
                    ->whereIn('status', ['completed', 'preparing'])->sum('total_amount'),
                'total_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
                    ->whereIn('status', ['completed', 'preparing'])->count(),
                'average_order_value' => Order::whereBetween('created_at', [$startDate, $endDate])
                    ->whereIn('status', ['completed', 'preparing'])->avg('total_amount'),
            ],
            'top_products' => $topProducts,
            'daily_sales' => $dailySales,
            'monthly_sales' => $monthlySales,
        ];
    }

    /**
     * جمع بيانات أداء الموظفين الشاملة
     */
    private function getEmployeePerformanceData($startDate, $endDate)
    {
        $employees = User::where('user_type', 'employee')
            ->where('is_active', true)
            ->orderBy('first_name')
            ->get();

        $employeePerformance = [];

        foreach ($employees as $employee) {
            $ordersCreated = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            $ordersCompleted = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->where('status', 'completed')
                ->count();

            $totalSales = Order::where('user_id', $employee->user_id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereIn('status', ['completed', 'preparing'])
                ->sum('total_amount');

            $employeePerformance[] = [
                'employee_id' => $employee->user_id,
                'employee_name' => $employee->first_name . ' ' . $employee->last_name,
                'employee_email' => $employee->email,
                'orders_created' => $ordersCreated,
                'orders_completed' => $ordersCompleted,
                'total_sales' => $totalSales,
                'avg_order_value' => $ordersCreated > 0 ? $totalSales / $ordersCreated : 0,
                'completion_rate' => $ordersCreated > 0 ? ($ordersCompleted / $ordersCreated) * 100 : 0,
            ];
        }

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'employees' => $employeePerformance,
            'summary' => [
                'total_employees' => count($employees),
                'total_orders' => array_sum(array_column($employeePerformance, 'orders_created')),
                'total_sales' => array_sum(array_column($employeePerformance, 'total_sales')),
            ]
        ];
    }

    /**
     * جمع بيانات المخزون الشاملة
     */
    private function getComprehensiveInventoryData()
    {
        $ingredients = Ingredient::with(['inventory' => function($query) {
            $query->orderBy('expiry_date');
        }])->orderBy('name')->get();

        $inventoryData = [];
        $totalValue = 0;
        $lowStockCount = 0;
        $outOfStockCount = 0;

        foreach ($ingredients as $ingredient) {
            $totalQuantity = $ingredient->inventory->sum('quantity');
            $avgCost = $ingredient->inventory->avg('cost_per_unit') ?? 0;
            $itemValue = $totalQuantity * $avgCost;
            $totalValue += $itemValue;

            $status = 'in_stock';
            if ($totalQuantity == 0) {
                $status = 'out_of_stock';
                $outOfStockCount++;
            } elseif ($totalQuantity <= 10) { // حد المخزون المنخفض
                $status = 'low_stock';
                $lowStockCount++;
            }

            $inventoryData[] = [
                'ingredient_id' => $ingredient->ingredient_id,
                'name' => $ingredient->name,
                'unit' => $ingredient->unit,
                'total_quantity' => $totalQuantity,
                'avg_cost' => $avgCost,
                'total_value' => $itemValue,
                'status' => $status,
                'nearest_expiry' => $ingredient->inventory->where('expiry_date', '>', now())
                    ->sortBy('expiry_date')->first()?->expiry_date,
            ];
        }

        return [
            'inventory' => $inventoryData,
            'summary' => [
                'total_ingredients' => count($ingredients),
                'total_value' => $totalValue,
                'low_stock_count' => $lowStockCount,
                'out_of_stock_count' => $outOfStockCount,
                'in_stock_count' => count($ingredients) - $lowStockCount - $outOfStockCount,
            ]
        ];
    }

    /**
     * جمع بيانات العملاء الشاملة
     */
    private function getComprehensiveCustomersData($startDate, $endDate)
    {
        // أفضل العملاء
        $topCustomers = User::where('user_type', 'customer')
            ->whereHas('orders', function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->whereIn('status', ['completed', 'preparing']);
            })
            ->withCount(['orders' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->whereIn('status', ['completed', 'preparing']);
            }])
            ->withSum(['orders' => function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate])
                      ->whereIn('status', ['completed', 'preparing']);
            }], 'total_amount')
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit(20)
            ->get();

        // العملاء الجدد
        $newCustomers = User::where('user_type', 'customer')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // العملاء النشطين
        $activeCustomers = User::where('user_type', 'customer')
            ->whereHas('orders', function($query) use ($startDate, $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            })
            ->count();

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'top_customers' => $topCustomers,
            'summary' => [
                'total_customers' => User::where('user_type', 'customer')->count(),
                'new_customers' => $newCustomers,
                'active_customers' => $activeCustomers,
                'customer_retention_rate' => User::where('user_type', 'customer')->count() > 0 ?
                    ($activeCustomers / User::where('user_type', 'customer')->count()) * 100 : 0,
            ]
        ];
    }

    // ==================== دوال التصدير إلى Excel ====================

    /**
     * تصدير التقرير المالي إلى Excel
     */
    private function exportFinancialToExcel($data, $startDate, $endDate)
    {
        $filename = 'financial_report_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // العنوان الرئيسي
            fputcsv($file, ['التقرير المالي الشامل']);
            fputcsv($file, ['من تاريخ: ' . $data['period']['start']->format('Y-m-d')]);
            fputcsv($file, ['إلى تاريخ: ' . $data['period']['end']->format('Y-m-d')]);
            fputcsv($file, []); // سطر فارغ

            // ملخص المبيعات
            fputcsv($file, ['ملخص المبيعات']);
            fputcsv($file, ['إجمالي المبيعات', number_format($data['sales']['total'], 2) . ' د.ل']);
            fputcsv($file, ['عدد الطلبات', number_format($data['sales']['orders_count'])]);
            fputcsv($file, ['متوسط قيمة الطلب', number_format($data['sales']['average_order'], 2) . ' د.ل']);
            fputcsv($file, []); // سطر فارغ

            // ملخص المصروفات
            fputcsv($file, ['ملخص المصروفات']);
            fputcsv($file, ['إجمالي المصروفات', number_format($data['expenses']['total'], 2) . ' د.ل']);
            fputcsv($file, ['تكلفة المواد الخام', number_format($data['costs']['cogs'], 2) . ' د.ل']);
            fputcsv($file, []); // سطر فارغ

            // ملخص الأرباح
            fputcsv($file, ['ملخص الأرباح']);
            fputcsv($file, ['إجمالي الربح', number_format($data['profits']['gross'], 2) . ' د.ل']);
            fputcsv($file, ['صافي الربح', number_format($data['profits']['net'], 2) . ' د.ل']);
            fputcsv($file, ['هامش الربح الإجمالي', number_format($data['profits']['gross_margin'], 1) . '%']);
            fputcsv($file, ['هامش الربح الصافي', number_format($data['profits']['net_margin'], 1) . '%']);
            fputcsv($file, []); // سطر فارغ

            // المصروفات حسب الفئة
            fputcsv($file, ['المصروفات حسب الفئة']);
            fputcsv($file, ['الفئة', 'المبلغ (د.ل)']);
            foreach ($data['expenses']['by_category'] as $expense) {
                fputcsv($file, [$expense->category, number_format($expense->total, 2)]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * تصدير تقرير أداء الموظفين إلى Excel
     */
    private function exportEmployeeToExcel($data, $startDate, $endDate)
    {
        $filename = 'employee_performance_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // العنوان الرئيسي
            fputcsv($file, ['تقرير أداء الموظفين']);
            fputcsv($file, ['من تاريخ: ' . $data['period']['start']->format('Y-m-d')]);
            fputcsv($file, ['إلى تاريخ: ' . $data['period']['end']->format('Y-m-d')]);
            fputcsv($file, []); // سطر فارغ

            // الملخص العام
            fputcsv($file, ['الملخص العام']);
            fputcsv($file, ['إجمالي الموظفين', $data['summary']['total_employees']]);
            fputcsv($file, ['إجمالي الطلبات', number_format($data['summary']['total_orders'])]);
            fputcsv($file, ['إجمالي المبيعات', number_format($data['summary']['total_sales'], 2) . ' د.ل']);
            fputcsv($file, []); // سطر فارغ

            // تفاصيل أداء الموظفين
            fputcsv($file, ['تفاصيل أداء الموظفين']);
            fputcsv($file, [
                'اسم الموظف', 'الإيميل', 'الطلبات المدخلة', 'الطلبات المكتملة',
                'إجمالي المبيعات (د.ل)', 'متوسط قيمة الطلب (د.ل)', 'معدل الإكمال (%)'
            ]);

            foreach ($data['employees'] as $employee) {
                fputcsv($file, [
                    $employee['employee_name'],
                    $employee['employee_email'],
                    $employee['orders_created'],
                    $employee['orders_completed'],
                    number_format($employee['total_sales'], 2),
                    number_format($employee['avg_order_value'], 2),
                    number_format($employee['completion_rate'], 1)
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * تصدير تقرير المبيعات إلى Excel
     */
    private function exportSalesToExcel($data, $startDate, $endDate)
    {
        $filename = 'sales_report_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // العنوان الرئيسي
            fputcsv($file, ['تقرير المبيعات الشامل']);
            fputcsv($file, ['من تاريخ: ' . $data['period']['start']->format('Y-m-d')]);
            fputcsv($file, ['إلى تاريخ: ' . $data['period']['end']->format('Y-m-d')]);
            fputcsv($file, []); // سطر فارغ

            // ملخص المبيعات
            fputcsv($file, ['ملخص المبيعات']);
            fputcsv($file, ['إجمالي المبيعات', number_format($data['summary']['total_sales'], 2) . ' د.ل']);
            fputcsv($file, ['إجمالي الطلبات', number_format($data['summary']['total_orders'])]);
            fputcsv($file, ['متوسط قيمة الطلب', number_format($data['summary']['average_order_value'], 2) . ' د.ل']);
            fputcsv($file, []); // سطر فارغ

            // أفضل المنتجات مبيعاً
            fputcsv($file, ['أفضل المنتجات مبيعاً']);
            fputcsv($file, ['اسم المنتج', 'الكمية المباعة', 'إجمالي المبيعات (د.ل)', 'متوسط السعر (د.ل)']);
            foreach ($data['top_products'] as $product) {
                fputcsv($file, [
                    $product->menuItem->name ?? 'منتج محذوف',
                    $product->total_quantity,
                    number_format($product->total_revenue, 2),
                    number_format($product->avg_price, 2)
                ]);
            }
            fputcsv($file, []); // سطر فارغ

            // المبيعات اليومية
            fputcsv($file, ['المبيعات اليومية']);
            fputcsv($file, ['التاريخ', 'عدد الطلبات', 'إجمالي المبيعات (د.ل)']);
            foreach ($data['daily_sales'] as $day) {
                fputcsv($file, [
                    $day->date,
                    $day->orders_count,
                    number_format($day->total_sales, 2)
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * تصدير تقرير المخزون إلى Excel
     */
    private function exportInventoryToExcel($data)
    {
        $filename = 'inventory_report_' . date('Y-m-d_H-i-s') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // العنوان الرئيسي
            fputcsv($file, ['تقرير المخزون الشامل']);
            fputcsv($file, ['تاريخ التقرير: ' . date('Y-m-d H:i:s')]);
            fputcsv($file, []); // سطر فارغ

            // الملخص العام
            fputcsv($file, ['الملخص العام']);
            fputcsv($file, ['إجمالي المكونات', $data['summary']['total_ingredients']]);
            fputcsv($file, ['قيمة المخزون الإجمالية', number_format($data['summary']['total_value'], 2) . ' د.ل']);
            fputcsv($file, ['مكونات منخفضة المخزون', $data['summary']['low_stock_count']]);
            fputcsv($file, ['مكونات نفدت من المخزون', $data['summary']['out_of_stock_count']]);
            fputcsv($file, ['مكونات متوفرة', $data['summary']['in_stock_count']]);
            fputcsv($file, []); // سطر فارغ

            // تفاصيل المخزون
            fputcsv($file, ['تفاصيل المخزون']);
            fputcsv($file, [
                'اسم المكون', 'الوحدة', 'الكمية الإجمالية', 'متوسط التكلفة (د.ل)',
                'القيمة الإجمالية (د.ل)', 'الحالة', 'أقرب تاريخ انتهاء'
            ]);

            foreach ($data['inventory'] as $item) {
                $statusText = [
                    'in_stock' => 'متوفر',
                    'low_stock' => 'مخزون منخفض',
                    'out_of_stock' => 'نفد من المخزون'
                ];

                fputcsv($file, [
                    $item['name'],
                    $item['unit'],
                    $item['total_quantity'],
                    number_format($item['avg_cost'], 2),
                    number_format($item['total_value'], 2),
                    $statusText[$item['status']] ?? $item['status'],
                    $item['nearest_expiry'] ? Carbon::parse($item['nearest_expiry'])->format('Y-m-d') : 'غير محدد'
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    // ==================== دوال التصدير إلى CSV ====================

    /**
     * تصدير التقرير المالي إلى CSV
     */
    private function exportFinancialToCSV($data, $startDate, $endDate)
    {
        $filename = 'financial_report_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // البيانات المالية الأساسية
            fputcsv($file, ['البيان', 'القيمة']);
            fputcsv($file, ['إجمالي المبيعات', number_format($data['sales']['total'], 2) . ' د.ل']);
            fputcsv($file, ['إجمالي المصروفات', number_format($data['expenses']['total'], 2) . ' د.ل']);
            fputcsv($file, ['تكلفة المواد الخام', number_format($data['costs']['cogs'], 2) . ' د.ل']);
            fputcsv($file, ['إجمالي الربح', number_format($data['profits']['gross'], 2) . ' د.ل']);
            fputcsv($file, ['صافي الربح', number_format($data['profits']['net'], 2) . ' د.ل']);
            fputcsv($file, ['هامش الربح الصافي', number_format($data['profits']['net_margin'], 1) . '%']);

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * تصدير تقرير العملاء إلى Excel
     */
    private function exportCustomersToExcel($data, $startDate, $endDate)
    {
        $filename = 'customers_report_' . $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // العنوان الرئيسي
            fputcsv($file, ['تقرير العملاء الشامل']);
            fputcsv($file, ['من تاريخ: ' . $data['period']['start']->format('Y-m-d')]);
            fputcsv($file, ['إلى تاريخ: ' . $data['period']['end']->format('Y-m-d')]);
            fputcsv($file, []); // سطر فارغ

            // الملخص العام
            fputcsv($file, ['الملخص العام']);
            fputcsv($file, ['إجمالي العملاء', $data['summary']['total_customers']]);
            fputcsv($file, ['العملاء الجدد', $data['summary']['new_customers']]);
            fputcsv($file, ['العملاء النشطين', $data['summary']['active_customers']]);
            fputcsv($file, ['معدل الاحتفاظ بالعملاء', number_format($data['summary']['customer_retention_rate'], 1) . '%']);
            fputcsv($file, []); // سطر فارغ

            // أفضل العملاء
            fputcsv($file, ['أفضل العملاء']);
            fputcsv($file, ['اسم العميل', 'الإيميل', 'عدد الطلبات', 'إجمالي المبيعات (د.ل)']);
            foreach ($data['top_customers'] as $customer) {
                fputcsv($file, [
                    $customer->first_name . ' ' . $customer->last_name,
                    $customer->email,
                    $customer->orders_count,
                    number_format($customer->orders_sum_total_amount ?? 0, 2)
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    /**
     * تصدير التقرير الرئيسي الشامل إلى Excel
     */
    private function exportMasterToExcel($data)
    {
        $filename = 'master_report_' . $data['period']['start']->format('Y-m-d') . '_to_' . $data['period']['end']->format('Y-m-d') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // العنوان الرئيسي
            fputcsv($file, ['التقرير الشامل للمطعم']);
            fputcsv($file, ['من تاريخ: ' . $data['period']['start']->format('Y-m-d')]);
            fputcsv($file, ['إلى تاريخ: ' . $data['period']['end']->format('Y-m-d')]);
            fputcsv($file, ['تاريخ إنشاء التقرير: ' . now()->format('Y-m-d H:i:s')]);
            fputcsv($file, []); // سطر فارغ

            // ملخص مالي سريع
            fputcsv($file, ['الملخص المالي']);
            fputcsv($file, ['إجمالي المبيعات', number_format($data['financial']['sales']['total'], 2) . ' د.ل']);
            fputcsv($file, ['إجمالي المصروفات', number_format($data['financial']['expenses']['total'], 2) . ' د.ل']);
            fputcsv($file, ['صافي الربح', number_format($data['financial']['profits']['net'], 2) . ' د.ل']);
            fputcsv($file, ['هامش الربح الصافي', number_format($data['financial']['profits']['net_margin'], 1) . '%']);
            fputcsv($file, []); // سطر فارغ

            // ملخص المبيعات
            fputcsv($file, ['ملخص المبيعات']);
            fputcsv($file, ['عدد الطلبات', number_format($data['sales']['summary']['total_orders'])]);
            fputcsv($file, ['متوسط قيمة الطلب', number_format($data['sales']['summary']['average_order_value'], 2) . ' د.ل']);
            fputcsv($file, []); // سطر فارغ

            // ملخص الموظفين
            fputcsv($file, ['ملخص الموظفين']);
            fputcsv($file, ['إجمالي الموظفين', $data['employees']['summary']['total_employees']]);
            fputcsv($file, ['إجمالي طلبات الموظفين', number_format($data['employees']['summary']['total_orders'])]);
            fputcsv($file, []); // سطر فارغ

            // ملخص المخزون
            fputcsv($file, ['ملخص المخزون']);
            fputcsv($file, ['إجمالي المكونات', $data['inventory']['summary']['total_ingredients']]);
            fputcsv($file, ['قيمة المخزون', number_format($data['inventory']['summary']['total_value'], 2) . ' د.ل']);
            fputcsv($file, ['مكونات منخفضة المخزون', $data['inventory']['summary']['low_stock_count']]);
            fputcsv($file, []); // سطر فارغ

            // ملخص العملاء
            fputcsv($file, ['ملخص العملاء']);
            fputcsv($file, ['إجمالي العملاء', $data['customers']['summary']['total_customers']]);
            fputcsv($file, ['العملاء الجدد', $data['customers']['summary']['new_customers']]);
            fputcsv($file, ['العملاء النشطين', $data['customers']['summary']['active_customers']]);

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    // دوال مؤقتة للـ PDF (يمكن تطويرها لاحقاً)
    private function exportFinancialToPDF($data, $startDate, $endDate)
    {
        return $this->exportFinancialToExcel($data, $startDate, $endDate);
    }

    private function exportEmployeeToPDF($data, $startDate, $endDate)
    {
        return $this->exportEmployeeToExcel($data, $startDate, $endDate);
    }

    private function exportSalesToPDF($data, $startDate, $endDate)
    {
        return $this->exportSalesToExcel($data, $startDate, $endDate);
    }

    private function exportInventoryToPDF($data)
    {
        return $this->exportInventoryToExcel($data);
    }

    private function exportCustomersToPDF($data, $startDate, $endDate)
    {
        return $this->exportCustomersToExcel($data, $startDate, $endDate);
    }

    private function exportMasterToPDF($data)
    {
        return $this->exportMasterToExcel($data);
    }

    // دوال مؤقتة للـ CSV (يمكن تطويرها لاحقاً)
    private function exportEmployeeToCSV($data, $startDate, $endDate)
    {
        return $this->exportEmployeeToExcel($data, $startDate, $endDate);
    }

    private function exportSalesToCSV($data, $startDate, $endDate)
    {
        return $this->exportSalesToExcel($data, $startDate, $endDate);
    }

    private function exportInventoryToCSV($data)
    {
        return $this->exportInventoryToExcel($data);
    }

    private function exportCustomersToCSV($data, $startDate, $endDate)
    {
        return $this->exportCustomersToExcel($data, $startDate, $endDate);
    }

    private function exportMasterToCSV($data)
    {
        return $this->exportMasterToExcel($data);
    }



    /**
     * حساب تكلفة منتج واحد من القائمة
     */
    private function calculateMenuItemCost($menuItem)
    {
        // هذه دالة تقديرية - يمكن تطويرها لاحقاً لتشمل المكونات الفعلية
        // حالياً نستخدم نسبة تقديرية من سعر البيع
        return $menuItem->price * 0.35; // افتراض أن تكلفة المواد الخام 35% من سعر البيع
    }

    /**
     * تقرير شامل للربحية
     */
    public function profitabilityReport(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);

        // البيانات المالية الشاملة
        $financialData = $this->getComprehensiveFinancialData($startDate, $endDate);

        // تحليل الربحية حسب المنتج
        $productProfitability = OrderItem::whereHas('order', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate])
                  ->whereIn('status', ['completed', 'preparing']);
        })
        ->select('menu_item_id',
                DB::raw('SUM(quantity) as total_quantity'),
                DB::raw('SUM(price * quantity) as total_revenue'),
                DB::raw('AVG(price) as avg_price'))
        ->with('menuItem')
        ->groupBy('menu_item_id')
        ->orderBy('total_revenue', 'desc')
        ->get()
        ->map(function($item) {
            $cost = $this->calculateMenuItemCost($item->menuItem);
            $totalCost = $cost * $item->total_quantity;
            $profit = $item->total_revenue - $totalCost;
            $profitMargin = $item->total_revenue > 0 ? ($profit / $item->total_revenue) * 100 : 0;

            return [
                'product_name' => $item->menuItem->name ?? 'منتج محذوف',
                'quantity_sold' => $item->total_quantity,
                'total_revenue' => $item->total_revenue,
                'total_cost' => $totalCost,
                'profit' => $profit,
                'profit_margin' => $profitMargin,
                'avg_price' => $item->avg_price,
                'cost_per_unit' => $cost,
            ];
        });

        return view('admin.reports.profitability', compact(
            'financialData', 'productProfitability', 'startDate', 'endDate'
        ));
    }

    /**
     * تقرير مقارنة الفترات
     */
    public function periodComparisonReport(Request $request)
    {
        $currentStart = $request->get('current_start', Carbon::now()->startOfMonth());
        $currentEnd = $request->get('current_end', Carbon::now()->endOfMonth());
        $previousStart = $request->get('previous_start', Carbon::now()->subMonth()->startOfMonth());
        $previousEnd = $request->get('previous_end', Carbon::now()->subMonth()->endOfMonth());

        $currentStart = Carbon::parse($currentStart);
        $currentEnd = Carbon::parse($currentEnd);
        $previousStart = Carbon::parse($previousStart);
        $previousEnd = Carbon::parse($previousEnd);

        // البيانات للفترة الحالية
        $currentData = $this->getComprehensiveFinancialData($currentStart, $currentEnd);

        // البيانات للفترة السابقة
        $previousData = $this->getComprehensiveFinancialData($previousStart, $previousEnd);

        // حساب النسب المئوية للتغيير
        $comparison = [
            'sales_change' => $this->calculatePercentageChange(
                $previousData['sales']['total'],
                $currentData['sales']['total']
            ),
            'expenses_change' => $this->calculatePercentageChange(
                $previousData['expenses']['total'],
                $currentData['expenses']['total']
            ),
            'profit_change' => $this->calculatePercentageChange(
                $previousData['profits']['net'],
                $currentData['profits']['net']
            ),
            'orders_change' => $this->calculatePercentageChange(
                $previousData['sales']['orders_count'],
                $currentData['sales']['orders_count']
            ),
        ];

        return view('admin.reports.period_comparison', compact(
            'currentData', 'previousData', 'comparison',
            'currentStart', 'currentEnd', 'previousStart', 'previousEnd'
        ));
    }

    /**
     * حساب النسبة المئوية للتغيير
     */
    private function calculatePercentageChange($oldValue, $newValue)
    {
        if ($oldValue == 0) {
            return $newValue > 0 ? 100 : 0;
        }

        return (($newValue - $oldValue) / $oldValue) * 100;
    }

    /**
     * جلب بيانات المخططات حسب الفترة المختارة
     */
    public function getFinancialChartData(Request $request)
    {
        $period = $request->get('period', 30); // عدد الأيام
        $type = $request->get('type', 'sales'); // نوع المخطط

        $endDate = Carbon::now();
        $startDate = Carbon::now()->subDays($period);

        if ($type === 'sales') {
            // بيانات مخطط المبيعات والمصروفات
            $data = $this->getSalesExpensesChartData($startDate, $endDate, $period);
        } else {
            // بيانات مخطط توزيع المصروفات
            $data = $this->getExpensesDistributionChartData($startDate, $endDate);
        }

        return response()->json($data);
    }

    /**
     * جلب بيانات مخطط المبيعات والمصروفات
     */
    private function getSalesExpensesChartData($startDate, $endDate, $period)
    {
        $categories = [];
        $salesData = [];
        $expensesData = [];

        if ($period <= 30) {
            // عرض البيانات يومياً
            for ($date = $startDate->copy(); $date <= $endDate; $date->addDay()) {
                $categories[] = $date->format('m/d');

                $dailySales = Order::whereDate('created_at', $date)
                    ->whereIn('status', ['completed', 'preparing'])
                    ->sum('total_amount');

                $dailyExpenses = Expense::whereDate('expense_date', $date)
                    ->sum('amount');

                $salesData[] = (float) $dailySales;
                $expensesData[] = (float) $dailyExpenses;
            }
        } else {
            // عرض البيانات شهرياً
            $months = ceil($period / 30);
            for ($i = $months - 1; $i >= 0; $i--) {
                $monthStart = Carbon::now()->subMonths($i)->startOfMonth();
                $monthEnd = Carbon::now()->subMonths($i)->endOfMonth();

                $categories[] = $monthStart->format('M Y');

                $monthlySales = Order::whereBetween('created_at', [$monthStart, $monthEnd])
                    ->whereIn('status', ['completed', 'preparing'])
                    ->sum('total_amount');

                $monthlyExpenses = Expense::whereBetween('expense_date', [$monthStart, $monthEnd])
                    ->sum('amount');

                $salesData[] = (float) $monthlySales;
                $expensesData[] = (float) $monthlyExpenses;
            }
        }

        return [
            'categories' => $categories,
            'sales' => $salesData,
            'expenses' => $expensesData
        ];
    }

    /**
     * جلب بيانات مخطط توزيع المصروفات
     */
    private function getExpensesDistributionChartData($startDate, $endDate)
    {
        $expensesByCategory = Expense::whereBetween('expense_date', [$startDate, $endDate])
            ->select('category', DB::raw('SUM(amount) as total'))
            ->groupBy('category')
            ->orderBy('total', 'desc')
            ->get();

        $labels = [];
        $values = [];

        foreach ($expensesByCategory as $expense) {
            $labels[] = $expense->category;
            $values[] = (float) $expense->total;
        }

        return [
            'labels' => $labels,
            'values' => $values
        ];
    }
}