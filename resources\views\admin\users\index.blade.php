@extends('layouts.admin')

@section('title', 'إدارة المستخدمين - نظام إدارة المطعم')

@section('page-title', 'إدارة المستخدمين')

@section('content')
<div class="mb-4">
    <div class="flex justify-between items-center mb-3">
        <h2 class="text-xl font-bold text-gray-800 dark:text-white">قائمة المستخدمين</h2>
        <a href="{{ route('admin.users.create') }}" class="bg-primary hover:bg-primary-dark text-white py-2 px-3 rounded-lg flex items-center text-sm">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مستخدم جديد</span>
        </a>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-3 py-2 rounded relative mb-3 text-sm" role="alert">
        <span class="block sm:inline">{{ session('success') }}</span>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded relative mb-3 text-sm" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <div class="flex items-center">
                <form action="{{ route('admin.users') }}" method="GET" class="flex items-center" id="search-form">
                    <input type="text" name="search" placeholder="بحث عن مستخدم..." value="{{ request('search') }}" class="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                    <button type="submit" class="bg-primary text-white rounded-lg px-2 py-1 mr-2 hover:bg-primary-dark text-sm">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
            <div class="flex items-center">
                <form action="{{ route('admin.users') }}" method="GET" class="flex items-center" id="filter-form">
                    <!-- إضافة حقل البحث المخفي للحفاظ على قيمة البحث عند التصفية -->
                    @if(request('search'))
                    <input type="hidden" name="search" value="{{ request('search') }}">
                    @endif
                    <select name="user_type" id="user_type" class="border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary ml-2">
                        <option value="">جميع المستخدمين</option>
                        <option value="admin" {{ request('user_type') == 'admin' ? 'selected' : '' }}>المديرين</option>
                        <option value="employee" {{ request('user_type') == 'employee' ? 'selected' : '' }}>الموظفين</option>
                        <option value="customer" {{ request('user_type') == 'customer' ? 'selected' : '' }}>العملاء</option>
                    </select>
                    <button type="submit" class="bg-primary text-white rounded-lg px-2 py-1 hover:bg-primary-dark text-sm">
                        <i class="fas fa-filter ml-1"></i>
                        <span>تصفية</span>
                    </button>
                </form>
            </div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 table-compact">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الاسم
                        </th>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            البريد الإلكتروني
                        </th>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم الهاتف
                        </th>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            نوع المستخدم
                        </th>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            تاريخ التسجيل
                        </th>
                        <th scope="col" class="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($users as $user)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-8 w-8">
                                    <div class="h-8 w-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold text-sm">
                                        {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
                                    </div>
                                </div>
                                <div class="mr-3">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $user->first_name }} {{ $user->last_name }}
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900 dark:text-white">{{ $user->email }}</div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <div class="text-sm text-gray-900 dark:text-white">{{ $user->phone }}</div>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $user->user_type == 'admin' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400' : '' }}
                                {{ $user->user_type == 'employee' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' : '' }}
                                {{ $user->user_type == 'customer' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : '' }}">
                                {{ $user->user_type == 'admin' ? 'مدير' : '' }}
                                {{ $user->user_type == 'employee' ? 'موظف' : '' }}
                                {{ $user->user_type == 'customer' ? 'عميل' : '' }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $user->is_active ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' }}">
                                {{ $user->is_active ? 'نشط' : 'غير نشط' }}
                            </span>
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            {{ $user->created_at->format('Y-m-d') }}
                        </td>
                        <td class="px-4 py-3 whitespace-nowrap text-left text-sm font-medium">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <a href="{{ route('admin.users.show', $user->user_id) }}" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('admin.users.edit', $user->user_id) }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                @if(Auth::id() != $user->user_id)
                                <form action="{{ route('admin.users.delete', $user->user_id) }}" method="POST" class="inline-block delete-user-form">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-4 py-3 text-center text-gray-500 dark:text-gray-400">
                            لا يوجد مستخدمين
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        <div class="px-4 py-3">
            {{ $users->links() }}
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأكيد حذف المستخدم
        const deleteForms = document.querySelectorAll('.delete-user-form');
        deleteForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                    this.submit();
                }
            });
        });
    });
</script>

@endsection