@extends('customer.layouts.app')

@section('title', 'فاتورة الطلب')

@push('scripts')
<!-- مكتبات PDF -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
@endpush

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Header Actions -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white">
                    <i class="fas fa-file-invoice-dollar text-primary ml-2"></i>
                    فاتورة الطلب
                </h1>
                <div class="flex space-x-3 space-x-reverse">
                    <button onclick="printInvoice()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200 shadow-md">
                        <i class="fas fa-print ml-2"></i>طباعة
                    </button>
                    <button onclick="downloadPDF()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-200 shadow-md">
                        <i class="fas fa-download ml-2"></i>تحميل PDF
                    </button>
                </div>
            </div>

            <!-- Invoice Card -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl overflow-hidden" id="invoice">
                <!-- Invoice Header -->
                <div class="bg-gradient-to-r from-primary to-blue-600 text-white p-8">
                    <div class="flex justify-between items-start">
                        <div>
                            <h2 class="text-4xl font-bold mb-2">Eat Hub</h2>
                        </div>
                        <div class="text-right">
                            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                                <h3 class="text-2xl font-bold mb-1">فاتورة #{{ $order->order_id }}</h3>
                                <p class="text-blue-100">{{ now()->format('Y/m/d') }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Body -->
                <div class="p-8">
                    <!-- Company & Invoice Info -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <!-- Company Info -->
                        <div class="space-y-4">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6">
                                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                                    <i class="fas fa-building text-primary ml-2"></i>
                                    معلومات المطعم
                                </h4>
                                <div class="space-y-2 text-gray-600 dark:text-gray-300">
                                    <p class="flex items-center">
                                        <i class="fas fa-map-marker-alt text-red-500 w-5 ml-2"></i>
                                        طرابلس، ليبيا
                                    </p>
                                    <p class="flex items-center">
                                        <i class="fas fa-phone text-green-500 w-5 ml-2"></i>
                                        +218 91 234 5678
                                    </p>
                                    <p class="flex items-center">
                                        <i class="fas fa-envelope text-blue-500 w-5 ml-2"></i>
                                        <EMAIL>
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Invoice Details -->
                        <div class="space-y-4">
                            <div class="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 rounded-xl p-6">
                                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                                    <i class="fas fa-info-circle text-primary ml-2"></i>
                                    تفاصيل الفاتورة
                                </h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-300">رقم الفاتورة:</span>
                                        <span class="font-bold text-gray-800 dark:text-white">INV-{{ $order->order_id }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-300">تاريخ الإصدار:</span>
                                        <span class="font-bold text-gray-800 dark:text-white">{{ now()->format('Y/m/d') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600 dark:text-gray-300">تاريخ الطلب:</span>
                                        <span class="font-bold text-gray-800 dark:text-white">{{ $order->created_at->format('Y/m/d H:i') }}</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600 dark:text-gray-300">حالة الطلب:</span>
                                        @if($order->status == 'completed')
                                            <span class="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">مكتمل</span>
                                        @elseif($order->status == 'pending')
                                            <span class="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 px-3 py-1 rounded-full text-sm font-medium">قيد التجهيز</span>
                                        @elseif($order->status == 'cancelled')
                                            <span class="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 px-3 py-1 rounded-full text-sm font-medium">ملغي</span>
                                        @else
                                            <span class="bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200 px-3 py-1 rounded-full text-sm font-medium">{{ $order->status }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer & Order Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                        <!-- Customer Info -->
                        <div class="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-gray-700 dark:to-gray-600 rounded-xl p-6">
                            <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-user text-green-600 ml-2"></i>
                                معلومات العميل
                            </h4>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <i class="fas fa-user-circle text-gray-400 w-5 ml-2"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">الاسم:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ $order->user->first_name ?? '' }} {{ $order->user->last_name ?? '' }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-gray-400 w-5 ml-2"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">البريد:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ $order->user->email ?? '' }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-phone text-gray-400 w-5 ml-2"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">الهاتف:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ $order->customer_phone ?? $order->user->phone ?? '' }}</span>
                                </div>
                                @if($order->delivery_address)
                                <div class="flex items-start">
                                    <i class="fas fa-map-marker-alt text-gray-400 w-5 ml-2 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">العنوان:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ $order->delivery_address }}</span>
                                </div>
                                @endif
                            </div>
                        </div>

                        <!-- Order Details -->
                        <div class="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-gray-700 dark:to-gray-600 rounded-xl p-6">
                            <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-shopping-bag text-purple-600 ml-2"></i>
                                تفاصيل الطلب
                            </h4>
                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <i class="fas fa-truck text-gray-400 w-5 ml-2"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">نوع الطلب:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        {{ $order->order_type == 'delivery' ? 'توصيل' : 'استلام من المطعم' }}
                                    </span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-credit-card text-gray-400 w-5 ml-2"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">طريقة الدفع:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">
                                        @switch($order->payment_method)
                                            @case('cash')
                                                نقداً
                                                @break
                                            @case('card')
                                                بطاقة ائتمان
                                                @break
                                            @case('wallet')
                                                محفظة إلكترونية
                                                @break
                                            @default
                                                غير محدد
                                        @endswitch
                                    </span>
                                </div>
                                @if($order->notes)
                                <div class="flex items-start">
                                    <i class="fas fa-sticky-note text-gray-400 w-5 ml-2 mt-1"></i>
                                    <span class="text-gray-600 dark:text-gray-300 ml-2">ملاحظات:</span>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ $order->notes }}</span>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="mb-8">
                        <h4 class="text-xl font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                            <i class="fas fa-utensils text-orange-600 ml-2"></i>
                            عناصر الطلب
                        </h4>
                        <div class="bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden">
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-600 dark:to-gray-700">
                                        <tr>
                                            <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-200 uppercase tracking-wider">العنصر</th>
                                            <th class="px-6 py-4 text-center text-sm font-bold text-gray-700 dark:text-gray-200 uppercase tracking-wider">الكمية</th>
                                            <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-200 uppercase tracking-wider">السعر الواحد</th>
                                            <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-200 uppercase tracking-wider">الإجمالي</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                                        @foreach($order->items as $index => $item)
                                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors duration-200 {{ $index % 2 == 0 ? 'bg-white dark:bg-gray-700' : 'bg-gray-50 dark:bg-gray-600' }}">
                                                <td class="px-6 py-4">
                                                    <div class="flex items-center">
                                                        <div class="w-12 h-12 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg flex items-center justify-center ml-4">
                                                            <i class="fas fa-utensils text-white text-sm"></i>
                                                        </div>
                                                        <div>
                                                            <div class="text-sm font-bold text-gray-900 dark:text-white">{{ $item->menuItem->name ?? 'عنصر محذوف' }}</div>
                                                            @if($item->menuItem && $item->menuItem->description)
                                                                <div class="text-xs text-gray-500 dark:text-gray-400">{{ Str::limit($item->menuItem->description, 50) }}</div>
                                                            @endif
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 text-center">
                                                    <span class="inline-flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full text-sm font-bold">
                                                        {{ $item->quantity }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-right">
                                                    <span class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($item->price, 2) }} د.ل</span>
                                                </td>
                                                <td class="px-6 py-4 text-right">
                                                    <span class="text-sm font-bold text-gray-900 dark:text-white">{{ number_format($item->price * $item->quantity, 2) }} د.ل</span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Summary -->
                    <div class="flex justify-end mb-8">
                        <div class="w-full max-w-md">
                            <div class="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-2xl p-6 shadow-lg">
                                <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-6 flex items-center">
                                    <i class="fas fa-calculator text-green-600 ml-2"></i>
                                    ملخص الدفع
                                </h4>
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                        <span class="text-gray-600 dark:text-gray-300">المجموع الفرعي:</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ number_format($subtotal, 2) }} د.ل</span>
                                    </div>
                                    <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                        <span class="text-gray-600 dark:text-gray-300">الضريبة (15%):</span>
                                        <span class="font-medium text-gray-800 dark:text-white">{{ number_format($tax, 2) }} د.ل</span>
                                    </div>
                                    @if($deliveryFee > 0)
                                        <div class="flex justify-between items-center py-2 border-b border-gray-200 dark:border-gray-600">
                                            <span class="text-gray-600 dark:text-gray-300">رسوم التوصيل:</span>
                                            <span class="font-medium text-gray-800 dark:text-white">{{ number_format($deliveryFee, 2) }} د.ل</span>
                                        </div>
                                    @endif
                                    <div class="flex justify-between items-center py-4 bg-gradient-to-r from-primary to-blue-600 rounded-lg px-4 text-white">
                                        <span class="text-lg font-bold">المجموع الإجمالي:</span>
                                        <span class="text-2xl font-bold">{{ number_format($total, 2) }} د.ل</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Message -->
                    <div class="text-center py-8 border-t border-gray-200 dark:border-gray-600">
                        <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-gray-700 dark:to-gray-600 rounded-xl p-6 max-w-md mx-auto">
                            <div class="flex items-center justify-center mb-4">
                                <div class="w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-heart text-white text-2xl"></i>
                                </div>
                            </div>
                            <h5 class="text-lg font-bold text-gray-800 dark:text-white mb-2">شكراً لاختياركم مطعم Eat Hub</h5>
                            <p class="text-gray-600 dark:text-gray-300">نتطلع لخدمتكم مرة أخرى قريباً</p>
                            <div class="flex justify-center space-x-4 space-x-reverse mt-4">
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                                <i class="fas fa-star text-yellow-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-center space-x-4 space-x-reverse mt-8">
                <a href="{{ route('customer.orders.show', $order->order_id) }}"
                   class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition duration-200 shadow-md flex items-center">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للطلب
                </a>
                <a href="{{ route('customer.orders') }}"
                   class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition duration-200 shadow-md flex items-center">
                    <i class="fas fa-list ml-2"></i>
                    جميع الطلبات
                </a>
            </div>
        </div>
    </div>
</div>

<style>
@media print {
    .btn, .card-header .col-auto {
        display: none !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .card-header {
        background-color: #007bff !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
}

.card {
    border: none;
    border-radius: 15px;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.badge {
    font-size: 0.8em;
}
</style>

<script>
function printInvoice() {
    window.print();
}

function downloadPDF() {
    // إضافة مؤشر التحميل
    const loadingDiv = document.createElement('div');
    loadingDiv.innerHTML = `
        <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p class="text-gray-700 dark:text-gray-300">جاري تحضير الفاتورة...</p>
            </div>
        </div>
    `;
    document.body.appendChild(loadingDiv);

    // إخفاء الأزرار أثناء التحميل
    const headerButtons = document.querySelector('.flex.space-x-3.space-x-reverse');
    if (headerButtons) {
        headerButtons.style.display = 'none';
    }

    // محاولة استخدام html2pdf أولاً
    if (typeof html2pdf !== 'undefined') {
        const element = document.getElementById('invoice');
        const opt = {
            margin: 0.5,
            filename: 'فاتورة_الطلب_{{ $order->order_id }}.pdf',
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            },
            jsPDF: {
                unit: 'in',
                format: 'a4',
                orientation: 'portrait'
            }
        };

        html2pdf().set(opt).from(element).save().then(() => {
            // إزالة مؤشر التحميل
            document.body.removeChild(loadingDiv);
            // إظهار الأزرار مرة أخرى
            if (headerButtons) {
                headerButtons.style.display = 'flex';
            }
        }).catch((error) => {
            console.error('خطأ في html2pdf:', error);
            fallbackToPrint();
        });
    }
    // محاولة استخدام jsPDF + html2canvas كبديل
    else if (typeof html2canvas !== 'undefined' && typeof jsPDF !== 'undefined') {
        const element = document.getElementById('invoice');

        html2canvas(element, {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            backgroundColor: '#ffffff'
        }).then(canvas => {
            const imgData = canvas.toDataURL('image/png');
            const pdf = new jsPDF.jsPDF('p', 'mm', 'a4');
            const imgWidth = 210;
            const pageHeight = 295;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            pdf.save('فاتورة_الطلب_{{ $order->order_id }}.pdf');

            // إزالة مؤشر التحميل
            document.body.removeChild(loadingDiv);
            // إظهار الأزرار مرة أخرى
            if (headerButtons) {
                headerButtons.style.display = 'flex';
            }
        }).catch((error) => {
            console.error('خطأ في jsPDF:', error);
            fallbackToPrint();
        });
    }
    // استخدام الطباعة كبديل أخير
    else {
        fallbackToPrint();
    }

    function fallbackToPrint() {
        // إزالة مؤشر التحميل
        document.body.removeChild(loadingDiv);
        // إظهار الأزرار مرة أخرى
        if (headerButtons) {
            headerButtons.style.display = 'flex';
        }
        // عرض رسالة وفتح نافذة الطباعة
        alert('سيتم فتح نافذة الطباعة. يمكنك اختيار "حفظ كـ PDF" من خيارات الطباعة.');
        window.print();
    }
}
</script>
@endsection
