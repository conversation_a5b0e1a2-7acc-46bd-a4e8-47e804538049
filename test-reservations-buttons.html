<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>اختبار أزرار الحجوزات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">اختبار أزرار الحجوزات</h1>
        
        <!-- حجز تجريبي -->
        <div class="reservation-card bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="flex items-center space-x-4 space-x-reverse mb-2">
                            <h3 class="text-lg font-bold text-gray-800">حجز #R1001</h3>
                            <span class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">قادم</span>
                        </div>
                        <p class="text-gray-600 text-sm">تم الحجز في 15 ديسمبر 2024</p>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold text-gray-800">غداً 7:00 م</p>
                        <p class="text-gray-600 text-sm">19 ديسمبر 2024</p>
                    </div>
                </div>

                <!-- تفاصيل الحجز -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-chair text-orange-500 text-xl ml-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">طاولة #8</p>
                                <p class="text-gray-600 text-sm">منطقة النافذة</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-users text-orange-500 text-xl ml-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">4 أشخاص</p>
                                <p class="text-gray-600 text-sm">عائلة</p>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <i class="fas fa-clock text-orange-500 text-xl ml-3"></i>
                            <div>
                                <p class="font-medium text-gray-800">ساعتان</p>
                                <p class="text-gray-600 text-sm">7:00 - 9:00 م</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إجراءات الحجز -->
                <div class="border-t border-gray-200 pt-4">
                    <div class="flex justify-between items-center">
                        <div class="flex space-x-3 space-x-reverse">
                            <button class="text-orange-500 hover:text-orange-600 text-sm font-medium">
                                <i class="fas fa-edit ml-1"></i>تعديل الحجز
                            </button>
                            <button class="text-orange-500 hover:text-orange-600 text-sm font-medium">
                                <i class="fas fa-share ml-1"></i>مشاركة التفاصيل
                            </button>
                            <button class="text-red-600 hover:text-red-700 text-sm font-medium">
                                <i class="fas fa-times ml-1"></i>إلغاء الحجز
                            </button>
                        </div>
                        <button class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm transition">
                            <i class="fas fa-directions ml-1"></i>
                            الاتجاهات
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- حجز مكتمل -->
        <div class="reservation-card bg-white rounded-lg shadow-md overflow-hidden mb-6">
            <div class="p-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <div class="flex items-center space-x-4 space-x-reverse mb-2">
                            <h3 class="text-lg font-bold text-gray-800">حجز #R1000</h3>
                            <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">مكتمل</span>
                        </div>
                        <p class="text-gray-600 text-sm">تم الحجز في 10 ديسمبر 2024</p>
                    </div>
                    <div class="text-right">
                        <p class="text-xl font-bold text-gray-800">15 ديسمبر 2024</p>
                        <p class="text-gray-600 text-sm">6:30 - 8:30 م</p>
                    </div>
                </div>

                <!-- إجراءات الحجز -->
                <div class="border-t border-gray-200 pt-4">
                    <div class="flex space-x-3 space-x-reverse">
                        <button class="text-orange-500 hover:text-orange-600 text-sm font-medium">
                            <i class="fas fa-redo ml-1"></i>حجز مماثل
                        </button>
                        <button class="text-orange-500 hover:text-orange-600 text-sm font-medium">
                            <i class="fas fa-star ml-1"></i>تقييم التجربة
                        </button>
                        <button class="text-orange-500 hover:text-orange-600 text-sm font-medium">
                            <i class="fas fa-download ml-1"></i>تحميل التأكيد
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل الاختبار -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">سجل الاختبار</h2>
            <div id="test-log" class="space-y-2 max-h-64 overflow-y-auto">
                <!-- سيتم إضافة رسائل الاختبار هنا -->
            </div>
        </div>
    </div>

    <script>
    // إعداد أزرار الإجراءات
    function setupActionButtons() {
        // البحث عن جميع الأزرار وإضافة المستمعات حسب النص
        document.querySelectorAll('button').forEach(btn => {
            const buttonText = btn.textContent.trim();
            
            if (buttonText.includes('تعديل الحجز')) {
                btn.addEventListener('click', function() {
                    const reservationCard = this.closest('.reservation-card');
                    const reservationId = getReservationId(reservationCard);
                    logTest(`تم النقر على تعديل الحجز: ${reservationId}`);
                    editReservation(reservationId);
                });
            }
            
            else if (buttonText.includes('مشاركة التفاصيل')) {
                btn.addEventListener('click', function() {
                    const reservationCard = this.closest('.reservation-card');
                    logTest('تم النقر على مشاركة التفاصيل');
                    shareReservation(reservationCard);
                });
            }
            
            else if (buttonText.includes('إلغاء الحجز')) {
                btn.addEventListener('click', function() {
                    const reservationCard = this.closest('.reservation-card');
                    const reservationId = getReservationId(reservationCard);
                    logTest(`تم النقر على إلغاء الحجز: ${reservationId}`);
                    cancelReservation(reservationId);
                });
            }
            
            else if (buttonText.includes('الاتجاهات')) {
                btn.addEventListener('click', function() {
                    logTest('تم النقر على الاتجاهات');
                    getDirections();
                });
            }
            
            else if (buttonText.includes('حجز مماثل')) {
                btn.addEventListener('click', function() {
                    const reservationCard = this.closest('.reservation-card');
                    logTest('تم النقر على حجز مماثل');
                    repeatReservation(reservationCard);
                });
            }
            
            else if (buttonText.includes('تقييم التجربة')) {
                btn.addEventListener('click', function() {
                    const reservationCard = this.closest('.reservation-card');
                    const reservationId = getReservationId(reservationCard);
                    logTest(`تم النقر على تقييم التجربة: ${reservationId}`);
                    rateExperience(reservationId);
                });
            }
            
            else if (buttonText.includes('تحميل التأكيد')) {
                btn.addEventListener('click', function() {
                    const reservationCard = this.closest('.reservation-card');
                    const reservationId = getReservationId(reservationCard);
                    logTest(`تم النقر على تحميل التأكيد: ${reservationId}`);
                    downloadConfirmation(reservationId);
                });
            }
        });
    }

    // الحصول على معرف الحجز من البطاقة
    function getReservationId(card) {
        const titleElement = card.querySelector('h3');
        if (titleElement) {
            const match = titleElement.textContent.match(/#(\w+)/);
            return match ? match[1] : null;
        }
        return null;
    }

    // تسجيل رسائل الاختبار
    function logTest(message) {
        const log = document.getElementById('test-log');
        const time = new Date().toLocaleTimeString('ar-SA');
        const entry = document.createElement('div');
        entry.className = 'text-sm text-gray-600 border-b border-gray-100 pb-1';
        entry.innerHTML = `<span class="text-blue-600">[${time}]</span> ${message}`;
        log.appendChild(entry);
        log.scrollTop = log.scrollHeight;
    }

    // الوظائف التجريبية
    function editReservation(reservationId) {
        logTest(`✅ وظيفة التعديل تعمل للحجز: ${reservationId}`);
        alert(`سيتم توجيهك لتعديل الحجز: ${reservationId}`);
    }

    function shareReservation(card) {
        const reservationId = getReservationId(card);
        const shareText = `حجزي في المطعم - رقم الحجز: ${reservationId}`;
        logTest('✅ وظيفة المشاركة تعمل');
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(shareText).then(() => {
                alert('تم نسخ تفاصيل الحجز إلى الحافظة');
            });
        } else {
            alert('تفاصيل الحجز: ' + shareText);
        }
    }

    function cancelReservation(reservationId) {
        logTest(`✅ وظيفة الإلغاء تعمل للحجز: ${reservationId}`);
        if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟')) {
            alert('تم إلغاء الحجز (تجريبي)');
        }
    }

    function getDirections() {
        logTest('✅ وظيفة الاتجاهات تعمل');
        const googleMapsUrl = 'https://www.google.com/maps/search/?api=1&query=32.8872,13.1913';
        window.open(googleMapsUrl, '_blank');
    }

    function repeatReservation(card) {
        logTest('✅ وظيفة الحجز المماثل تعمل');
        alert('سيتم توجيهك لإنشاء حجز مماثل');
    }

    function rateExperience(reservationId) {
        logTest(`✅ وظيفة التقييم تعمل للحجز: ${reservationId}`);
        alert('سيتم فتح نافذة التقييم');
    }

    function downloadConfirmation(reservationId) {
        logTest(`✅ وظيفة التحميل تعمل للحجز: ${reservationId}`);
        alert('سيتم تحميل تأكيد الحجز');
    }

    // تشغيل الإعداد عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        logTest('🚀 تم تحميل الصفحة وإعداد الأزرار');
        setupActionButtons();
    });
    </script>
</body>
</html>
