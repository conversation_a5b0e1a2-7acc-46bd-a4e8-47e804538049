<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'لوحة تحكم Eat Hub - نظام إدارة المطعم')</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // نظام ألوان متطور ومبدع للمطعم
                        primary: {
                            50: '#FFF7ED',
                            100: '#FFEDD5',
                            200: '#FED7AA',
                            300: '#FDBA74',
                            400: '#FB923C',
                            500: '#F97316',  // البرتقالي الأساسي
                            600: '#EA580C',
                            700: '#C2410C',
                            800: '#9A3412',
                            900: '#7C2D12',
                        },
                        secondary: {
                            50: '#F0FDF4',
                            100: '#DCFCE7',
                            200: '#BBF7D0',
                            300: '#86EFAC',
                            400: '#4ADE80',
                            500: '#22C55E',  // الأخضر الأساسي
                            600: '#16A34A',
                            700: '#15803D',
                            800: '#166534',
                            900: '#14532D',
                        },
                        accent: {
                            50: '#FEFCE8',
                            100: '#FEF9C3',
                            200: '#FEF08A',
                            300: '#FDE047',
                            400: '#FACC15',
                            500: '#EAB308',  // الأصفر الأساسي
                            600: '#CA8A04',
                            700: '#A16207',
                            800: '#854D0E',
                            900: '#713F12',
                        },
                        royal: {
                            50: '#F8FAFC',
                            100: '#F1F5F9',
                            200: '#E2E8F0',
                            300: '#CBD5E1',
                            400: '#94A3B8',
                            500: '#64748B',  // الرمادي الملكي
                            600: '#475569',
                            700: '#334155',
                            800: '#1E293B',
                            900: '#0F172A',
                        },
                        luxury: {
                            50: '#FDF4FF',
                            100: '#FAE8FF',
                            200: '#F5D0FE',
                            300: '#F0ABFC',
                            400: '#E879F9',
                            500: '#D946EF',  // البنفسجي الفاخر
                            600: '#C026D3',
                            700: '#A21CAF',
                            800: '#86198F',
                            900: '#701A75',
                        },
                        ocean: {
                            50: '#F0F9FF',
                            100: '#E0F2FE',
                            200: '#BAE6FD',
                            300: '#7DD3FC',
                            400: '#38BDF8',
                            500: '#0EA5E9',  // الأزرق المحيطي
                            600: '#0284C7',
                            700: '#0369A1',
                            800: '#075985',
                            900: '#0C4A6E',
                        },
                        sunset: {
                            50: '#FEF2F2',
                            100: '#FEE2E2',
                            200: '#FECACA',
                            300: '#FCA5A5',
                            400: '#F87171',
                            500: '#EF4444',  // الأحمر الغروبي
                            600: '#DC2626',
                            700: '#B91C1C',
                            800: '#991B1B',
                            900: '#7F1D1D',
                        },
                        forest: {
                            50: '#F7FDF7',
                            100: '#ECFDF5',
                            200: '#D1FAE5',
                            300: '#A7F3D0',
                            400: '#6EE7B7',
                            500: '#34D399',  // الأخضر الغابي
                            600: '#059669',
                            700: '#047857',
                            800: '#065F46',
                            900: '#064E3B',
                        },
                        gold: {
                            50: '#FFFBEB',
                            100: '#FEF3C7',
                            200: '#FDE68A',
                            300: '#FCD34D',
                            400: '#FBBF24',
                            500: '#F59E0B',  // الذهبي
                            600: '#D97706',
                            700: '#B45309',
                            800: '#92400E',
                            900: '#78350F',
                        },
                        // ألوان خاصة للمطعم
                        spice: '#D2691E',        // لون التوابل
                        cream: '#F5F5DC',        // لون الكريمة
                        coffee: '#6F4E37',       // لون القهوة
                        mint: '#98FB98',         // لون النعناع
                        tomato: '#FF6347',       // لون الطماطم
                        olive: '#808000',        // لون الزيتون
                        wine: '#722F37',         // لون النبيذ
                        honey: '#FFB347',        // لون العسل
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                }
            }
        };

        // تحميل الثيم المحفوظ من localStorage
        const savedTheme = localStorage.getItem('theme_preference') || 'auto';
        const effectiveTheme = localStorage.getItem('effective_theme') || 'light';

        // تطبيق الثيم فوراً لتجنب الوميض
        if (savedTheme === 'dark' || (savedTheme === 'auto' && effectiveTheme === 'dark')) {
            document.documentElement.classList.add('dark');
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.css" rel="stylesheet">
    <link href="{{ asset('css/admin.css') }}" rel="stylesheet">
    <link href="{{ asset('css/theme.css') }}" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Cairo', sans-serif;
        }

        /* تأثيرات الانتقال */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* تأثير الانتقال للقائمة الجانبية */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* تأثير الانتقال للبطاقات */
        .card-hover {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* تخصيص شريط التمرير */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .dark ::-webkit-scrollbar-track {
            background: #1f2937;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        .dark ::-webkit-scrollbar-thumb {
            background: #4b5563;
        }

        .dark ::-webkit-scrollbar-thumb:hover {
            background: #6b7280;
        }

        /* تخصيص الجداول */
        .table-responsive {
            overflow-x: auto;
        }

        /* تخصيص الطاولات */
        .table-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }

        .table-item {
            aspect-ratio: 1;
            border-radius: 0.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }

        .dark .table-item {
            border-color: #374151;
        }

        .table-available {
            background-color: rgba(74, 222, 128, 0.2);
            border: 2px solid #4ade80;
        }

        .table-occupied {
            background-color: rgba(248, 113, 113, 0.2);
            border: 2px solid #f87171;
        }

        .table-reserved {
            background-color: rgba(251, 191, 36, 0.2);
            border: 2px solid #fbbf24;
        }

        .table-number {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .table-status {
            font-size: 0.875rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            margin-top: 0.5rem;
        }

        /* تحسينات التخطيط المدمج */
        .compact-layout {
            padding: 0.5rem !important;
            margin: 0.25rem !important;
        }

        .compact-spacing {
            gap: 0.5rem !important;
        }

        .compact-card {
            padding: 0.75rem !important;
        }

        .compact-grid {
            gap: 0.75rem !important;
        }

        /* تحسينات النصوص المدمجة */
        .compact-text {
            font-size: 0.875rem !important;
            line-height: 1.25rem !important;
        }

        .compact-text-sm {
            font-size: 0.75rem !important;
            line-height: 1rem !important;
        }

        /* تحسينات الأزرار المدمجة */
        .btn-compact {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.875rem !important;
        }

        /* تحسينات الجداول المدمجة */
        .table-compact th,
        .table-compact td {
            padding: 0.5rem !important;
            font-size: 0.875rem !important;
        }

        /* تحسينات الهوامش المدمجة */
        .space-y-compact > * + * {
            margin-top: 0.5rem !important;
        }

        .mb-compact {
            margin-bottom: 0.5rem !important;
        }

        .p-compact {
            padding: 0.5rem !important;
        }

        /* تحسينات زر الوضع المظلم */
        .theme-toggle {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .theme-toggle:active {
            transform: scale(0.95);
        }

        .theme-icon {
            transition: all 0.3s ease;
        }

        /* ألوان الأيقونات */
        .fa-moon {
            color: #fbbf24 !important;
        }

        .fa-sun {
            color: #f59e0b !important;
        }

        .dark .fa-sun {
            color: #fbbf24 !important;
        }

        /* تأثير النقر */
        .theme-toggle::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s, height 0.3s;
            pointer-events: none;
        }

        .theme-toggle:active::after {
            width: 60px;
            height: 60px;
        }

        /* إصلاح مشكلة التكرار في الشريط الجانبي */
        #mobileMenu {
            transform: translateX(-100%);
        }

        /* التأكد من عدم تداخل العناصر */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* ضمان ظهور الشريط الجانبي على الشاشات الكبيرة */
        @media (min-width: 768px) {
            #sidebar {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }
            #mobileMenu {
                display: none !important;
            }
        }

        /* إخفاء الشريط الجانبي على الشاشات الصغيرة */
        @media (max-width: 767px) {
            #sidebar {
                display: none !important;
            }
            #mobileMenu {
                display: block !important;
            }
        }

        /* إعدادات افتراضية للشريط الجانبي */
        #sidebar {
            display: flex !important;
            flex-direction: column !important;
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            height: 100vh !important;
            width: 16rem !important;
            z-index: 20 !important;
        }

        /* تأكد من ظهور الشريط الجانبي */
        body #sidebar {
            display: flex !important;
        }
    </style>
    @yield('styles')
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="flex h-screen overflow-hidden">
        <!-- القائمة الجانبية للشاشات الكبيرة -->
        @include('includes.sidebar')

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col md:mr-64 min-h-screen">
            <!-- الهيدر -->
            @include('includes.header')

            <!-- المحتوى الرئيسي -->
            <main class="flex-1 p-2 bg-gray-50 dark:bg-gray-900 overflow-y-auto">
                <div class="w-full max-w-full h-full">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- سكريبت الصفحة -->
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.js"></script>
    <script src="{{ asset('js/simple-dark-mode-fix.js') }}"></script>
    <script>
        // تبديل القائمة الجانبية للجوال
        document.addEventListener('DOMContentLoaded', function() {
            // التأكد من ظهور الشريط الجانبي على الشاشات الكبيرة
            function ensureSidebarVisibility() {
                const sidebar = document.getElementById('sidebar');
                const mobileMenu = document.getElementById('mobileMenu');

                if (window.innerWidth >= 768) {
                    if (sidebar) {
                        sidebar.style.display = 'flex';
                        sidebar.style.visibility = 'visible';
                        sidebar.style.opacity = '1';
                        sidebar.style.position = 'fixed';
                        sidebar.style.top = '0';
                        sidebar.style.right = '0';
                        sidebar.style.height = '100vh';
                        sidebar.style.width = '16rem';
                        sidebar.style.zIndex = '20';
                    }
                    if (mobileMenu) {
                        mobileMenu.style.display = 'none';
                    }
                } else {
                    if (sidebar) {
                        sidebar.style.display = 'none';
                    }
                    if (mobileMenu) {
                        mobileMenu.style.display = 'block';
                    }
                }
            }

            // تطبيق الإعدادات عند تحميل الصفحة
            ensureSidebarVisibility();

            // تطبيق الإعدادات عند تغيير حجم الشاشة
            window.addEventListener('resize', ensureSidebarVisibility);

            // تطبيق الإعدادات مرة أخرى بعد تحميل كامل للصفحة
            window.addEventListener('load', ensureSidebarVisibility);

            // تحديث عنوان الصفحة حسب الرابط النشط
            updatePageTitle();

            const sidebarToggle = document.getElementById('sidebarToggle');
            const mobileMenuElement = document.getElementById('mobileMenu');
            const closeMobileMenu = document.getElementById('closeMobileMenu');

            if (sidebarToggle && mobileMenuElement) {
                let overlay = document.getElementById('overlay');

                // إنشاء طبقة التغطية إذا لم تكن موجودة
                if (!overlay) {
                    overlay = document.createElement('div');
                    overlay.id = 'overlay';
                    overlay.className = 'fixed inset-0 bg-black bg-opacity-50 z-20 hidden';
                    document.body.appendChild(overlay);
                }

                sidebarToggle.addEventListener('click', function() {
                    mobileMenuElement.classList.remove('-translate-x-full');
                    overlay.classList.remove('hidden');
                });

                if (closeMobileMenu) {
                    closeMobileMenu.addEventListener('click', function() {
                        mobileMenuElement.classList.add('-translate-x-full');
                        overlay.classList.add('hidden');
                    });
                }

                // إضافة مستمع حدث للنقر على طبقة التغطية
                overlay.addEventListener('click', function() {
                    mobileMenuElement.classList.add('-translate-x-full');
                    overlay.classList.add('hidden');
                });
            }

            // تبديل قائمة المستخدم
            const userMenuButton = document.getElementById('userMenuBtn');
            const userMenu = document.getElementById('userMenu');

            if (userMenuButton && userMenu) {
                userMenuButton.addEventListener('click', function() {
                    userMenu.classList.toggle('hidden');
                });

                // إخفاء القائمة عند النقر خارجها
                document.addEventListener('click', function(event) {
                    if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
                        userMenu.classList.add('hidden');
                    }
                });
            }

            // تبديل قائمة الإشعارات (فقط في الهيدر)
            const notificationButton = document.getElementById('notificationButton');
            const notificationsMenu = document.getElementById('notificationsMenu');

            if (notificationButton && notificationsMenu) {
                notificationButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    notificationsMenu.classList.toggle('hidden');

                    // تحميل الإشعارات عند فتح القائمة
                    if (!notificationsMenu.classList.contains('hidden')) {
                        loadNotifications();
                    }
                });

                // إخفاء القائمة عند النقر خارجها
                document.addEventListener('click', function(event) {
                    if (!notificationButton.contains(event.target) && !notificationsMenu.contains(event.target)) {
                        notificationsMenu.classList.add('hidden');
                    }
                });
            }

            // التأكد من أن روابط الإشعارات في الشريط الجانبي تعمل بشكل طبيعي
            const sidebarNotificationLinks = document.querySelectorAll('.sidebar-notification-link, .mobile-sidebar-notification-link');
            sidebarNotificationLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // السماح للرابط بالعمل بشكل طبيعي
                    // لا نمنع السلوك الافتراضي هنا
                });
            });

            // دالة لتحميل الإشعارات
            function loadNotifications() {
                const notificationsList = document.getElementById('notificationsList');
                if (!notificationsList) return;

                // عرض رسالة التحميل
                notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>جاري تحميل الإشعارات...</p></div>';

                // طلب الإشعارات من الخادم
                fetch('/api/notifications/latest')
                    .then(response => response.json())
                    .then(data => {
                        // تحديث عدد الإشعارات غير المقروءة
                        const count = data.unread_count || 0;
                        const notificationCount = document.getElementById('notificationCount');
                        const notificationBadge = document.getElementById('notification-badge');
                        const mobileNotificationBadge = document.getElementById('mobile-notification-badge');

                        if (notificationCount) notificationCount.textContent = count;
                        if (notificationBadge) notificationBadge.textContent = count;
                        if (mobileNotificationBadge) mobileNotificationBadge.textContent = count;

                        // عرض الإشعارات
                        if (data.notifications && data.notifications.length > 0) {
                            let html = '';

                            data.notifications.forEach(notification => {
                                html += `
                                <div class="px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 border-b border-gray-200 dark:border-gray-700">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 mt-1">
                                            <span class="w-2 h-2 bg-primary rounded-full inline-block ${notification.is_read ? 'opacity-0' : ''}"></span>
                                        </div>
                                        <div class="mr-3 flex-1">
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">${notification.title || 'إشعار جديد'}</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">${notification.message}</p>
                                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">${formatDate(notification.created_at)}</p>
                                        </div>
                                    </div>
                                </div>`;
                            });

                            notificationsList.innerHTML = html;
                        } else {
                            notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>لا توجد إشعارات جديدة</p></div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading notifications:', error);
                        if (notificationsList) {
                            notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>حدث خطأ أثناء تحميل الإشعارات</p></div>';
                        }
                    });
            }

            // دالة لتنسيق التاريخ
            function formatDate(dateString) {
                const date = new Date(dateString);
                const now = new Date();
                const diffMs = now - date;
                const diffSec = Math.floor(diffMs / 1000);
                const diffMin = Math.floor(diffSec / 60);
                const diffHour = Math.floor(diffMin / 60);
                const diffDay = Math.floor(diffHour / 24);

                if (diffSec < 60) {
                    return 'منذ لحظات';
                } else if (diffMin < 60) {
                    return `منذ ${diffMin} دقيقة`;
                } else if (diffHour < 24) {
                    return `منذ ${diffHour} ساعة`;
                } else if (diffDay < 30) {
                    return `منذ ${diffDay} يوم`;
                } else {
                    return date.toLocaleDateString('ar-LY');
                }
            }

            // تحميل عدد الإشعارات غير المقروءة عند تحميل الصفحة
            fetch('/api/notifications/count')
                .then(response => response.json())
                .then(data => {
                    const count = data.count || 0;
                    const notificationCount = document.getElementById('notificationCount');
                    const notificationBadge = document.getElementById('notification-badge');
                    const mobileNotificationBadge = document.getElementById('mobile-notification-badge');

                    if (notificationCount) notificationCount.textContent = count;
                    if (notificationBadge) notificationBadge.textContent = count;
                    if (mobileNotificationBadge) mobileNotificationBadge.textContent = count;
                })
                .catch(error => {
                    console.error('Error loading notification count:', error);
                });

            // تبديل القائمة الجانبية على الشاشات الكبيرة
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                const mainContent = document.querySelector('.flex-1');

                // حفظ حالة القائمة الجانبية في التخزين المحلي
                const sidebarState = localStorage.getItem('sidebarState');
                if (sidebarState === 'collapsed') {
                    sidebar.classList.add('md:w-20');
                    sidebar.classList.remove('md:w-64');
                    if (mainContent) {
                        mainContent.classList.add('md:mr-20');
                        mainContent.classList.remove('md:mr-64');
                    }

                    // إخفاء النصوص في القائمة الجانبية
                    const navTexts = sidebar.querySelectorAll('.nav-link span');
                    navTexts.forEach(text => {
                        text.classList.add('md:hidden');
                    });
                }

                // إضافة زر لتبديل القائمة الجانبية على الشاشات الكبيرة
                const sidebarCollapseBtn = document.createElement('button');
                sidebarCollapseBtn.className = 'hidden md:flex items-center justify-center p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 absolute top-4 -left-4 shadow-md';
                sidebarCollapseBtn.innerHTML = '<i class="fas fa-chevron-right"></i>';
                sidebar.style.position = 'relative';
                sidebar.appendChild(sidebarCollapseBtn);

                sidebarCollapseBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('md:w-64');
                    sidebar.classList.toggle('md:w-20');
                    if (mainContent) {
                        mainContent.classList.toggle('md:mr-64');
                        mainContent.classList.toggle('md:mr-20');
                    }

                    // تبديل أيقونة الزر
                    const icon = this.querySelector('i');
                    if (icon.classList.contains('fa-chevron-right')) {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-left');
                        localStorage.setItem('sidebarState', 'collapsed');

                        // إخفاء النصوص في القائمة الجانبية
                        const navTexts = sidebar.querySelectorAll('.nav-link span');
                        navTexts.forEach(text => {
                            text.classList.add('md:hidden');
                        });
                    } else {
                        icon.classList.remove('fa-chevron-left');
                        icon.classList.add('fa-chevron-right');
                        localStorage.setItem('sidebarState', 'expanded');

                        // إظهار النصوص في القائمة الجانبية
                        const navTexts = sidebar.querySelectorAll('.nav-link span');
                        navTexts.forEach(text => {
                            text.classList.remove('md:hidden');
                        });
                    }
                });
            }

            // تبديل وضع الظلام - محسن
            function initDarkMode() {
                const darkModeToggle = document.getElementById('darkModeToggle');
                const themeIcon = darkModeToggle?.querySelector('.theme-icon');

                // تحديث أيقونة الزر
                function updateThemeIcon() {
                    if (themeIcon) {
                        if (document.documentElement.classList.contains('dark')) {
                            themeIcon.classList.remove('fa-moon');
                            themeIcon.classList.add('fa-sun');
                        } else {
                            themeIcon.classList.remove('fa-sun');
                            themeIcon.classList.add('fa-moon');
                        }
                    }
                }

                // تحقق من حالة الوضع المظلم المحفوظة
                const savedTheme = localStorage.getItem('darkMode');
                if (savedTheme === 'enabled') {
                    document.documentElement.classList.add('dark');
                } else if (savedTheme === 'disabled') {
                    document.documentElement.classList.remove('dark');
                } else {
                    // إذا لم يكن هناك إعداد محفوظ، استخدم إعداد النظام
                    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                        document.documentElement.classList.add('dark');
                        localStorage.setItem('darkMode', 'enabled');
                    }
                }

                // تحديث الأيقونة عند التحميل
                updateThemeIcon();

                // إضافة مستمع للنقر
                if (darkModeToggle) {
                    darkModeToggle.addEventListener('click', function() {
                        document.documentElement.classList.toggle('dark');

                        // حفظ حالة الوضع المظلم في التخزين المحلي
                        if (document.documentElement.classList.contains('dark')) {
                            localStorage.setItem('darkMode', 'enabled');
                            console.log('🌙 تم تفعيل الوضع المظلم');
                        } else {
                            localStorage.setItem('darkMode', 'disabled');
                            console.log('☀️ تم تفعيل الوضع العادي');
                        }

                        // تحديث الأيقونة
                        updateThemeIcon();

                        // إجبار تحديث المخططات إذا كانت موجودة
                        setTimeout(function() {
                            if (typeof forceUpdateCharts === 'function') {
                                forceUpdateCharts();
                            }
                        }, 100);
                    });
                }

                // مراقبة تغيير إعدادات النظام
                if (window.matchMedia) {
                    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                        if (!localStorage.getItem('darkMode')) {
                            if (e.matches) {
                                document.documentElement.classList.add('dark');
                            } else {
                                document.documentElement.classList.remove('dark');
                            }
                            updateThemeIcon();
                        }
                    });
                }
            }

            // تشغيل دالة الوضع المظلم
            initDarkMode();
        });

        // دالة تحديث عنوان الصفحة
        function updatePageTitle() {
            const currentPath = window.location.pathname;
            const pageTitle = document.querySelector('h1');
            const pageSubtitle = document.querySelector('h1').nextElementSibling;

            // تحديد العنوان والوصف حسب المسار
            let title = 'لوحة التحكم';
            let subtitle = 'مرحباً بك في نظام إدارة المطعم';

            if (currentPath.includes('/users')) {
                title = 'إدارة المستخدمين';
                subtitle = 'إدارة حسابات المستخدمين والموظفين';
            } else if (currentPath.includes('/menu')) {
                title = 'قائمة الطعام';
                subtitle = 'إدارة عناصر القائمة والأصناف';
            } else if (currentPath.includes('/inventory')) {
                title = 'إدارة المخزون';
                subtitle = 'متابعة المكونات والمواد الخام';
            } else if (currentPath.includes('/orders')) {
                title = 'إدارة الطلبات';
                subtitle = 'متابعة ومعالجة طلبات العملاء';
            } else if (currentPath.includes('/expenses')) {
                title = 'المصروفات';
                subtitle = 'تتبع وإدارة مصروفات المطعم';
            } else if (currentPath.includes('/reports')) {
                title = 'التقارير';
                subtitle = 'تقارير مالية وإحصائيات مفصلة';
            } else if (currentPath.includes('/notifications')) {
                title = 'الإشعارات';
                subtitle = 'إدارة وإرسال الإشعارات';
            } else if (currentPath.includes('/settings')) {
                title = 'الإعدادات';
                subtitle = 'إعدادات النظام والمطعم';
            }

            // تحديث العنوان في الصفحة
            if (pageTitle) {
                pageTitle.textContent = title;
            }
            if (pageSubtitle) {
                pageSubtitle.textContent = subtitle;
            }
        }
    </script>
    @yield('scripts')
</body>
</html>
