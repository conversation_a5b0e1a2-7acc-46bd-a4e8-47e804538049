@extends('layouts.admin')

@section('title', 'تقرير المصروفات الشهري')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير المصروفات الشهري</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">عرض تقرير المصروفات الشهري للسنة الحالية</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.expenses') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للمصروفات</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">المصروفات الشهرية ({{ now()->year }})</h3>
        </div>
        <div class="relative" style="height: 350px;">
            <canvas id="monthlyExpensesChart"></canvas>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">المصروفات حسب الفئة</h3>
        </div>
        <div class="relative" style="height: 350px;">
            <canvas id="categoryExpensesChart"></canvas>
        </div>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">تفاصيل المصروفات الشهرية</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
                <tr class="bg-gray-50 dark:bg-gray-700">
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الشهر
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        إجمالي المصروفات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($months as $index => $month)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ $month }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ number_format($totals[$index], 2) }} ر.س
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">تفاصيل المصروفات حسب الفئة</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
                <tr class="bg-gray-50 dark:bg-gray-700">
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        الفئة
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        إجمالي المصروفات
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($categoryExpenses as $expense)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ $expense->category }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                        {{ number_format($expense->total, 2) }} ر.س
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // بيانات الرسم البياني للمصروفات الشهرية
        const monthlyData = {
            labels: @json($months),
            datasets: [{
                label: 'المصروفات الشهرية (ر.س)',
                data: @json($totals),
                backgroundColor: 'rgba(59, 130, 246, 0.2)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 2,
                tension: 0.3,
                fill: true
            }]
        };

        // إعدادات الرسم البياني للمصروفات الشهرية
        const monthlyConfig = {
            type: 'line',
            data: monthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ر.س';
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        };

        // إنشاء الرسم البياني للمصروفات الشهرية
        new Chart(document.getElementById('monthlyExpensesChart'), monthlyConfig);

        // بيانات الرسم البياني للمصروفات حسب الفئة
        const categoryData = {
            labels: @json($categoryExpenses->pluck('category')),
            datasets: [{
                label: 'المصروفات حسب الفئة (ر.س)',
                data: @json($categoryExpenses->pluck('total')),
                backgroundColor: [
                    'rgba(59, 130, 246, 0.7)',
                    'rgba(16, 185, 129, 0.7)',
                    'rgba(245, 158, 11, 0.7)',
                    'rgba(239, 68, 68, 0.7)',
                    'rgba(139, 92, 246, 0.7)',
                ],
                borderWidth: 1
            }]
        };

        // إعدادات الرسم البياني للمصروفات حسب الفئة
        const categoryConfig = {
            type: 'doughnut',
            data: categoryData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += parseFloat(context.raw).toLocaleString() + ' ر.س';
                                return label;
                            }
                        }
                    }
                }
            }
        };

        // إنشاء الرسم البياني للمصروفات حسب الفئة
        new Chart(document.getElementById('categoryExpensesChart'), categoryConfig);
    });
</script>
@endsection
