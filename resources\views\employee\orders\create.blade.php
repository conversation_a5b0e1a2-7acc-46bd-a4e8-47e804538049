@extends('employee.layouts.app')

@section('title', 'إنشاء طلب جديد')

@section('styles')
<style>
    .menu-item-card {
        cursor: pointer;
        transition: all 0.3s;
    }
    .menu-item-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .menu-item-card.selected {
        border: 2px solid #4e73df;
        background-color: rgba(78, 115, 223, 0.1);
    }
    .category-section {
        margin-bottom: 30px;
    }
    .order-summary {
        position: sticky;
        top: 20px;
    }
</style>
@endsection

@section('content')
<div id="create-order-page" class="page">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إنشاء طلب جديد</h2>
        <div>
            <a href="{{ route('employee.orders') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                <i class="fas fa-arrow-right ml-2"></i>
                <span>العودة للطلبات</span>
            </a>
        </div>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif



    <form id="orderForm" action="{{ route('employee.orders.store') }}" method="POST">
        @csrf
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center">
                        <i class="fas fa-user ml-2 text-primary"></i>
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white">معلومات العميل والطاولة</h3>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <div class="flex items-center mb-4 space-x-6 space-x-reverse">
                                <div class="flex items-center bg-gray-100 dark:bg-gray-700 px-4 py-2 rounded-md border border-gray-200 dark:border-gray-600">
                                    <input type="radio" id="registered_customer" name="customer_type" value="registered" class="customer-type-radio mr-2 h-4 w-4 text-primary" checked>
                                    <label for="registered_customer" class="text-sm font-medium text-gray-700 dark:text-gray-300">عميل مسجل</label>
                                </div>

                                <div class="flex items-center bg-gray-100 dark:bg-gray-700 px-4 py-2 rounded-md border border-gray-200 dark:border-gray-600">
                                    <input type="radio" id="guest_customer" name="customer_type" value="guest" class="customer-type-radio mr-2 h-4 w-4 text-primary">
                                    <label for="guest_customer" class="text-sm font-medium text-gray-700 dark:text-gray-300">زبون عادي (غير مسجل)</label>
                                </div>
                            </div>

                            <div id="registered_customer_fields" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العميل</label>
                                    <select name="user_id" id="user_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('user_id') border-red-500 @enderror">
                                        <option value="">اختر العميل</option>
                                        @foreach(\App\Models\User::where('user_type', 'customer')->orderBy('first_name')->get() as $user)
                                            <option value="{{ $user->user_id }}" {{ old('user_id') == $user->user_id ? 'selected' : '' }}>
                                                {{ $user->first_name }} {{ $user->last_name }} ({{ $user->phone }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="table_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الطاولة (اختياري)</label>
                                    <select name="table_id" id="table_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('table_id') border-red-500 @enderror">
                                        <option value="">بدون طاولة</option>
                                        @foreach($tables->where('status', 'available') as $table)
                                            <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }}>
                                                طاولة رقم {{ $table->table_number }} ({{ $table->capacity }} أشخاص)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('table_id')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div id="guest_customer_fields" class="grid grid-cols-1 md:grid-cols-2 gap-4" style="display: none;">
                                <div>
                                    <label for="customer_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">اسم الزبون <span class="text-red-500">*</span></label>
                                    <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name') }}" required class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('customer_name') border-red-500 @enderror">
                                    @error('customer_name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="customer_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف (اختياري)</label>
                                    <input type="text" id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('customer_phone') border-red-500 @enderror">
                                    @error('customer_phone')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="guest_count" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">عدد الأفراد <span class="text-red-500">*</span></label>
                                    <input type="number" id="guest_count" name="guest_count" value="{{ old('guest_count', 1) }}" min="1" required class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('guest_count') border-red-500 @enderror">
                                    @error('guest_count')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="table_id_guest" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الطاولة (اختياري)</label>
                                    <select id="table_id_guest" name="table_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('table_id') border-red-500 @enderror">
                                        <option value="">بدون طاولة</option>
                                        @foreach($tables->where('status', 'available') as $table)
                                            <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }}>
                                                طاولة رقم {{ $table->table_number }} ({{ $table->capacity }} أشخاص)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('table_id')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center">
                        <i class="fas fa-utensils ml-2 text-primary"></i>
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white">قائمة الطعام</h3>
                    </div>
                    <div class="p-6">
                        <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
                            <nav class="flex flex-wrap -mb-px" aria-label="Tabs">
                                @foreach($menuItems->keys() as $index => $category)
                                    <button class="tab-btn mr-2 py-2 px-4 border-b-2 font-medium text-sm {{ $index === 0 ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}"
                                            id="{{ Str::slug($category) }}-tab"
                                            data-target="#{{ Str::slug($category) }}"
                                            type="button"
                                            role="tab"
                                            aria-controls="{{ Str::slug($category) }}"
                                            aria-selected="{{ $index === 0 ? 'true' : 'false' }}">
                                        {{ $category }}
                                    </button>
                                @endforeach
                            </nav>
                        </div>
                        <div class="tab-content" id="menuTabsContent">
                            @foreach($menuItems as $category => $items)
                                <div class="tab-pane {{ $loop->first ? 'block' : 'hidden' }}"
                                     id="{{ Str::slug($category) }}"
                                     role="tabpanel"
                                     aria-labelledby="{{ Str::slug($category) }}-tab">
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($items as $item)
                                            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md overflow-hidden border border-gray-200 dark:border-gray-700" data-id="{{ $item->item_id }}" data-name="{{ $item->name }}" data-price="{{ $item->price }}">
                                                <div class="p-4">
                                                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">{{ $item->name }}</h5>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">{{ Str::limit($item->description, 50) }}</p>
                                                    <p class="text-lg font-bold text-primary">{{ number_format($item->price, 2) }} د.ل</p>
                                                </div>
                                                <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
                                                    <button type="button" onclick="addItemToOrder('{{ $item->item_id }}', '{{ $item->name }}', {{ $item->price }})" class="add-item-btn w-full bg-primary hover:bg-primary/90 text-white py-1.5 px-3 rounded-md flex items-center justify-center transition-all">
                                                        <i class="fas fa-plus ml-1"></i> إضافة للطلب
                                                    </button>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden order-summary sticky top-4">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center">
                        <i class="fas fa-shopping-cart ml-2 text-primary"></i>
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white">ملخص الطلب</h3>
                    </div>
                    <div class="p-6">
                        <div id="orderItems">
                            <p id="emptyOrderMessage" class="text-gray-500 dark:text-gray-400 text-center">لم يتم إضافة أي عناصر للطلب بعد</p>
                            <div id="orderItemsList"></div>
                        </div>
                        <hr class="my-4 border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center font-bold">
                            <span class="text-gray-800 dark:text-white">المجموع:</span>
                            <span id="totalAmount" class="text-primary text-xl">0.00 د.ل</span>
                        </div>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                        <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-4 rounded-md flex items-center justify-center transition-all text-lg" id="submitOrderBtn" disabled>
                            <i class="fas fa-check ml-2"></i> تأكيد الطلب
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
    // تعريف المتغيرات العالمية
    var orderItems = [];

    // دالة إضافة عنصر للطلب
    function addItemToOrder(itemId, itemName, itemPrice) {
        console.log("إضافة عنصر: ", itemId, itemName, itemPrice);

        // إضافة العنصر مباشرة إلى الطلب بكمية 1
        addItemToCart(itemId, itemName, itemPrice, 1, '');

        // إظهار رسالة نجاح
        showSuccessMessage('تمت إضافة ' + itemName + ' إلى الطلب');
    }

    // دالة إظهار رسالة نجاح
    function showSuccessMessage(message) {
        // إنشاء عنصر الرسالة
        var messageElement = document.createElement('div');
        messageElement.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-100 border border-green-400 text-green-700 px-6 py-3 rounded-md shadow-md z-50 flex items-center';
        messageElement.innerHTML = '<i class="fas fa-check-circle ml-2 text-green-500"></i>' + message;

        // إضافة العنصر إلى الصفحة
        document.body.appendChild(messageElement);

        // إخفاء الرسالة بعد 3 ثوان
        setTimeout(function() {
            messageElement.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(function() {
                document.body.removeChild(messageElement);
            }, 500);
        }, 3000);
    }

    // دالة إضافة العنصر إلى سلة الطلب
    function addItemToCart(itemId, itemName, itemPrice, quantity, notes) {
        // التحقق مما إذا كان العنصر موجودًا بالفعل في الطلب
        var existingItemIndex = -1;
        for (var i = 0; i < orderItems.length; i++) {
            if (orderItems[i].id === itemId) {
                existingItemIndex = i;
                break;
            }
        }

        if (existingItemIndex !== -1) {
            // زيادة الكمية إذا كان العنصر موجودًا بالفعل
            orderItems[existingItemIndex].quantity += quantity;
            // تحديث الملاحظات إذا تم إدخالها
            if (notes) {
                orderItems[existingItemIndex].notes = notes;
            }
        } else {
            // إضافة عنصر جديد إلى الطلب
            orderItems.push({
                id: itemId,
                name: itemName,
                price: parseFloat(itemPrice),
                quantity: quantity,
                notes: notes
            });
        }

        // تحديث ملخص الطلب
        updateOrderSummary();

        // تأثير بصري للتأكيد
        var card = document.querySelector('.menu-item-card[data-id="' + itemId + '"]');
        if (card) {
            card.classList.add('ring-2', 'ring-primary', 'ring-opacity-50');
            setTimeout(function() {
                card.classList.remove('ring-2', 'ring-primary', 'ring-opacity-50');
            }, 300);
        }
    }

    // دالة تحديث ملخص الطلب
    function updateOrderSummary() {
        console.log("تحديث ملخص الطلب", orderItems);

        var orderItemsList = document.getElementById('orderItemsList');
        var emptyOrderMessage = document.getElementById('emptyOrderMessage');
        var totalAmountElement = document.getElementById('totalAmount');
        var submitOrderBtn = document.getElementById('submitOrderBtn');

        // مسح قائمة عناصر الطلب
        orderItemsList.innerHTML = '';

        if (orderItems.length === 0) {
            emptyOrderMessage.style.display = 'block';
            submitOrderBtn.disabled = true;
        } else {
            emptyOrderMessage.style.display = 'none';
            submitOrderBtn.disabled = false;

            // إنشاء HTML لعناصر الطلب
            for (var i = 0; i < orderItems.length; i++) {
                var item = orderItems[i];
                var itemElement = document.createElement('div');
                itemElement.className = 'mb-4 pb-4 border-b border-gray-200 dark:border-gray-700 last:border-0 last:pb-0 last:mb-0';

                var itemHtml = '<div class="flex justify-between items-center mb-2">' +
                    '<span class="font-medium text-gray-800 dark:text-white">' + item.name + '</span>' +
                    '<div class="flex items-center space-x-1 space-x-reverse">' +
                    '<button type="button" onclick="decreaseQuantity(' + i + ')" class="decrease-btn w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center justify-center">' +
                    '<i class="fas fa-minus text-xs"></i>' +
                    '</button>' +
                    '<span class="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center text-sm font-bold">' + item.quantity + '</span>' +
                    '<button type="button" onclick="increaseQuantity(' + i + ')" class="increase-btn w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center justify-center">' +
                    '<i class="fas fa-plus text-xs"></i>' +
                    '</button>' +
                    '</div>' +
                    '</div>' +
                    '<div class="flex justify-between text-sm">' +
                    '<span class="text-gray-500 dark:text-gray-400">' + item.quantity + ' × ' + item.price.toFixed(2) + ' د.ل</span>' +
                    '<span class="font-medium text-gray-800 dark:text-white">' + (item.quantity * item.price).toFixed(2) + ' د.ل</span>' +
                    '</div>';

                // إضافة الملاحظات إذا وجدت
                if (item.notes && item.notes.trim() !== '') {
                    itemHtml += '<div class="mt-2 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 p-2 rounded-md">' +
                        '<span class="font-medium">ملاحظات:</span> ' + item.notes +
                        '</div>';
                }

                // إضافة الحقول المخفية
                itemHtml += '<input type="hidden" name="items[' + i + '][id]" value="' + item.id + '">' +
                    '<input type="hidden" name="items[' + i + '][quantity]" value="' + item.quantity + '">';

                if (item.notes) {
                    itemHtml += '<input type="hidden" name="items[' + i + '][notes]" value="' + item.notes + '">';
                }

                itemElement.innerHTML = itemHtml;
                orderItemsList.appendChild(itemElement);
            }
        }

        // تحديث المبلغ الإجمالي
        var totalAmount = 0;
        for (var i = 0; i < orderItems.length; i++) {
            totalAmount += orderItems[i].price * orderItems[i].quantity;
        }
        totalAmountElement.textContent = totalAmount.toFixed(2) + ' د.ل';
    }

    // دالة زيادة الكمية
    function increaseQuantity(index) {
        orderItems[index].quantity++;
        updateOrderSummary();
    }

    // دالة إنقاص الكمية
    function decreaseQuantity(index) {
        if (orderItems[index].quantity > 1) {
            orderItems[index].quantity--;
        } else {
            orderItems.splice(index, 1);
        }
        updateOrderSummary();
    }

    // تبديل بين حقول العميل المسجل والزبون العادي
    function toggleCustomerFields(isRegistered) {
        var registeredFields = document.getElementById('registered_customer_fields');
        var guestFields = document.getElementById('guest_customer_fields');

        if (isRegistered) {
            registeredFields.style.display = 'grid';
            guestFields.style.display = 'none';
            // مسح حقول الزبون العادي
            document.getElementById('customer_name').value = '';
            document.getElementById('customer_phone').value = '';
            if (document.getElementById('guest_count')) {
                document.getElementById('guest_count').value = '1';
            }
        } else {
            registeredFields.style.display = 'none';
            guestFields.style.display = 'grid';
            // مسح حقول العميل المسجل
            document.getElementById('user_id').value = '';
        }
    }

    // تبديل علامات التبويب
    function switchTab(tabButton) {
        // إزالة الفئة النشطة من جميع علامات التبويب
        var allTabs = document.querySelectorAll('.tab-btn');
        for (var i = 0; i < allTabs.length; i++) {
            allTabs[i].classList.remove('border-primary', 'text-primary');
            allTabs[i].classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        }

        // إضافة الفئة النشطة إلى علامة التبويب المحددة
        tabButton.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
        tabButton.classList.add('border-primary', 'text-primary');

        // إخفاء جميع أقسام علامات التبويب
        var allPanes = document.querySelectorAll('.tab-pane');
        for (var i = 0; i < allPanes.length; i++) {
            allPanes[i].classList.add('hidden');
            allPanes[i].classList.remove('block');
        }

        // إظهار قسم علامة التبويب المستهدف
        var targetId = tabButton.getAttribute('data-target');
        var targetPane = document.querySelector(targetId);
        targetPane.classList.remove('hidden');
        targetPane.classList.add('block');
    }

    // تهيئة الصفحة عند تحميلها
    document.addEventListener('DOMContentLoaded', function() {
        console.log("تم تحميل الصفحة");

        // إضافة مستمعي الأحداث لأزرار نوع العميل
        var customerTypeRadios = document.querySelectorAll('.customer-type-radio');
        for (var i = 0; i < customerTypeRadios.length; i++) {
            customerTypeRadios[i].addEventListener('change', function() {
                toggleCustomerFields(this.value === 'registered');
            });
        }

        // التحقق من الحالة الأولية
        var initialCustomerType = document.querySelector('input[name="customer_type"]:checked');
        if (initialCustomerType) {
            toggleCustomerFields(initialCustomerType.value === 'registered');
        }

        // إضافة مستمعي الأحداث لعلامات التبويب
        var tabButtons = document.querySelectorAll('.tab-btn');
        for (var i = 0; i < tabButtons.length; i++) {
            tabButtons[i].addEventListener('click', function() {
                switchTab(this);
            });
        }

        // التحقق من وجود عناصر في سلة الطلبات من التخزين المحلي
        var cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        if (cartItems.length > 0) {
            // إضافة العناصر من سلة الطلبات إلى الطلب الحالي
            cartItems.forEach(function(item) {
                addItemToCart(item.id, item.name, item.price, item.quantity, item.notes || '');
            });

            // عرض رسالة للمستخدم
            showSuccessMessage('تم استيراد ' + cartItems.length + ' عناصر من سلة الطلبات');

            // مسح سلة الطلبات بعد استيرادها
            localStorage.removeItem('cartItems');

            // تحديث عدد العناصر في السلة
            var cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = 0;
            });
        }

        // تحديث ملخص الطلب الأولي
        updateOrderSummary();
    });
</script>
@endsection
