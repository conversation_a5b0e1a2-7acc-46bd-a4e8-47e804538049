@extends('employee.layouts.app')

@section('title', 'تعديل الطلب #' . $order->order_id)

@section('content')
<div id="edit-order-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تعديل الطلب <span class="text-primary">#{{ $order->order_id }}</span></h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('employee.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <a href="{{ route('employee.orders') }}" class="hover:text-primary">الطلبات</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>تعديل الطلب #{{ $order->order_id }}</span>
            </div>
        </div>
        <div class="mt-4 md:mt-0">
            @if($order->status == 'pending')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                    <i class="fas fa-clock ml-1.5"></i>قيد الانتظار
                </span>
            @elseif($order->status == 'preparing')
                <span class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                    <i class="fas fa-utensils ml-1.5"></i>قيد التحضير
                </span>
            @endif
        </div>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif

    <form id="orderForm" action="{{ route('employee.orders.update', $order->order_id) }}" method="POST">
        @csrf
        @method('PUT')
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-user-circle text-primary ml-2"></i>
                            معلومات العميل والطاولة
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="mb-4">
                            <div class="flex items-center mb-4">
                                <input type="radio" id="registered_customer" name="customer_type" value="registered" class="customer-type-radio mr-2" {{ $order->user_id ? 'checked' : '' }}>
                                <label for="registered_customer" class="text-sm font-medium text-gray-700 dark:text-gray-300">عميل مسجل</label>

                                <input type="radio" id="guest_customer" name="customer_type" value="guest" class="customer-type-radio mr-2 ml-6" {{ !$order->user_id ? 'checked' : '' }}>
                                <label for="guest_customer" class="text-sm font-medium text-gray-700 dark:text-gray-300">زبون عادي (غير مسجل)</label>
                            </div>

                            <div id="registered_customer_fields" class="grid grid-cols-1 md:grid-cols-2 gap-6" {{ !$order->user_id ? 'style="display: none;"' : '' }}>
                                <div>
                                    <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العميل</label>
                                    <select name="user_id" id="user_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('user_id') border-red-500 @enderror">
                                        <option value="">اختر العميل</option>
                                        @foreach(\App\Models\User::where('user_type', 'customer')->orderBy('first_name')->get() as $user)
                                            <option value="{{ $user->user_id }}" {{ $order->user_id == $user->user_id ? 'selected' : '' }}>
                                                {{ $user->first_name }} {{ $user->last_name }} ({{ $user->phone }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('user_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="table_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطاولة (اختياري)</label>
                                    <select name="table_id" id="table_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('table_id') border-red-500 @enderror">
                                        <option value="">بدون طاولة</option>
                                        @foreach($tables->where('status', function($status) use ($order) {
                                            return $status === 'available' || ($order->table_id && $status === 'occupied' && $order->table_id == $tables->where('status', 'occupied')->first()->table_id);
                                        }) as $table)
                                            <option value="{{ $table->table_id }}" {{ $order->table_id == $table->table_id ? 'selected' : '' }}>
                                                طاولة رقم {{ $table->table_number }} ({{ $table->capacity }} أشخاص)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('table_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div id="guest_customer_fields" class="grid grid-cols-1 md:grid-cols-2 gap-6" {{ $order->user_id ? 'style="display: none;"' : '' }}>
                                <div>
                                    <label for="customer_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الزبون</label>
                                    <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name', $order->customer_name) }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('customer_name') border-red-500 @enderror">
                                    @error('customer_name')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="customer_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف (اختياري)</label>
                                    <input type="text" id="customer_phone" name="customer_phone" value="{{ old('customer_phone', $order->customer_phone) }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('customer_phone') border-red-500 @enderror">
                                    @error('customer_phone')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>

                                <div>
                                    <label for="table_id_guest" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطاولة (اختياري)</label>
                                    <select id="table_id_guest" name="table_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary @error('table_id') border-red-500 @enderror">
                                        <option value="">بدون طاولة</option>
                                        @foreach($tables->where('status', function($status) use ($order) {
                                            return $status === 'available' || ($order->table_id && $status === 'occupied' && $order->table_id == $tables->where('status', 'occupied')->first()->table_id);
                                        }) as $table)
                                            <option value="{{ $table->table_id }}" {{ $order->table_id == $table->table_id ? 'selected' : '' }}>
                                                طاولة رقم {{ $table->table_number }} ({{ $table->capacity }} أشخاص)
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('table_id')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-utensils text-primary ml-2"></i>
                            قائمة الطعام
                        </h3>
                    </div>
                    <div class="p-6">
                        <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
                            <nav class="flex flex-wrap -mb-px" aria-label="Tabs">
                                @foreach($menuItems->keys() as $index => $category)
                                    <button type="button"
                                            class="tab-btn inline-block py-3 px-4 border-b-2 {{ $index === 0 ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300' }} font-medium text-sm focus:outline-none"
                                            data-target="{{ Str::slug($category) }}">
                                        {{ $category }}
                                    </button>
                                @endforeach
                            </nav>
                        </div>
                        <div class="tab-content">
                            @foreach($menuItems as $category => $items)
                                <div class="tab-pane {{ $loop->first ? 'block' : 'hidden' }}" id="{{ Str::slug($category) }}">
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach($items as $item)
                                            <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 overflow-hidden menu-item-card" data-id="{{ $item->item_id }}" data-name="{{ $item->name }}" data-price="{{ $item->price }}">
                                                <div class="p-4">
                                                    <h4 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">{{ $item->name }}</h4>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-3 h-12 overflow-hidden">{{ Str::limit($item->description, 50) }}</p>
                                                    <div class="flex justify-between items-center">
                                                        <span class="text-primary font-bold">{{ number_format($item->price, 2) }} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span></span>
                                                        <button type="button" class="add-item-btn inline-flex items-center px-3 py-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 transition-colors">
                                                            <i class="fas fa-plus ml-1.5"></i>
                                                            <span>إضافة</span>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden sticky top-6">
                    <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-shopping-cart text-primary ml-2"></i>
                            ملخص الطلب
                        </h3>
                    </div>
                    <div class="p-6">
                        <div id="orderItems" class="min-h-[200px]">
                            <p id="emptyOrderMessage" class="text-center text-gray-500 dark:text-gray-400">لم يتم إضافة أي عناصر للطلب بعد</p>
                            <div id="orderItemsList" class="space-y-4"></div>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 mt-6 pt-4">
                            <div class="flex justify-between items-center font-bold text-gray-800 dark:text-white">
                                <span>المجموع:</span>
                                <span id="totalAmount" class="text-primary">0.00 <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span></span>
                            </div>
                        </div>
                    </div>
                    <div class="px-6 pb-6">
                        <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-4 rounded-md flex items-center justify-center transition-all disabled:opacity-50 disabled:cursor-not-allowed" id="submitOrderBtn" disabled>
                            <i class="fas fa-check ml-2"></i>
                            <span>تحديث الطلب</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between registered customer and guest customer fields
        const customerTypeRadios = document.querySelectorAll('.customer-type-radio');
        customerTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'registered') {
                    document.getElementById('registered_customer_fields').style.display = 'grid';
                    document.getElementById('guest_customer_fields').style.display = 'none';
                    // Clear guest customer fields
                    document.getElementById('customer_name').value = '';
                    document.getElementById('customer_phone').value = '';
                } else {
                    document.getElementById('registered_customer_fields').style.display = 'none';
                    document.getElementById('guest_customer_fields').style.display = 'grid';
                    // Clear registered customer fields
                    document.getElementById('user_id').value = '';
                }
            });
        });

        // Initialize order items with existing items
        const orderItems = [
            @foreach($order->items as $item)
            {
                id: "{{ $item->menu_item_id }}",
                name: "{{ $item->menuItem->name }}",
                price: {{ $item->price }},
                quantity: {{ $item->quantity }}
            },
            @endforeach
        ];

        const orderItemsList = document.getElementById('orderItemsList');
        const emptyOrderMessage = document.getElementById('emptyOrderMessage');
        const totalAmountElement = document.getElementById('totalAmount');
        const submitOrderBtn = document.getElementById('submitOrderBtn');

        // Tab functionality
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const target = this.dataset.target;

                // Update active tab button
                tabButtons.forEach(btn => {
                    btn.classList.remove('border-primary', 'text-primary');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                this.classList.remove('border-transparent', 'text-gray-500');
                this.classList.add('border-primary', 'text-primary');

                // Show target tab pane
                tabPanes.forEach(pane => {
                    pane.classList.add('hidden');
                    pane.classList.remove('block');
                });
                document.getElementById(target).classList.remove('hidden');
                document.getElementById(target).classList.add('block');
            });
        });

        // Add item to order
        document.querySelectorAll('.menu-item-card').forEach(card => {
            card.querySelector('.add-item-btn').addEventListener('click', function() {
                const itemId = card.dataset.id;
                const itemName = card.dataset.name;
                const itemPrice = parseFloat(card.dataset.price);

                // Check if item already exists in order
                const existingItemIndex = orderItems.findIndex(item => item.id === itemId);

                if (existingItemIndex !== -1) {
                    // Increment quantity if item already exists
                    orderItems[existingItemIndex].quantity++;
                } else {
                    // Add new item to order
                    orderItems.push({
                        id: itemId,
                        name: itemName,
                        price: itemPrice,
                        quantity: 1
                    });
                }

                updateOrderSummary();
            });
        });

        function updateOrderSummary() {
            // Clear order items list
            orderItemsList.innerHTML = '';

            if (orderItems.length === 0) {
                emptyOrderMessage.style.display = 'block';
                submitOrderBtn.disabled = true;
            } else {
                emptyOrderMessage.style.display = 'none';
                submitOrderBtn.disabled = false;

                // Create order items HTML
                orderItems.forEach((item, index) => {
                    const itemElement = document.createElement('div');
                    itemElement.className = 'bg-gray-50 dark:bg-gray-700/30 rounded-lg p-3';
                    itemElement.innerHTML = `
                        <div class="flex justify-between items-center mb-2">
                            <span class="font-semibold text-gray-800 dark:text-white">${item.name}</span>
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <button type="button" class="decrease-btn w-7 h-7 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors" data-index="${index}">
                                    <i class="fas fa-minus text-xs"></i>
                                </button>
                                <span class="w-6 text-center font-medium text-gray-800 dark:text-white">${item.quantity}</span>
                                <button type="button" class="increase-btn w-7 h-7 rounded-full bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 flex items-center justify-center hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors" data-index="${index}">
                                    <i class="fas fa-plus text-xs"></i>
                                </button>
                            </div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500 dark:text-gray-400">${item.quantity} × ${item.price.toFixed(2)} د.ل</span>
                            <span class="font-medium text-primary">${(item.quantity * item.price).toFixed(2)} د.ل</span>
                        </div>
                        <input type="hidden" name="items[${index}][id]" value="${item.id}">
                        <input type="hidden" name="items[${index}][quantity]" value="${item.quantity}">
                    `;
                    orderItemsList.appendChild(itemElement);
                });

                // Add event listeners for quantity buttons
                document.querySelectorAll('.decrease-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        if (orderItems[index].quantity > 1) {
                            orderItems[index].quantity--;
                        } else {
                            orderItems.splice(index, 1);
                        }
                        updateOrderSummary();
                    });
                });

                document.querySelectorAll('.increase-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        orderItems[index].quantity++;
                        updateOrderSummary();
                    });
                });
            }

            // Update total amount
            const totalAmount = orderItems.reduce((total, item) => total + (item.price * item.quantity), 0);
            totalAmountElement.innerHTML = `${totalAmount.toFixed(2)} <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span>`;
        }

        // Initialize order summary
        updateOrderSummary();
    });
</script>
@endsection
