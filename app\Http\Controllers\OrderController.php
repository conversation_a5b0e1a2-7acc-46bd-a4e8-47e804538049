<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\MenuItem;
use App\Models\Table;
use App\Models\Notification;
use App\Models\Inventory;
use App\Models\InventoryTransaction;
use App\Models\Cart;
use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // دالة إضافة عنصر إلى سلة التسوق
    public function addToCart(Request $request)
    {
        try {
            // التحقق من البيانات
            $validator = Validator::make($request->all(), [
                'item_id' => 'required|exists:menu_items,item_id',
                'quantity' => 'required|integer|min:1',
                'notes' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صالحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            // الحصول على معلومات العنصر
            $menuItem = MenuItem::findOrFail($request->item_id);

            // في هذه المرحلة، نحن نستخدم التخزين المحلي في المتصفح لتخزين عناصر السلة
            // لذلك نعيد فقط استجابة نجاح مع معلومات العنصر

            return response()->json([
                'success' => true,
                'message' => 'تمت إضافة العنصر إلى السلة بنجاح',
                'item' => [
                    'id' => $menuItem->item_id,
                    'name' => $menuItem->name,
                    'price' => $menuItem->price,
                    'quantity' => $request->quantity,
                    'notes' => $request->notes
                ]
            ]);
        } catch (\Exception $e) {
            // تسجيل الخطأ
            Log::error('Error adding item to cart: ' . $e->getMessage());

            // إعادة استجابة خطأ
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة العنصر: ' . $e->getMessage()
            ], 500);
        }
    }

    // دالة حساب إجمالي السلة
    private function calculateCartTotal($cart)
    {
        $total = 0;
        foreach ($cart as $item) {
            $total += $item['total'];
        }
        return $total;
    }

    // Admin methods
    public function index(Request $request)
    {
        $query = Order::with(['user', 'table', 'items.menuItem', 'payments']);

        // معالجة البحث
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function($q) use ($searchTerm) {
                $q->whereHas('user', function($userQuery) use ($searchTerm) {
                    $userQuery->where('first_name', 'like', '%' . $searchTerm . '%')
                      ->orWhere('last_name', 'like', '%' . $searchTerm . '%')
                      ->orWhere('email', 'like', '%' . $searchTerm . '%');
                })
                ->orWhere('order_id', 'like', '%' . $searchTerm . '%');
            });
        }

        // تصفية حسب الحالة
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // تسجيل الاستعلام للتشخيص
        Log::info('Orders Query:', ['sql' => $query->toSql(), 'bindings' => $query->getBindings()]);

        $orders = $query->orderBy('created_at', 'desc')
            ->paginate(15)
            ->appends($request->query());

        Log::info('Orders Count:', ['count' => $orders->count()]);

        // Get order status counts
        $orderStats = [
            'pending' => Order::where('status', 'pending')->count(),
            'preparing' => Order::where('status', 'preparing')->count(),
            'completed' => Order::where('status', 'completed')->count(),
            'canceled' => Order::where('status', 'canceled')->count(),
            'total' => Order::count()
        ];

        // تحديد القالب بناءً على نوع المستخدم
        $viewPrefix = Auth::user()->user_type == 'admin' ? 'admin' : 'employee';

        try {
            return view($viewPrefix . '/orders/index', compact('orders', 'orderStats'));
        } catch (\Exception $e) {
            // إذا لم يتم العثور على القالب المحدد، استخدم قالب الموظف
            Log::error('View not found: ' . $e->getMessage());
            return view('employee/orders/index', compact('orders', 'orderStats'));
        }
    }

    public function create()
    {
        $menuItems = MenuItem::where('is_available', true)
            ->orderBy('category')
            ->orderBy('name')
            ->get()
            ->groupBy('category');

        $tables = Table::orderBy('table_number')->get();

        // تحديد القالب بناءً على نوع المستخدم
        $viewPrefix = Auth::user()->user_type == 'admin' ? 'admin' : 'employee';

        try {
            return view($viewPrefix . '/orders/create', compact('menuItems', 'tables'));
        } catch (\Exception $e) {
            // إذا لم يتم العثور على القالب المحدد، استخدم قالب الموظف
            Log::error('View not found: ' . $e->getMessage());
            return view('employee/orders/create', compact('menuItems', 'tables'));
        }
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,user_id',
            'customer_name' => 'required_without:user_id|string|max:100|nullable',
            'customer_phone' => 'nullable|string|max:20',
            'table_id' => 'nullable|exists:tables,table_id',
            'items' => 'required|array|min:1',
            'items.*.id' => 'required|exists:menu_items,item_id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            $totalAmount = 0;

            // Calculate total amount
            foreach ($request->items as $item) {
                $menuItem = MenuItem::findOrFail($item['id']);
                $totalAmount += $menuItem->price * $item['quantity'];
            }

            // Create order
            $order = Order::create([
                'user_id' => $request->user_id,
                'customer_name' => $request->customer_name,
                'customer_phone' => $request->customer_phone,
                'table_id' => $request->table_id,
                'total_amount' => $totalAmount,
                'status' => 'pending',
            ]);

            // Add order items
            foreach ($request->items as $item) {
                $menuItem = MenuItem::findOrFail($item['id']);

                OrderItem::create([
                    'order_id' => $order->order_id,
                    'menu_item_id' => $item['id'],
                    'quantity' => $item['quantity'],
                    'price' => $menuItem->price,
                ]);

                // Update inventory - deduct ingredients used
                $this->updateInventoryForOrderItem($menuItem, $item['quantity']);
            }

            // If table is specified, update its status
            if ($request->table_id) {
                Table::where('table_id', $request->table_id)
                    ->update(['status' => 'occupied']);
            }

            // Create notification for the customer if registered
            if ($request->user_id) {
                Notification::create([
                    'user_id' => $request->user_id,
                    'message' => 'تم استلام طلبك رقم #' . $order->order_id . ' وجاري تحضيره.',
                    'is_read' => false,
                ]);
            }

            DB::commit();

            // تحديد مسار إعادة التوجيه بناءً على نوع المستخدم
            $redirectRoute = Auth::user()->user_type == 'admin' ? 'admin.orders' : 'employee.orders';

            return redirect()->route($redirectRoute)->with('success', 'تم إنشاء الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء الطلب: ' . $e->getMessage())->withInput();
        }
    }

    public function show($id)
    {
        $order = Order::with(['user', 'table', 'items.menuItem'])->findOrFail($id);

        // تحديد القالب بناءً على نوع المستخدم
        $viewPrefix = Auth::user()->user_type == 'admin' ? 'admin' : 'employee';

        try {
            return view($viewPrefix . '/orders/show', compact('order'));
        } catch (\Exception $e) {
            // إذا لم يتم العثور على القالب المحدد، استخدم قالب الموظف
            Log::error('View not found: ' . $e->getMessage());
            return view('employee/orders/show', compact('order'));
        }
    }

    public function edit($id)
    {
        $order = Order::with(['user', 'table', 'items.menuItem'])->findOrFail($id);
        $menuItems = MenuItem::where('is_available', true)
            ->orderBy('category')
            ->orderBy('name')
            ->get()
            ->groupBy('category');
        $tables = Table::orderBy('table_number')->get();

        // تحديد القالب بناءً على نوع المستخدم
        $viewPrefix = Auth::user()->user_type == 'admin' ? 'admin' : 'employee';

        try {
            return view($viewPrefix . '/orders/edit', compact('order', 'menuItems', 'tables'));
        } catch (\Exception $e) {
            // إذا لم يتم العثور على القالب المحدد، استخدم قالب الموظف
            Log::error('View not found: ' . $e->getMessage());
            return view('employee/orders/edit', compact('order', 'menuItems', 'tables'));
        }
    }

    public function update(Request $request, $id)
    {
        // تسجيل البيانات المرسلة للتشخيص
        Log::info('Update Order Request Data:', $request->all());

        $validator = Validator::make($request->all(), [
            'user_id' => 'nullable|exists:users,user_id',
            'customer_name' => 'required_without:user_id|string|max:100|nullable',
            'customer_phone' => 'nullable|string|max:20',
            'table_id' => 'nullable|exists:tables,table_id',
            'items' => 'required|array|min:1',
            'items.*.id' => 'required|exists:menu_items,item_id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            Log::error('Validation failed:', $validator->errors()->toArray());
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $order = Order::with('items')->findOrFail($id);
        Log::info('Found Order:', ['order_id' => $order->order_id, 'status' => $order->status]);

        DB::beginTransaction();

        try {
            // Calculate total amount
            $totalAmount = 0;
            foreach ($request->items as $item) {
                $menuItem = MenuItem::findOrFail($item['id']);
                $totalAmount += $menuItem->price * $item['quantity'];
            }
            Log::info('Calculated total amount:', ['total' => $totalAmount]);

            // Update order
            $oldTableId = $order->table_id;
            $order->user_id = $request->user_id;
            $order->customer_name = $request->customer_name;
            $order->customer_phone = $request->customer_phone;
            $order->table_id = $request->table_id;
            $order->total_amount = $totalAmount;
            $order->save();
            Log::info('Order updated:', ['order_id' => $order->order_id]);

            // If table changed, update table statuses
            if ($oldTableId != $request->table_id) {
                Log::info('Table changed:', ['old' => $oldTableId, 'new' => $request->table_id]);

                // Free old table if exists
                if ($oldTableId) {
                    Table::where('table_id', $oldTableId)
                        ->update(['status' => 'available']);
                    Log::info('Old table freed:', ['table_id' => $oldTableId]);
                }

                // Occupy new table if exists
                if ($request->table_id) {
                    Table::where('table_id', $request->table_id)
                        ->update(['status' => 'occupied']);
                    Log::info('New table occupied:', ['table_id' => $request->table_id]);
                }
            }

            // Get existing order items before deleting them
            $existingItems = OrderItem::where('order_id', $id)->get();
            Log::info('Existing order items:', ['count' => $existingItems->count()]);

            // Restore inventory for existing items
            foreach ($existingItems as $orderItem) {
                $menuItem = MenuItem::find($orderItem->menu_item_id);
                if (!$menuItem) {
                    Log::warning('Menu item not found:', ['menu_item_id' => $orderItem->menu_item_id]);
                    continue;
                }

                $recipes = DB::table('recipes')
                    ->where('menu_item_id', $orderItem->menu_item_id)
                    ->get();

                foreach ($recipes as $recipe) {
                    $totalQuantityToRestore = $recipe->quantity * $orderItem->quantity;

                    // Get the latest inventory for this ingredient
                    $latestInventory = Inventory::where('ingredient_id', $recipe->ingredient_id)
                        ->orderBy('inventory_id', 'desc')
                        ->first();

                    if ($latestInventory) {
                        // Update inventory
                        $latestInventory->quantity += $totalQuantityToRestore;
                        $latestInventory->save();

                        // Record transaction
                        InventoryTransaction::create([
                            'inventory_id' => $latestInventory->inventory_id,
                            'user_id' => Auth::id(),
                            'transaction_type' => 'purchase', // Using purchase to indicate adding back to inventory
                            'quantity' => $totalQuantityToRestore,
                        ]);

                        Log::info('Restored inventory:', [
                            'ingredient_id' => $recipe->ingredient_id,
                            'quantity' => $totalQuantityToRestore
                        ]);
                    }
                }
            }

            // Delete existing order items
            OrderItem::where('order_id', $id)->delete();
            Log::info('Deleted existing order items');

            // Add new order items
            foreach ($request->items as $item) {
                $menuItem = MenuItem::findOrFail($item['id']);

                OrderItem::create([
                    'order_id' => $order->order_id,
                    'menu_item_id' => $item['id'],
                    'quantity' => $item['quantity'],
                    'price' => $menuItem->price,
                ]);
                Log::info('Created new order item:', ['menu_item_id' => $item['id'], 'quantity' => $item['quantity']]);

                // تجاهل التحقق من المخزون عند تحديث الطلب
                try {
                    $this->updateInventoryForOrderItem($menuItem, $item['quantity']);
                } catch (\Exception $inventoryException) {
                    Log::warning('Inventory issue during order update:', [
                        'menu_item_id' => $item['id'],
                        'quantity' => $item['quantity'],
                        'error' => $inventoryException->getMessage()
                    ]);
                    // استمر في التنفيذ حتى لو كان المخزون غير كافٍ
                }
            }

            // Create notification for the customer if registered
            if ($order->user_id) {
                Notification::create([
                    'user_id' => $order->user_id,
                    'message' => 'تم تحديث طلبك رقم #' . $order->order_id . '.',
                    'is_read' => false,
                ]);
            }
            Log::info('Created notification for customer');

            DB::commit();
            Log::info('Transaction committed successfully');

            // تحديد مسار إعادة التوجيه بناءً على نوع المستخدم
            $redirectRoute = Auth::user()->user_type == 'admin' ? 'admin.orders.show' : 'employee.orders.show';

            return redirect()->route($redirectRoute, $id)->with('success', 'تم تحديث الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating order:', ['message' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الطلب: ' . $e->getMessage())->withInput();
        }
    }

    public function updateStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,preparing,completed,canceled',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $order = Order::findOrFail($id);

        DB::beginTransaction();

        try {
            $oldStatus = $order->status;
            $order->status = $request->status;
            $order->save();

            // If order is completed and has a table, update table status
            if ($request->status === 'completed' && $order->table_id) {
                Table::where('table_id', $order->table_id)
                    ->update(['status' => 'available']);
            }

            // If order is canceled, restore inventory
            if ($request->status === 'canceled' && in_array($oldStatus, ['pending', 'preparing'])) {
                $this->restoreInventoryForOrder($order);
            }

            // Create notification for the customer
            $statusMessages = [
                'pending' => 'تم تغيير حالة طلبك رقم #' . $order->order_id . ' إلى قيد الانتظار.',
                'preparing' => 'جاري تحضير طلبك رقم #' . $order->order_id . '.',
                'completed' => 'تم الانتهاء من تحضير طلبك رقم #' . $order->order_id . '.',
                'canceled' => 'تم إلغاء طلبك رقم #' . $order->order_id . '.',
            ];

            // إنشاء إشعار للعميل إذا كان مسجلاً فقط
            if ($order->user_id) {
                Notification::create([
                    'user_id' => $order->user_id,
                    'message' => $statusMessages[$request->status],
                    'is_read' => false,
                ]);
            }

            DB::commit();

            // تحديد مسار إعادة التوجيه بناءً على نوع المستخدم
            $redirectRoute = Auth::user()->user_type == 'admin' ? 'admin.orders.show' : 'employee.orders.show';

            return redirect()->route($redirectRoute, $id)->with('success', 'تم تحديث حالة الطلب بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث حالة الطلب: ' . $e->getMessage());
        }
    }

    // Customer methods
    public function customerIndex(Request $request)
    {
        $status = $request->get('status', 'all');

        $query = Order::with(['items.menuItem'])
            ->where('user_id', Auth::id());

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        $orders = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->appends($request->query());

        return view('customer.orders', compact('orders', 'status'));
    }

    public function customerCreate()
    {
        $menuItems = MenuItem::where('is_available', true)
            ->orderBy('category')
            ->orderBy('name')
            ->get()
            ->groupBy('category');

        return view('customer.orders.create', compact('menuItems'));
    }

    public function customerStore(Request $request)
    {
        Log::info('Customer order creation started:', [
            'user_id' => Auth::id(),
            'request_data' => $request->all()
        ]);

        // التحقق من وجود عناصر في السلة
        $cartItems = Cart::with('menuItem')->where('user_id', Auth::id())->get();

        Log::info('Cart items found:', ['count' => $cartItems->count()]);

        if ($cartItems->isEmpty()) {
            Log::warning('Cart is empty for user:', ['user_id' => Auth::id()]);
            return redirect()->route('customer.cart')->with('error', 'السلة فارغة');
        }

        $validator = Validator::make($request->all(), [
            'order_type' => 'required|in:delivery,pickup',
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
            'address' => 'required_if:order_type,delivery|nullable|string|max:500',
            'payment_method' => 'required|in:cash,card,wallet',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            $totalAmount = 0;

            // Calculate total amount from cart items
            foreach ($cartItems as $cartItem) {
                $totalAmount += $cartItem->subtotal;
            }

            // حساب الضريبة ورسوم التوصيل
            $tax = $totalAmount * 0.15;
            $deliveryFee = ($request->order_type === 'pickup') ? 0 : 10.00;
            $finalTotal = $totalAmount + $tax + $deliveryFee;

            // Create order
            $order = Order::create([
                'user_id' => Auth::id(),
                'customer_name' => $request->first_name . ' ' . $request->last_name,
                'customer_phone' => $request->phone,
                'table_id' => null, // Online order
                'total_amount' => $finalTotal,
                'status' => 'pending',
                'order_type' => $request->order_type,
                'delivery_address' => $request->address,
                'payment_method' => $request->payment_method,
                'notes' => $request->notes,
            ]);

            // Add order items from cart
            foreach ($cartItems as $cartItem) {
                OrderItem::create([
                    'order_id' => $order->order_id,
                    'menu_item_id' => $cartItem->menu_item_id,
                    'quantity' => $cartItem->quantity,
                    'price' => $cartItem->price,
                ]);

                // Update inventory - deduct ingredients used (تجاهل أخطاء المخزون مؤقتاً)
                try {
                    $this->updateInventoryForOrderItem($cartItem->menuItem, $cartItem->quantity);
                } catch (\Exception $inventoryException) {
                    // تسجيل الخطأ ولكن لا نوقف العملية
                    Log::warning('Inventory issue during order creation:', [
                        'menu_item_id' => $cartItem->menu_item_id,
                        'quantity' => $cartItem->quantity,
                        'error' => $inventoryException->getMessage()
                    ]);
                }
            }

            // Clear cart after successful order
            Cart::where('user_id', Auth::id())->delete();

            // Create notification for staff
            $this->notifyStaffAboutNewOrder($order);

            DB::commit();

            return redirect()->route('customer.orders.show', $order->order_id)->with('success', 'تم إرسال طلبك بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating customer order:', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return redirect()->back()->with('error', 'حدث خطأ أثناء إرسال الطلب: ' . $e->getMessage())->withInput();
        }
    }

    public function customerShow($id)
    {
        $order = Order::with(['items.menuItem'])
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        return view('customer.orders.show', compact('order'));
    }

    // إلغاء الطلب من قبل العميل
    public function customerCancelPage($id)
    {
        $order = Order::where('user_id', Auth::id())
            ->where('order_id', $id)
            ->firstOrFail();

        // التحقق من إمكانية إلغاء الطلب
        if (!in_array($order->status, ['pending', 'preparing'])) {
            return redirect()->route('customer.orders.show', $id)
                ->with('error', 'لا يمكن إلغاء هذا الطلب في الحالة الحالية');
        }

        return view('customer.orders.cancel', compact('order'));
    }

    // تأكيد إلغاء الطلب
    public function customerCancel(Request $request, $id)
    {
        $order = Order::where('user_id', Auth::id())
            ->where('order_id', $id)
            ->firstOrFail();

        // التحقق من إمكانية إلغاء الطلب
        if (!in_array($order->status, ['pending', 'preparing'])) {
            return redirect()->route('customer.orders.show', $id)
                ->with('error', 'لا يمكن إلغاء هذا الطلب في الحالة الحالية');
        }

        DB::beginTransaction();

        try {
            // تحديث حالة الطلب إلى ملغي
            $order->status = 'canceled';
            $order->save();

            // استرداد المخزون
            $this->restoreInventoryForOrder($order);

            // تحرير الطاولة إذا كانت محجوزة
            if ($order->table_id) {
                Table::where('table_id', $order->table_id)
                    ->update(['status' => 'available']);
            }

            // إنشاء إشعار للموظفين
            $this->notifyStaffAboutCancelledOrder($order);

            DB::commit();

            return redirect()->route('customer.orders.index')
                ->with('success', 'تم إلغاء الطلب بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error cancelling order:', [
                'order_id' => $id,
                'user_id' => Auth::id(),
                'error' => $e->getMessage()
            ]);

            return redirect()->back()
                ->with('error', 'حدث خطأ أثناء إلغاء الطلب: ' . $e->getMessage());
        }
    }

    // تحميل الفاتورة
    public function customerInvoice($id)
    {
        $order = Order::with(['items.menuItem', 'user'])
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        // السماح بتحميل الفاتورة لجميع الطلبات (للمراجعة والمتابعة)
        // if (!in_array($order->status, ['completed', 'canceled'])) {
        //     return redirect()->route('customer.orders.show', $id)
        //         ->with('error', 'لا يمكن تحميل الفاتورة إلا للطلبات المكتملة أو الملغية');
        // }

        // حساب التفاصيل المالية
        $subtotal = $order->items->sum(function($item) {
            return $item->price * $item->quantity;
        });

        $tax = $subtotal * 0.15; // ضريبة 15%
        $deliveryFee = ($order->order_type === 'pickup') ? 0 : 10.00;
        $total = $subtotal + $tax + $deliveryFee;

        return view('customer.orders.invoice', compact('order', 'subtotal', 'tax', 'deliveryFee', 'total'));
    }

    // Helper methods
    private function updateInventoryForOrderItem($menuItem, $quantity)
    {
        $recipes = DB::table('recipes')
            ->where('menu_item_id', $menuItem->item_id)
            ->get();

        foreach ($recipes as $recipe) {
            $totalQuantityNeeded = $recipe->quantity * $quantity;

            // Find the inventory with the earliest expiry date
            $inventories = Inventory::where('ingredient_id', $recipe->ingredient_id)
                ->where('quantity', '>', 0)
                ->orderBy('expiry_date')
                ->get();

            $remainingQuantity = $totalQuantityNeeded;

            foreach ($inventories as $inventory) {
                if ($remainingQuantity <= 0) break;

                $quantityToDeduct = min($remainingQuantity, $inventory->quantity);
                $remainingQuantity -= $quantityToDeduct;

                // Update inventory
                $inventory->quantity -= $quantityToDeduct;
                $inventory->save();

                // Record transaction
                $transaction = InventoryTransaction::create([
                    'inventory_id' => $inventory->inventory_id,
                    'user_id' => Auth::id(),
                    'transaction_type' => 'consumption',
                    'quantity' => $quantityToDeduct,
                ]);

                // إضافة مصروف تلقائي للمخزون المستخدم
                $this->createInventoryExpense($inventory, $quantityToDeduct, $transaction->transaction_id);
            }

            // If we still have remaining quantity, throw an exception
            if ($remainingQuantity > 0) {
                throw new \Exception('عذراً، لا يوجد مخزون كافٍ من المكون: ' .
                    DB::table('ingredients')->where('ingredient_id', $recipe->ingredient_id)->value('name'));
            }
        }
    }

    private function restoreInventoryForOrder($order)
    {
        $orderItems = OrderItem::where('order_id', $order->order_id)->get();

        foreach ($orderItems as $orderItem) {
            $menuItem = MenuItem::find($orderItem->menu_item_id);
            if (!$menuItem) {
                continue; // Skip if menu item not found
            }
            $recipes = DB::table('recipes')
                ->where('menu_item_id', $orderItem->menu_item_id)
                ->get();

            foreach ($recipes as $recipe) {
                $totalQuantityToRestore = $recipe->quantity * $orderItem->quantity;

                // Get the latest inventory transaction for this ingredient
                $latestInventory = Inventory::where('ingredient_id', $recipe->ingredient_id)
                    ->orderBy('inventory_id', 'desc')
                    ->first();

                if ($latestInventory) {
                    // Update inventory
                    $latestInventory->quantity += $totalQuantityToRestore;
                    $latestInventory->save();

                    // Record transaction
                    InventoryTransaction::create([
                        'inventory_id' => $latestInventory->inventory_id,
                        'user_id' => Auth::id(),
                        'transaction_type' => 'purchase', // Using purchase to indicate adding back to inventory
                        'quantity' => $totalQuantityToRestore,
                    ]);
                }
            }
        }
    }

    private function notifyStaffAboutNewOrder($order)
    {
        // Find employee users
        $employees = DB::table('users')
            ->where('user_type', 'employee')
            ->where('is_active', true)
            ->get();

        foreach ($employees as $employee) {
            Notification::create([
                'user_id' => $employee->user_id,
                'message' => 'طلب جديد #' . $order->order_id . ' من العميل ' . Auth::user()->first_name,
                'is_read' => false,
            ]);
        }
    }

    private function notifyStaffAboutCancelledOrder($order)
    {
        // Find employee and admin users
        $staff = DB::table('users')
            ->whereIn('user_type', ['employee', 'admin'])
            ->where('is_active', true)
            ->get();

        foreach ($staff as $staffMember) {
            Notification::create([
                'user_id' => $staffMember->user_id,
                'message' => 'تم إلغاء الطلب #' . $order->order_id . ' من قبل العميل ' . Auth::user()->first_name,
                'is_read' => false,
            ]);
        }
    }

    // إنشاء مصروف تلقائي عند استخدام المخزون
    private function createInventoryExpense($inventory, $quantityUsed, $transactionId)
    {
        try {
            // الحصول على معلومات المكون
            $ingredient = DB::table('ingredients')
                ->where('ingredient_id', $inventory->ingredient_id)
                ->first();

            if (!$ingredient) {
                return;
            }

            // حساب تكلفة المخزون المستخدم
            $totalCost = $inventory->cost_per_unit * $quantityUsed;

            // إنشاء وصف المصروف
            $description = "استخدام تلقائي للمخزون: {$ingredient->name} ({$quantityUsed} {$ingredient->unit})";

            // إنشاء المصروف التلقائي
            Expense::create([
                'amount' => $totalCost,
                'category' => 'inventory_auto',
                'description' => $description,
                'expense_date' => now(),
                'recorded_by' => null, // النظام
                'payment_method' => 'inventory',
                'is_automatic' => true,
                'inventory_transaction_id' => $transactionId
            ]);

        } catch (\Exception $e) {
            // تسجيل الخطأ ولكن لا نوقف العملية
            Log::warning('خطأ في إنشاء مصروف تلقائي للمخزون:', [
                'inventory_id' => $inventory->inventory_id,
                'quantity_used' => $quantityUsed,
                'error' => $e->getMessage()
            ]);
        }
    }
}