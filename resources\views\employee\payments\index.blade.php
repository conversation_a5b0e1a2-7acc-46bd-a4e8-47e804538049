@extends('employee.layouts.app')

@section('title', 'إدارة المدفوعات - نظام إدارة المطعم')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6 flex flex-wrap items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة المدفوعات</h1>
            <p class="text-gray-600 dark:text-gray-400">عرض وإدارة المدفوعات والفواتير</p>
        </div>
        <div class="mt-4 sm:mt-0 flex space-x-2 space-x-reverse">
            <a href="{{ route('employee.orders') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                <i class="fas fa-shopping-cart ml-2"></i>
                الطلبات
            </a>
            <button id="createPaymentBtn" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                <i class="fas fa-plus-circle ml-2"></i>
                تسجيل دفعة
            </button>
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border-r-4 border-green-500 text-green-700 p-4 mb-6 rounded-md dark:bg-green-900/30 dark:text-green-500 dark:border-green-500">
        <p>{{ session('success') }}</p>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <!-- بطاقات الإحصائيات -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-500 mr-4">
                    <i class="fas fa-money-bill-wave text-2xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">إجمالي المدفوعات (اليوم)</p>
                    <p class="text-xl font-bold text-gray-800 dark:text-white">{{ number_format($todayPayments ?? 0, 2) }} د.ل</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-500 mr-4">
                    <i class="fas fa-cash-register text-2xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">المدفوعات النقدية</p>
                    <p class="text-xl font-bold text-gray-800 dark:text-white">{{ number_format($cashPayments ?? 0, 2) }} د.ل</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-500 mr-4">
                    <i class="fas fa-credit-card text-2xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">المدفوعات الإلكترونية</p>
                    <p class="text-xl font-bold text-gray-800 dark:text-white">{{ number_format($cardPayments ?? 0, 2) }} د.ل</p>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-500 mr-4">
                    <i class="fas fa-file-invoice-dollar text-2xl"></i>
                </div>
                <div>
                    <p class="text-sm text-gray-500 dark:text-gray-400">الفواتير غير المدفوعة</p>
                    <p class="text-xl font-bold text-gray-800 dark:text-white">{{ $unpaidInvoices ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">فلترة المدفوعات</h2>
        </div>
        <div class="p-4">
            <form action="{{ route('employee.payments') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طريقة الدفع</label>
                    <select id="payment_method" name="payment_method" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                        <option value="">جميع الطرق</option>
                        <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                        <option value="card" {{ request('payment_method') == 'card' ? 'selected' : '' }}>بطاقة</option>
                    </select>
                </div>
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">من تاريخ</label>
                    <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                </div>
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إلى تاريخ</label>
                    <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                        <i class="fas fa-filter ml-2"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المدفوعات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">سجل المدفوعات</h2>
        </div>
        
        @if(count($payments ?? []) > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم العملية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم الطلب
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المبلغ
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            طريقة الدفع
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            تاريخ العملية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($payments ?? [] as $payment)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ $payment->payment_id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            <a href="{{ route('employee.orders.show', $payment->order_id) }}" class="text-primary hover:underline">
                                #{{ $payment->order_id }}
                            </a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            @if(isset($payment->order->user))
                            {{ $payment->order->user->first_name }} {{ $payment->order->user->last_name }}
                            @else
                            <span class="text-gray-500 dark:text-gray-400">غير متوفر</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {{ number_format($payment->amount, 2) }} د.ل
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $payment->payment_method == 'cash' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' }}">
                                {{ $payment->payment_method == 'cash' ? 'نقدي' : 'بطاقة' }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ \Carbon\Carbon::parse($payment->transaction_date)->format('Y-m-d H:i') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="#" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 print-receipt" data-payment-id="{{ $payment->payment_id }}" title="طباعة الإيصال">
                                    <i class="fas fa-print"></i>
                                </a>
                                <a href="{{ route('employee.orders.show', $payment->order_id) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" title="عرض الطلب">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $payments->links() ?? '' }}
        </div>
        @else
        <div class="text-center py-8">
            <div class="text-5xl text-gray-300 dark:text-gray-600 mb-4">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">لا توجد مدفوعات</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">لم يتم العثور على أي مدفوعات تطابق معايير البحث</p>
        </div>
        @endif
    </div>
</div>

<!-- نافذة منبثقة لإنشاء دفعة جديدة -->
<div id="createPaymentModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-right sm:w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            تسجيل دفعة جديدة
                        </h3>
                        <div class="mt-4">
                            <form id="paymentForm" action="{{ route('employee.payments.store') }}" method="POST">
                                @csrf
                                <div class="mb-4">
                                    <label for="order_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">رقم الطلب</label>
                                    <input type="text" id="order_id" name="order_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white" required>
                                </div>
                                <div class="mb-4">
                                    <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المبلغ</label>
                                    <input type="number" id="amount" name="amount" step="0.01" min="0.01" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white" required>
                                </div>
                                <div class="mb-4">
                                    <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طريقة الدفع</label>
                                    <select id="payment_method" name="payment_method" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white" required>
                                        <option value="cash">نقدي</option>
                                        <option value="card">بطاقة</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="submitPayment" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    تسجيل الدفعة
                </button>
                <button type="button" id="cancelPayment" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إدارة النافذة المنبثقة لإنشاء دفعة جديدة
        const createPaymentBtn = document.getElementById('createPaymentBtn');
        const createPaymentModal = document.getElementById('createPaymentModal');
        const cancelPayment = document.getElementById('cancelPayment');
        const submitPayment = document.getElementById('submitPayment');
        const paymentForm = document.getElementById('paymentForm');
        
        createPaymentBtn.addEventListener('click', function() {
            createPaymentModal.classList.remove('hidden');
        });
        
        cancelPayment.addEventListener('click', function() {
            createPaymentModal.classList.add('hidden');
        });
        
        submitPayment.addEventListener('click', function() {
            paymentForm.submit();
        });
        
        // طباعة الإيصال
        const printButtons = document.querySelectorAll('.print-receipt');
        
        printButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const paymentId = this.getAttribute('data-payment-id');
                printReceipt(paymentId);
            });
        });
        
        function printReceipt(paymentId) {
            // يمكن تنفيذ طباعة الإيصال هنا
            alert('جاري طباعة إيصال للدفعة رقم ' + paymentId);
        }
    });
</script>
@endsection
