@extends('layouts.admin')

@section('title', 'إضافة طلب جديد')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إضافة طلب جديد</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">إنشاء طلب جديد للعملاء</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.orders') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للطلبات</span>
        </a>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
        <form action="{{ route('admin.orders.store') }}" method="POST" id="orderForm">
            @csrf

            <div class="mb-6">
                <div class="flex items-center mb-4">
                    <input type="radio" id="registered_customer" name="customer_type" value="registered" class="customer-type-radio mr-2" checked>
                    <label for="registered_customer" class="text-sm font-medium text-gray-700 dark:text-gray-300">عميل مسجل</label>

                    <input type="radio" id="guest_customer" name="customer_type" value="guest" class="customer-type-radio mr-2 ml-6">
                    <label for="guest_customer" class="text-sm font-medium text-gray-700 dark:text-gray-300">زبون عادي (غير مسجل)</label>
                </div>

                <div id="registered_customer_fields" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العميل</label>
                        <select id="user_id" name="user_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="">اختر العميل</option>
                            @foreach(\App\Models\User::where('user_type', 'customer')->orderBy('first_name')->get() as $user)
                                <option value="{{ $user->user_id }}" {{ old('user_id') == $user->user_id ? 'selected' : '' }}>
                                    {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
                                </option>
                            @endforeach
                        </select>
                        @error('user_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="table_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطاولة (اختياري)</label>
                        <select id="table_id" name="table_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="">بدون طاولة (طلب خارجي)</option>
                            @foreach($tables as $table)
                                <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }} {{ $table->status != 'available' ? 'disabled' : '' }}>
                                    طاولة {{ $table->table_number }} ({{ $table->capacity }} أشخاص) {{ $table->status != 'available' ? '- مشغولة' : '' }}
                                </option>
                            @endforeach
                        </select>
                        @error('table_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <div id="guest_customer_fields" class="grid grid-cols-1 md:grid-cols-2 gap-6" style="display: none;">
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الزبون</label>
                        <input type="text" id="customer_name" name="customer_name" value="{{ old('customer_name') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                        @error('customer_name')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف (اختياري)</label>
                        <input type="text" id="customer_phone" name="customer_phone" value="{{ old('customer_phone') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                        @error('customer_phone')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="table_id_guest" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الطاولة (اختياري)</label>
                        <select id="table_id_guest" name="table_id" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="">بدون طاولة (طلب خارجي)</option>
                            @foreach($tables as $table)
                                <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }} {{ $table->status != 'available' ? 'disabled' : '' }}>
                                    طاولة {{ $table->table_number }} ({{ $table->capacity }} أشخاص) {{ $table->status != 'available' ? '- مشغولة' : '' }}
                                </option>
                            @endforeach
                        </select>
                        @error('table_id')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">عناصر الطلب</h3>

                <div class="mb-4">
                    <div class="flex flex-wrap gap-2">
                        @foreach($menuItems as $category => $items)
                            <button type="button" class="category-btn px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all" data-category="{{ $category }}">
                                {{ $category }}
                            </button>
                        @endforeach
                    </div>
                </div>

                @foreach($menuItems as $category => $items)
                    <div class="menu-category mb-6" id="category-{{ $category }}" style="display: none;">
                        <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">{{ $category }}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            @foreach($items as $item)
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                    <div class="flex justify-between items-start mb-2">
                                        <div>
                                            <h5 class="font-medium text-gray-800 dark:text-white">{{ $item->name }}</h5>
                                            <p class="text-sm text-gray-500 dark:text-gray-400">{{ number_format($item->price, 2) }} د.ل</p>
                                        </div>
                                        <button type="button" class="add-item-btn bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-primary/90 transition-all" data-id="{{ $item->item_id }}" data-name="{{ $item->name }}" data-price="{{ $item->price }}">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                    @if($item->description)
                                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $item->description }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach

                <div class="mt-6">
                    <h4 class="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">العناصر المختارة</h4>
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <table class="w-full mb-4" id="selected-items-table">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-600">
                                    <th class="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">العنصر</th>
                                    <th class="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">السعر</th>
                                    <th class="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">الكمية</th>
                                    <th class="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">المجموع</th>
                                    <th class="text-right py-2 text-sm font-medium text-gray-500 dark:text-gray-400">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="selected-items-body">
                                <tr id="no-items-row">
                                    <td colspan="5" class="py-4 text-center text-gray-500 dark:text-gray-400">لم يتم إضافة أي عناصر بعد</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="border-t border-gray-200 dark:border-gray-600">
                                    <td colspan="3" class="py-2 text-left font-medium text-gray-700 dark:text-gray-300">المجموع الكلي</td>
                                    <td class="py-2 text-right font-bold text-gray-800 dark:text-white" id="total-amount">0.00 د.ل</td>
                                    <td></td>
                                </tr>
                            </tfoot>
                        </table>

                        <div id="selected-items-container"></div>

                        @error('items')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-2 space-x-reverse">
                <a href="{{ route('admin.orders') }}" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">إلغاء</a>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">إنشاء الطلب</button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle between registered customer and guest customer fields
        const customerTypeRadios = document.querySelectorAll('.customer-type-radio');
        customerTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'registered') {
                    document.getElementById('registered_customer_fields').style.display = 'grid';
                    document.getElementById('guest_customer_fields').style.display = 'none';
                    // Clear guest customer fields
                    document.getElementById('customer_name').value = '';
                    document.getElementById('customer_phone').value = '';
                } else {
                    document.getElementById('registered_customer_fields').style.display = 'none';
                    document.getElementById('guest_customer_fields').style.display = 'grid';
                    // Clear registered customer fields
                    document.getElementById('user_id').value = '';
                }
            });
        });

        // Show first category by default
        const firstCategoryBtn = document.querySelector('.category-btn');
        if (firstCategoryBtn) {
            const firstCategory = firstCategoryBtn.dataset.category;
            document.getElementById('category-' + firstCategory).style.display = 'block';
            firstCategoryBtn.classList.add('bg-primary', 'text-white');
            firstCategoryBtn.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-white');
        }

        // Category buttons
        document.querySelectorAll('.category-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const category = this.dataset.category;

                // Hide all categories
                document.querySelectorAll('.menu-category').forEach(div => {
                    div.style.display = 'none';
                });

                // Reset all buttons
                document.querySelectorAll('.category-btn').forEach(button => {
                    button.classList.remove('bg-primary', 'text-white');
                    button.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-white');
                });

                // Show selected category
                document.getElementById('category-' + category).style.display = 'block';

                // Highlight selected button
                this.classList.add('bg-primary', 'text-white');
                this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-800', 'dark:text-white');
            });
        });

        // Add item buttons
        document.querySelectorAll('.add-item-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const itemId = this.dataset.id;
                const itemName = this.dataset.name;
                const itemPrice = parseFloat(this.dataset.price);

                addItemToOrder(itemId, itemName, itemPrice);
            });
        });

        // Form submission
        document.getElementById('orderForm').addEventListener('submit', function(e) {
            const selectedItems = document.querySelectorAll('.item-row');
            if (selectedItems.length === 0) {
                e.preventDefault();
                alert('يرجى إضافة عنصر واحد على الأقل إلى الطلب');
            }
        });
    });

    function addItemToOrder(itemId, itemName, itemPrice) {
        // Hide "no items" row
        document.getElementById('no-items-row').style.display = 'none';

        // Check if item already exists
        const existingRow = document.getElementById('item-row-' + itemId);
        if (existingRow) {
            // Update quantity
            const quantityInput = document.getElementById('item-quantity-' + itemId);
            const currentQuantity = parseInt(quantityInput.value);
            quantityInput.value = currentQuantity + 1;

            // Update subtotal
            const subtotalElement = document.getElementById('item-subtotal-' + itemId);
            const newSubtotal = (currentQuantity + 1) * itemPrice;
            subtotalElement.textContent = newSubtotal.toFixed(2) + ' د.ل';
        } else {
            // Create new row
            const tbody = document.getElementById('selected-items-body');
            const newRow = document.createElement('tr');
            newRow.id = 'item-row-' + itemId;
            newRow.className = 'item-row';

            newRow.innerHTML = `
                <td class="py-2 text-sm text-gray-800 dark:text-white">${itemName}</td>
                <td class="py-2 text-sm text-gray-500 dark:text-gray-400">${itemPrice.toFixed(2)} د.ل</td>
                <td class="py-2">
                    <input type="number" id="item-quantity-${itemId}" name="items[${itemId}][quantity]" value="1" min="1" class="w-16 px-2 py-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white" onchange="updateSubtotal(${itemId}, ${itemPrice})">
                    <input type="hidden" name="items[${itemId}][id]" value="${itemId}">
                </td>
                <td class="py-2 text-sm text-gray-800 dark:text-white" id="item-subtotal-${itemId}">${itemPrice.toFixed(2)} د.ل</td>
                <td class="py-2">
                    <button type="button" class="text-red-500 hover:text-red-700" onclick="removeItem(${itemId})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;

            tbody.appendChild(newRow);
        }

        updateTotalAmount();
    }

    function updateSubtotal(itemId, itemPrice) {
        const quantityInput = document.getElementById('item-quantity-' + itemId);
        const quantity = parseInt(quantityInput.value);

        if (quantity < 1) {
            quantityInput.value = 1;
            return;
        }

        const subtotalElement = document.getElementById('item-subtotal-' + itemId);
        const subtotal = quantity * itemPrice;
        subtotalElement.textContent = subtotal.toFixed(2) + ' د.ل';

        updateTotalAmount();
    }

    function removeItem(itemId) {
        const row = document.getElementById('item-row-' + itemId);
        row.remove();

        // Show "no items" row if no items left
        const itemRows = document.querySelectorAll('.item-row');
        if (itemRows.length === 0) {
            document.getElementById('no-items-row').style.display = 'table-row';
        }

        updateTotalAmount();
    }

    function updateTotalAmount() {
        let total = 0;
        document.querySelectorAll('.item-row').forEach(row => {
            const itemId = row.id.replace('item-row-', '');
            const quantityInput = document.getElementById('item-quantity-' + itemId);
            const quantity = parseInt(quantityInput.value);
            const priceText = row.querySelector('td:nth-child(2)').textContent;
            const price = parseFloat(priceText.replace(' د.ل', ''));

            total += price * quantity;
        });

        document.getElementById('total-amount').textContent = total.toFixed(2) + ' د.ل';
    }
</script>
@endsection
