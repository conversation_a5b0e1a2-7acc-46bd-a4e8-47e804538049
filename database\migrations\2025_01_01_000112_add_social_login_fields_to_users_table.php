<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // إضافة أعمدة تسجيل الدخول بالحسابات الاجتماعية
            $table->string('google_id')->nullable()->after('phone');
            $table->string('facebook_id')->nullable()->after('google_id');
            $table->string('apple_id')->nullable()->after('facebook_id');
            $table->string('avatar')->nullable()->after('apple_id');
            $table->timestamp('email_verified_at')->nullable()->after('avatar');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // حذف أعمدة تسجيل الدخول بالحسابات الاجتماعية
            $table->dropColumn(['google_id', 'facebook_id', 'apple_id', 'avatar', 'email_verified_at']);
        });
    }
};
