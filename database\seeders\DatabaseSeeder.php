<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{




    public function run()
    {
        // إنشاء الأدوار الأساسية
        Role::firstOrCreate(['name' => 'admin']);
        Role::firstOrCreate(['name' => 'employee']);
        Role::firstOrCreate(['name' => 'customer']);

        // إنشاء مستخدم مدير
        $admin = User::create([
            'first_name' => 'Admin',
            'last_name' => 'admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('A178a2002'),
            'phone' => '0919676123',
            'user_type' => 'admin',
            'is_active' => true
        ]);
        $admin->assignRole('admin');

        // تشغيل بذور المصروفات
        $this->call(ExpenseSeeder::class);

        // تشغيل بذور قائمة الطعام
        $this->call(MenuItemSeeder::class);

        // تشغيل بذور الطاولات
        $this->call(TableSeeder::class);

        // تشغيل بذور المدفوعات والطلبات
        $this->call(PaymentSeeder::class);

        // تشغيل بذور الإشعارات
        $this->call(NotificationSeeder::class);
    }
}//> Spatie\Permission\Models\Role::all();