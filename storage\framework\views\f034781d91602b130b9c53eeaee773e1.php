<aside id="sidebar" class="md:flex md:flex-col w-64 bg-white dark:bg-gray-800 shadow-lg sidebar-transition z-20 fixed md:relative h-full">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex flex-col items-center flex-1">
                <span class="text-2xl font-bold text-primary">Eat Hub</span>
                <span class="px-3 py-1 bg-primary/10 text-primary text-xs rounded-full mt-2">لوحة التحكم</span>
            </div>
            <?php if(Auth::user()->user_type == 'admin'): ?>
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="p-2 rounded-lg text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
               title="الانتقال لواجهة الموظف">
                <i class="fas fa-user-tie text-sm"></i>
            </a>
            <?php elseif(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin')): ?>
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="p-2 rounded-lg text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors"
               title="العودة لواجهة الموظف">
                <i class="fas fa-arrow-left text-sm"></i>
            </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="py-2 flex flex-col flex-1 overflow-y-auto">
        <nav class="px-3 space-y-1">
            <a href="<?php echo e(route('admin.dashboard')); ?>" data-page="dashboard" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-tachometer-alt <?php echo e(request()->routeIs('admin.dashboard') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">لوحة التحكم</span>
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.view')): ?>
            <a href="<?php echo e(route('admin.users.index')); ?>" data-page="users" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.users*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-users <?php echo e(request()->routeIs('admin.users*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">إدارة المستخدمين</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('menu.view')): ?>
            <a href="<?php echo e(route('admin.menu')); ?>" data-page="menu" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.menu*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-utensils <?php echo e(request()->routeIs('admin.menu*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">قائمة الطعام</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('inventory.view')): ?>
            <a href="<?php echo e(route('admin.inventory')); ?>" data-page="inventory" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.inventory*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-warehouse <?php echo e(request()->routeIs('admin.inventory*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">إدارة المخزون</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('orders.view')): ?>
            <a href="<?php echo e(route('admin.orders.index')); ?>" data-page="orders" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.orders*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-shopping-cart <?php echo e(request()->routeIs('admin.orders*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">إدارة الطلبات</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expenses.view')): ?>
            <a href="<?php echo e(route('admin.expenses')); ?>" data-page="expenses" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.expenses*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-money-bill-wave <?php echo e(request()->routeIs('admin.expenses*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">المصروفات</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('reports.view')): ?>
            <a href="<?php echo e(route('admin.reports')); ?>" data-page="reports" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.reports*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-chart-bar <?php echo e(request()->routeIs('admin.reports*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">التقارير</span>
            </a>
            <?php endif; ?>

            <a href="<?php echo e(route('admin.notifications')); ?>" data-page="notifications" class="sidebar-notification-link nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.notifications*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-bell sidebar-bell-icon <?php echo e(request()->routeIs('admin.notifications*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">الإشعارات</span>
                <span id="notification-badge" class="mr-auto bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold">0</span>
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.permissions')): ?>
            <a href="<?php echo e(route('admin.permissions.index')); ?>" data-page="permissions" class="nav-link flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.permissions*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                <i class="fas fa-user-shield <?php echo e(request()->routeIs('admin.permissions*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                <span class="font-medium">إدارة الصلاحيات</span>
            </a>
            <?php endif; ?>
        </nav>

        <div class="mt-auto px-3 py-3">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-3 space-y-1">
                <a href="<?php echo e(route('admin.settings')); ?>" class="flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-primary/5 hover:text-primary transition-all duration-200 group <?php echo e(request()->routeIs('admin.settings*') ? 'bg-primary text-white shadow-md' : ''); ?>">
                    <i class="fas fa-cog <?php echo e(request()->routeIs('admin.settings*') ? 'text-white' : 'text-primary'); ?> ml-3 w-5"></i>
                    <span class="font-medium">الإعدادات</span>
                </a>
                <a href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" class="flex items-center px-3 py-2.5 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-red-50 hover:text-red-600 transition-all duration-200 group">
                    <i class="fas fa-sign-out-alt text-red-500 ml-3 w-5"></i>
                    <span class="font-medium">تسجيل الخروج</span>
                </a>
                <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
                    <?php echo csrf_field(); ?>
                </form>
            </div>
        </div>
    </div>
</aside>

<!-- شريط القائمة للجوال -->
<div id="mobileMenu" class="md:hidden bg-white dark:bg-gray-800 shadow-lg sidebar-transition fixed inset-0 w-64 transform -translate-x-full z-30">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <div class="flex items-center">
            <span class="text-2xl font-bold text-primary">Eat Hub</span>
            <?php if(Auth::user()->user_type == 'admin'): ?>
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="mr-3 p-2 rounded-lg text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
               title="الانتقال لواجهة الموظف">
                <i class="fas fa-user-tie text-sm"></i>
            </a>
            <?php elseif(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin')): ?>
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="mr-3 p-2 rounded-lg text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors"
               title="العودة لواجهة الموظف">
                <i class="fas fa-arrow-left text-sm"></i>
            </a>
            <?php endif; ?>
        </div>
        <button id="closeMobileMenu" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>

    <div class="py-4">
        <nav class="px-4 space-y-1">
            <a href="<?php echo e(route('admin.dashboard')); ?>" data-page="dashboard" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.dashboard') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-tachometer-alt text-primary ml-3"></i>
                <span>لوحة التحكم</span>
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.view')): ?>
            <a href="<?php echo e(route('admin.users.index')); ?>" data-page="users" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.users*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-users text-primary ml-3"></i>
                <span>إدارة المستخدمين</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('menu.view')): ?>
            <a href="<?php echo e(route('admin.menu')); ?>" data-page="menu" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.menu*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-utensils text-primary ml-3"></i>
                <span>قائمة الطعام</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('inventory.view')): ?>
            <a href="<?php echo e(route('admin.inventory')); ?>" data-page="inventory" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.inventory*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-warehouse text-primary ml-3"></i>
                <span>إدارة المخزون</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('orders.view')): ?>
            <a href="<?php echo e(route('admin.orders')); ?>" data-page="orders" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.orders*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-shopping-cart text-primary ml-3"></i>
                <span>إدارة الطلبات</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('expenses.view')): ?>
            <a href="<?php echo e(route('admin.expenses')); ?>" data-page="expenses" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.expenses*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-money-bill-wave text-primary ml-3"></i>
                <span>المصروفات</span>
            </a>
            <?php endif; ?>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('reports.view')): ?>
            <a href="<?php echo e(route('admin.reports')); ?>" data-page="reports" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.reports*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-chart-bar text-primary ml-3"></i>
                <span>التقارير</span>
            </a>
            <?php endif; ?>

            <a href="<?php echo e(route('admin.notifications')); ?>" data-page="notifications" class="mobile-sidebar-notification-link mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.notifications*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-bell mobile-sidebar-bell-icon text-primary ml-3"></i>
                <span>الإشعارات</span>
                <span id="mobile-notification-badge" class="mr-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">0</span>
            </a>

            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('users.permissions')): ?>
            <a href="<?php echo e(route('admin.permissions.index')); ?>" data-page="permissions" class="mobile-nav-link flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group <?php echo e(request()->routeIs('admin.permissions*') ? 'bg-primary/10 text-primary' : ''); ?>">
                <i class="fas fa-user-shield text-primary ml-3"></i>
                <span>إدارة الصلاحيات</span>
            </a>
            <?php endif; ?>
        </nav>
    </div>
</div><?php /**PATH D:\ccss450\cs450level10\resources\views/includes/sidebar.blade.php ENDPATH**/ ?>