<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MenuItem;
use App\Models\Order;
use App\Models\Reservation;
use Illuminate\Support\Facades\Auth;

class PublicSearchController extends Controller
{
    /**
     * البحث السريع (AJAX)
     */
    public function quickSearch(Request $request)
    {
        $query = $request->get('q', '');
        
        if (empty($query) || strlen($query) < 2) {
            return response()->json([
                'success' => false,
                'message' => 'يرجى إدخال حرفين على الأقل للبحث'
            ]);
        }

        $results = [];

        // البحث في قائمة الطعام
        $menuItems = MenuItem::where('is_available', true)
            ->where(function($q) use ($query) {
                $q->where('name', 'LIKE', "%{$query}%")
                  ->orWhere('description', 'LIKE', "%{$query}%")
                  ->orWhere('category', 'LIKE', "%{$query}%");
            })
            ->limit(5)
            ->get();

        foreach ($menuItems as $item) {
            $results[] = [
                'type' => 'menu',
                'id' => $item->item_id,
                'title' => $item->name,
                'description' => $item->description,
                'price' => $item->price . ' د.ل',
                'category' => $item->category,
                'image' => $item->image_path,
                'url' => route('customer.menu') . '#item-' . $item->item_id,
                'icon' => 'fas fa-utensils'
            ];
        }

        // إذا كان المستخدم مسجل دخول، ابحث في طلباته وحجوزاته
        if (Auth::check()) {
            // البحث في الطلبات
            $orders = Order::where('user_id', Auth::id())
                ->where(function($q) use ($query) {
                    $q->where('order_id', 'LIKE', "%{$query}%")
                      ->orWhere('status', 'LIKE', "%{$query}%");
                })
                ->limit(3)
                ->get();

            foreach ($orders as $order) {
                $results[] = [
                    'type' => 'order',
                    'id' => $order->order_id,
                    'title' => 'طلب #' . $order->order_id,
                    'description' => 'المبلغ: ' . $order->total_amount . ' د.ل - ' . $this->getStatusText($order->status),
                    'date' => $order->created_at->format('Y-m-d'),
                    'status' => $order->status,
                    'url' => route('customer.orders.show', $order->order_id),
                    'icon' => 'fas fa-shopping-bag'
                ];
            }

            // البحث في الحجوزات
            $reservations = Reservation::where('user_id', Auth::id())
                ->where(function($q) use ($query) {
                    $q->where('reservation_id', 'LIKE', "%{$query}%")
                      ->orWhere('status', 'LIKE', "%{$query}%");
                })
                ->with('table')
                ->limit(3)
                ->get();

            foreach ($reservations as $reservation) {
                $results[] = [
                    'type' => 'reservation',
                    'id' => $reservation->reservation_id,
                    'title' => 'حجز #' . $reservation->reservation_id,
                    'description' => 'طاولة #' . ($reservation->table->table_number ?? 'غير محدد') . ' - ' . $reservation->party_size . ' أشخاص',
                    'date' => $reservation->reservation_time->format('Y-m-d H:i'),
                    'status' => $reservation->status,
                    'url' => route('customer.reservations.show', $reservation->reservation_id),
                    'icon' => 'fas fa-calendar-check'
                ];
            }
        }

        return response()->json([
            'success' => true,
            'query' => $query,
            'results' => $results,
            'total' => count($results)
        ]);
    }

    /**
     * صفحة نتائج البحث المفصلة
     */
    public function searchResults(Request $request)
    {
        $query = $request->get('q', '');
        $category = $request->get('category', 'all');
        
        $results = [
            'menu' => [],
            'orders' => [],
            'reservations' => []
        ];

        if (!empty($query)) {
            // البحث في قائمة الطعام
            $menuQuery = MenuItem::where('is_available', true)
                ->where(function($q) use ($query) {
                    $q->where('name', 'LIKE', "%{$query}%")
                      ->orWhere('description', 'LIKE', "%{$query}%")
                      ->orWhere('category', 'LIKE', "%{$query}%");
                });

            if ($category !== 'all' && $category !== 'menu') {
                $menuQuery->where('category', $category);
            }

            $results['menu'] = $menuQuery->paginate(12);

            // إذا كان المستخدم مسجل دخول
            if (Auth::check()) {
                // البحث في الطلبات
                $results['orders'] = Order::where('user_id', Auth::id())
                    ->where(function($q) use ($query) {
                        $q->where('order_id', 'LIKE', "%{$query}%")
                          ->orWhere('status', 'LIKE', "%{$query}%");
                    })
                    ->orderBy('created_at', 'desc')
                    ->paginate(10);

                // البحث في الحجوزات
                $results['reservations'] = Reservation::where('user_id', Auth::id())
                    ->where(function($q) use ($query) {
                        $q->where('reservation_id', 'LIKE', "%{$query}%")
                          ->orWhere('status', 'LIKE', "%{$query}%");
                    })
                    ->with('table')
                    ->orderBy('created_at', 'desc')
                    ->paginate(10);
            }
        }

        return view('customer.search.results', compact('query', 'category', 'results'));
    }

    /**
     * تحويل حالة الطلب إلى نص عربي
     */
    private function getStatusText($status)
    {
        $statuses = [
            'pending' => 'في الانتظار',
            'preparing' => 'قيد التحضير',
            'completed' => 'مكتمل',
            'canceled' => 'ملغي'
        ];

        return $statuses[$status] ?? $status;
    }

    /**
     * اقتراحات البحث
     */
    public function suggestions(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }

        $suggestions = [];

        // اقتراحات من أسماء الأطباق
        $menuNames = MenuItem::where('is_available', true)
            ->where('name', 'LIKE', "%{$query}%")
            ->pluck('name')
            ->take(5)
            ->toArray();

        // اقتراحات من الفئات
        $categories = MenuItem::where('is_available', true)
            ->where('category', 'LIKE', "%{$query}%")
            ->distinct()
            ->pluck('category')
            ->take(3)
            ->toArray();

        $suggestions = array_merge($menuNames, $categories);

        return response()->json(array_unique($suggestions));
    }
}
