# ترتيب الهجرات الصحيح لنظام إدارة المطعم

## المشكلة الحالية
الهجرات غير مرتبة بشكل صحيح مما يسبب مشاكل عند تشغيلها على جهاز جديد بسبب التبعيات بين الجداول.

## الترتيب الصحيح للهجرات

### 1. الجداول الأساسية (بدون تبعيات خارجية)
```
2025_01_01_000001_create_users_table.php
2025_01_01_000002_create_sessions_table.php
2025_01_01_000003_create_cache_table.php
2025_01_01_000004_create_permission_tables.php
2025_01_01_000005_create_settings_table.php
```

### 2. ج<PERSON><PERSON><PERSON><PERSON> المواد والمخزون
```
2025_01_01_000010_create_ingredients_table.php
2025_01_01_000011_create_inventory_table.php
2025_01_01_000012_create_inventory_transactions_table.php
```

### 3. جداول القائمة والوصفات
```
2025_01_01_000020_create_menu_items_table.php
2025_01_01_000021_create_recipes_table.php
```

### 4. جداول الطاولات والحجوزات
```
2025_01_01_000030_create_tables_table.php
2025_01_01_000031_create_reservations_table.php
```

### 5. جداول الطلبات والمدفوعات
```
2025_01_01_000040_create_orders_table.php
2025_01_01_000041_create_order_items_table.php
2025_01_01_000042_create_payments_table.php
2025_01_01_000043_create_cart_table.php
```

### 6. جداول التقييمات والإشعارات
```
2025_01_01_000050_create_reviews_table.php
2025_01_01_000051_create_notifications_table.php
```

### 7. جداول التقارير المالية
```
2025_01_01_000060_create_expenses_table.php
2025_01_01_000061_create_invoices_table.php
2025_01_01_000062_create_financial_reports_table.php
2025_01_01_000063_create_report_sources_table.php
```

### 8. تعديلات الجداول (ALTER TABLE)
```
2025_01_01_000100_add_image_path_to_menu_items_table.php
2025_01_01_000101_add_expiry_date_to_ingredients_table.php
2025_01_01_000102_add_password_hash_to_users_table.php
2025_01_01_000103_add_cost_per_unit_to_ingredients_table.php
2025_01_01_000104_add_description_to_menu_items_table.php
2025_01_01_000105_add_menu_item_id_to_reviews_table.php
2025_01_01_000106_update_expenses_table.php
2025_01_01_000107_add_location_to_tables_table.php
2025_01_01_000108_make_user_id_nullable_in_orders_table.php
2025_01_01_000109_add_customer_info_to_orders_table.php
2025_01_01_000110_add_is_featured_to_menu_items_table.php
2025_01_01_000111_add_address_to_users_table.php
2025_01_01_000112_add_social_login_fields_to_users_table.php
2025_01_01_000113_add_is_featured_column_to_menu_items_table.php
2025_01_01_000114_add_delivery_fields_to_orders_table.php
2025_01_01_000115_add_party_size_and_special_requests_to_reservations_table.php
2025_01_01_000116_make_title_nullable_in_notifications_table.php
2025_01_01_000117_add_offer_fields_to_reservations_table.php
2025_01_01_000118_add_columns_to_notifications_table.php
2025_01_01_000119_add_is_approved_to_reviews_table.php
2025_01_01_000120_ensure_notifications_table_structure.php
2025_01_01_000121_add_automatic_fields_to_expenses_table.php
2025_01_01_000122_add_theme_preference_to_users_table.php
```

## خطوات إعادة الترتيب

### الخطوة 1: نسخ احتياطي
```bash
# إنشاء نسخة احتياطية من مجلد الهجرات
cp -r database/migrations database/migrations_backup
```

### الخطوة 2: إعادة تسمية الملفات
سيتم إعادة تسمية جميع ملفات الهجرات حسب الترتيب الجديد.

### الخطوة 3: تنظيف قاعدة البيانات
```bash
# حذف جميع الجداول وإعادة تشغيل الهجرات
php artisan migrate:fresh
```

### الخطوة 4: تشغيل الهجرات الجديدة
```bash
# تشغيل الهجرات بالترتيب الجديد
php artisan migrate
```

## فوائد الترتيب الجديد

1. **حل مشاكل التبعيات**: الجداول الأساسية تُنشأ أولاً
2. **سهولة النشر**: يمكن تشغيل الهجرات على أي جهاز جديد بدون مشاكل
3. **ترتيب منطقي**: الهجرات مرتبة حسب الأهمية والتبعيات
4. **سهولة الصيانة**: يمكن إضافة هجرات جديدة بسهولة

## ملاحظات مهمة

- يجب عمل نسخة احتياطية قبل البدء
- تأكد من عدم وجود بيانات مهمة في قاعدة البيانات
- اختبر الهجرات على بيئة تطوير أولاً
- تأكد من تحديث ملفات seeder إذا لزم الأمر
