<script>
    // متغيرات عامة
    let currentPage = 'home';
    let isLoggedIn = {{ auth()->check() ? 'true' : 'false' }};

    // إدارة الصفحات
    function showPage(pageId) {
        // إخفاء جميع الصفحات
        document.querySelectorAll('.page').forEach(page => {
            page.classList.add('hidden');
        });

        // إظهار الصفحة المطلوبة
        const targetPage = document.getElementById(pageId + '-page');
        if (targetPage) {
            targetPage.classList.remove('hidden');
            currentPage = pageId;
        }

        // تحديث حالة الروابط في القائمة
        updateNavLinks(pageId);
    }

    // تحديث حالة الروابط
    function updateNavLinks(activePageId) {
        // إزالة الفئة النشطة من جميع الروابط
        document.querySelectorAll('.nav-link, .mobile-nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // إضافة الفئة النشطة للرابط المحدد
        document.querySelectorAll(`[data-page="${activePageId}"]`).forEach(link => {
            link.classList.add('active');
        });
    }

    // تم نقل دوال الوضع المظلم إلى ملف منفصل: /js/dark-mode.js

    // إدارة قائمة المستخدم
    function toggleUserMenu() {
        const userMenu = document.getElementById('userMenu');
        userMenu.classList.toggle('hidden');
    }

    // إدارة قائمة الجوال
    function toggleMobileMenu() {
        const mobileMenu = document.getElementById('mobileMenu');
        mobileMenu.classList.toggle('hidden');
    }

    // تحديث واجهة المستخدم بناءً على حالة تسجيل الدخول
    function updateUIForAuthState() {
        const cartButton = document.getElementById('cartButton');
        const notificationsButton = document.getElementById('notificationsButton');
        const mobileUserNav = document.getElementById('mobileUserNav');

        if (isLoggedIn) {
            if (cartButton) cartButton.classList.remove('hidden');
            if (notificationsButton) notificationsButton.classList.remove('hidden');
            if (mobileUserNav) mobileUserNav.classList.remove('hidden');
        } else {
            if (cartButton) cartButton.classList.add('hidden');
            if (notificationsButton) notificationsButton.classList.add('hidden');
            if (mobileUserNav) mobileUserNav.classList.add('hidden');
        }
    }

    // إعداد مستمعي الأحداث
    document.addEventListener('DOMContentLoaded', function() {
        // تم نقل إعدادات الوضع المظلم إلى ملف منفصل

        // إعداد التنقل بين الصفحات
        document.querySelectorAll('[data-page]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const pageId = this.getAttribute('data-page');
                showPage(pageId);

                // إغلاق قائمة الجوال إذا كانت مفتوحة
                const mobileMenu = document.getElementById('mobileMenu');
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenu.classList.add('hidden');
                }
            });
        });

        // تم نقل إعداد زر الوضع المظلم إلى ملف منفصل

        // زر قائمة المستخدم
        const userMenuBtn = document.getElementById('userMenuBtn');
        if (userMenuBtn) {
            userMenuBtn.addEventListener('click', toggleUserMenu);
        }

        // زر قائمة الجوال
        document.getElementById('mobileMenuToggle').addEventListener('click', toggleMobileMenu);

        // إغلاق قائمة المستخدم عند النقر خارجها
        document.addEventListener('click', function(e) {
            const userControls = document.getElementById('userControls');
            const userMenu = document.getElementById('userMenu');

            if (userControls && userMenu && !userControls.contains(e.target)) {
                userMenu.classList.add('hidden');
            }
        });

        // تحديث واجهة المستخدم
        updateUIForAuthState();

        // تحديث عداد السلة
        loadCartCount();

        // إظهار الصفحة الرئيسية افتراضياً
        showPage('home');

        // تم نقل جميع إعدادات الوضع المظلم إلى ملف منفصل
    });

    // تأثير التمرير على شريط التنقل
    window.addEventListener('scroll', function() {
        const navbar = document.getElementById('navbar');
        if (window.scrollY > 50) {
            navbar.classList.add('navbar-fixed');
        } else {
            navbar.classList.remove('navbar-fixed');
        }
    });

    // دالة تحديث عداد السلة
    function loadCartCount() {
        const cartButton = document.getElementById('cartButton');
        if (!cartButton) return; // إذا لم يكن المستخدم مسجل دخول

        fetch('/customer/cart/count')
            .then(response => response.json())
            .then(data => {
                const cartBadge = cartButton.querySelector('span');

                if (data.count > 0) {
                    cartBadge.textContent = data.count;
                } else {
                    cartBadge.textContent = '0';
                }
            })
            .catch(error => console.log('Error loading cart count:', error));
    }

    // إضافة للسلة
    function addToCart(itemId, itemName, itemPrice) {
        if (!isLoggedIn) {
            showNotification('يجب تسجيل الدخول أولاً', 'error');
            return;
        }

        const button = document.querySelector(`[data-item-id="${itemId}"]`);
        if (!button) return;

        const originalText = button.innerHTML;

        // تغيير النص أثناء التحميل
        button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الإضافة...';
        button.disabled = true;

        fetch('/customer/cart/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                menu_item_id: itemId,
                quantity: 1
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عداد السلة في الـ header
                updateCartCount(data.cart_count);

                // تغيير النص مؤقتاً
                button.innerHTML = '<i class="fas fa-check ml-1"></i>تم الإضافة!';
                button.classList.remove('bg-primary', 'hover:bg-primary/90');
                button.classList.add('bg-green-500');

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('bg-green-500');
                    button.classList.add('bg-primary', 'hover:bg-primary/90');
                    button.disabled = false;
                }, 2000);

                showNotification(data.message, 'success');
            } else {
                button.innerHTML = originalText;
                button.disabled = false;
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            button.innerHTML = originalText;
            button.disabled = false;
            showNotification('حدث خطأ أثناء إضافة العنصر للسلة', 'error');
        });
    }

    // تحديث عداد السلة
    function updateCartCount(count) {
        const cartButton = document.getElementById('cartButton');
        if (cartButton) {
            const cartBadge = cartButton.querySelector('span');
            if (cartBadge) {
                cartBadge.textContent = count;
                if (count > 0) {
                    cartBadge.style.display = 'inline';
                }
            }
        }
    }

    // إظهار الإشعارات
    function showNotification(message, type = 'info') {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

        if (type === 'success') {
            notification.classList.add('bg-green-500', 'text-white');
        } else if (type === 'error') {
            notification.classList.add('bg-red-500', 'text-white');
        } else {
            notification.classList.add('bg-blue-500', 'text-white');
        }

        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} ml-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
</script>

<!-- Rating System JavaScript -->
<script src="{{ asset('js/rating-system.js') }}"></script>

@include('customer.partials.search-notifications')

<!-- تم نقل جميع scripts الوضع المظلم إلى ملف منفصل: /js/dark-mode.js -->
