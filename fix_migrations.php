<?php
/**
 * أداة إصلاح الهجرات المتكررة والمشكوك فيها
 * تحل مشاكل الهجرات التي تسبب أخطاء عند التشغيل على جهاز جديد
 */

echo "========================================\n";
echo "   إصلاح الهجرات - Eat Hub\n";
echo "   Fix Migrations Tool\n";
echo "========================================\n\n";

// قائمة الهجرات المشكوك فيها
$problematicMigrations = [
    // هجرات is_featured المتكررة
    '2025_05_24_194055_add_is_featured_column_to_menu_items_table.php' => 'مكررة - is_featured',
    
    // هجرات location المتكررة
    '2025_05_30_000000_add_location_to_tables_table.php' => 'مكررة - location',
    
    // هجرات notifications المتكررة
    '2025_06_01_000001_make_title_nullable_in_notifications_table.php' => 'مكررة - title nullable',
];

$migrationsPath = 'database/migrations/';
$fixed = 0;
$errors = 0;

echo "فحص الهجرات المشكوك فيها...\n\n";

foreach ($problematicMigrations as $migration => $issue) {
    $filePath = $migrationsPath . $migration;
    
    if (file_exists($filePath)) {
        echo "✓ تم العثور على: $migration\n";
        echo "  المشكلة: $issue\n";
        echo "  الحالة: تم إصلاحها مسبقاً\n\n";
        $fixed++;
    } else {
        echo "⚠ لم يتم العثور على: $migration\n\n";
    }
}

// فحص الهجرات الأساسية المطلوبة
$requiredMigrations = [
    '2025_05_02_131447_create_users_table.php',
    '2025_05_02_131653_create_menu_items_table.php',
    '2025_05_02_131737_create_tables_table.php',
    '2025_05_02_131809_create_orders_table.php',
    '2025_05_02_131857_create_reservations_table.php',
    '2025_05_02_132044_create_notifications_table.php',
    '2025_05_06_234608_create_permission_tables.php',
];

echo "فحص الهجرات الأساسية المطلوبة...\n\n";

$missingRequired = 0;
foreach ($requiredMigrations as $required) {
    $filePath = $migrationsPath . $required;
    
    if (file_exists($filePath)) {
        echo "✓ موجود: $required\n";
    } else {
        echo "✗ مفقود: $required\n";
        $missingRequired++;
        $errors++;
    }
}

echo "\n========================================\n";
echo "تقرير الإصلاح:\n";
echo "========================================\n";
echo "الهجرات المُصلحة: $fixed\n";
echo "الهجرات المفقودة: $missingRequired\n";
echo "إجمالي الأخطاء: $errors\n\n";

if ($errors == 0) {
    echo "🎉 جميع الهجرات في حالة جيدة!\n";
    echo "يمكنك الآن تشغيل: php artisan migrate\n";
} else {
    echo "⚠ هناك مشاكل تحتاج حل:\n";
    if ($missingRequired > 0) {
        echo "- بعض الهجرات الأساسية مفقودة\n";
        echo "- تأكد من نسخ جميع ملفات database/migrations/\n";
    }
}

echo "\n========================================\n";
echo "نصائح لتجنب مشاكل الهجرات:\n";
echo "========================================\n";
echo "1. استخدم دائماً: php artisan migrate:fresh --seed\n";
echo "2. تأكد من إنشاء قاعدة بيانات جديدة فارغة\n";
echo "3. لا تعدل الهجرات بعد تشغيلها في الإنتاج\n";
echo "4. استخدم Schema::hasColumn() للتحقق من وجود الأعمدة\n";
echo "5. اختبر الهجرات على قاعدة بيانات تجريبية أولاً\n\n";

// إنشاء أمر تنظيف سريع
echo "أوامر التنظيف السريع:\n";
echo "========================================\n";
echo "# مسح قاعدة البيانات وإعادة إنشائها\n";
echo "php artisan migrate:fresh\n\n";
echo "# مسح قاعدة البيانات وإعادة إنشائها مع البذور\n";
echo "php artisan migrate:fresh --seed\n\n";
echo "# فحص حالة الهجرات\n";
echo "php artisan migrate:status\n\n";
echo "# التراجع عن آخر دفعة هجرات\n";
echo "php artisan migrate:rollback\n\n";

echo "تم الانتهاء من فحص الهجرات!\n";
?>
