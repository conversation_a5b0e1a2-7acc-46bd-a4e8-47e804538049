<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\User;

class AdminPermissionServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // تعريف Gate عام يعطي المديرين صلاحيات كاملة
        Gate::before(function (User $user, $ability) {
            // إذا كان المستخدم مدير، يملك جميع الصلاحيات
            if ($user->user_type === 'admin') {
                return true;
            }

            // للمستخدمين الآخرين، استمر بالتحقق العادي
            return null;
        });

        // تعريف Gates مخصصة للمديرين
        $this->defineAdminGates();
    }

    /**
     * تعريف Gates مخصصة للمديرين
     */
    private function defineAdminGates()
    {
        // Gate للتحقق من كون المستخدم مدير
        Gate::define('is-admin', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate للتحقق من كون المستخدم مدير أو لديه صلاحية محددة
        Gate::define('admin-or-permission', function (User $user, $permission) {
            return $user->user_type === 'admin' || $user->can($permission);
        });

        // Gate للوصول الكامل للبيانات (للمديرين فقط)
        Gate::define('full-data-access', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate لإدارة المستخدمين (مديرين فقط)
        Gate::define('manage-users', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate لإدارة الصلاحيات (مديرين فقط)
        Gate::define('manage-permissions', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate للوصول لجميع التقارير
        Gate::define('access-all-reports', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate للوصول لجميع الطلبات
        Gate::define('access-all-orders', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate للوصول لجميع الحجوزات
        Gate::define('access-all-reservations', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate لحذف أي بيانات
        Gate::define('delete-any-data', function (User $user) {
            return $user->user_type === 'admin';
        });

        // Gate لتعديل أي بيانات
        Gate::define('edit-any-data', function (User $user) {
            return $user->user_type === 'admin';
        });
    }
}
