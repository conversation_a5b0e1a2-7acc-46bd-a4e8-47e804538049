<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<div class="relative mb-8 overflow-hidden">
    <div class="bg-white dark:bg-gray-800 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-black text-gray-800 dark:text-white mb-3">
                    مرحباً، <span class="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent"><?php echo e(Auth::user()->first_name ?? 'المدير'); ?></span>!
                    <span class="inline-block animate-bounce">👑</span>
                </h1>
                <p class="text-gray-600 dark:text-gray-400 text-lg font-medium">
                    إليك نظرة عامة شاملة على أداء المطعم
                </p>
                <?php if(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin')): ?>
                <p class="text-sm text-amber-600 dark:text-amber-400 mt-2 flex items-center">
                    <i class="fas fa-info-circle ml-2"></i>
                    أنت موظف بصلاحيات إدارية
                </p>
                <?php endif; ?>
            </div>
            <div class="hidden md:block">
                <div class="w-32 h-32 rounded-full bg-gradient-to-br from-red-400 via-pink-500 to-purple-600 flex items-center justify-center animate-float shadow-2xl">
                    <i class="fas fa-crown text-white text-4xl"></i>
                </div>
            </div>
        </div>
        
        <!-- أزرار التنقل السريع -->
        <div class="flex flex-wrap gap-3 mt-6">
            <a href="<?php echo e(route('employee.dashboard')); ?>"
               class="px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-2xl hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-user-tie ml-2"></i>
                واجهة الموظف
            </a>
            <a href="<?php echo e(route('home')); ?>"
               class="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-2xl hover:from-orange-600 hover:to-red-600 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-home ml-2"></i>
                الموقع الرئيسي
            </a>
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
    <!-- بطاقة إحصائية - الإيرادات -->
    <div class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-blue-200/60 dark:border-blue-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 rounded-full blur-xl"></div>
        <div class="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">إجمالي الإيرادات</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e(number_format($totalRevenue ?? 0, 0)); ?></h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">
                        <?php echo e($revenuePercentChange >= 0 ? '+' : ''); ?><?php echo e(number_format(abs($revenuePercentChange ?? 0), 1)); ?>% د.ل
                    </p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-dollar-sign text-3xl text-blue-600 dark:text-blue-400"></i>
            </div>
        </div>
    </div>

    <!-- بطاقة إحصائية - الطلبات -->
    <div class="relative bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100 dark:from-green-900/30 dark:via-emerald-900/30 dark:to-teal-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-green-200/60 dark:border-green-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full blur-xl"></div>
        <div class="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-teal-400/20 to-cyan-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">إجمالي الطلبات</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($totalOrders ?? 0); ?></h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">
                        <?php echo e($ordersPercentChange >= 0 ? '+' : ''); ?><?php echo e(number_format(abs($ordersPercentChange ?? 0), 1)); ?>%
                    </p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-shopping-cart text-3xl text-green-600 dark:text-green-400"></i>
            </div>
        </div>
    </div>

    <!-- بطاقة إحصائية - العملاء -->
    <div class="relative bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-100 dark:from-orange-900/30 dark:via-amber-900/30 dark:to-yellow-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-orange-200/60 dark:border-orange-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-400 via-amber-500 to-yellow-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-amber-500/20 rounded-full blur-xl"></div>
        <div class="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">إجمالي العملاء</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e($totalCustomers ?? 0); ?></h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">
                        نشط
                    </p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-users text-3xl text-orange-600 dark:text-orange-400"></i>
            </div>
        </div>
    </div>

    <!-- بطاقة إحصائية - التقييم -->
    <div class="relative bg-gradient-to-br from-purple-50 via-pink-50 to-rose-100 dark:from-purple-900/30 dark:via-pink-900/30 dark:to-rose-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-purple-200/60 dark:border-purple-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-purple-400 via-pink-500 to-rose-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-500/20 rounded-full blur-xl"></div>
        <div class="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-br from-rose-400/20 to-purple-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">متوسط التقييم</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight"><?php echo e(number_format($averageRating ?? 4.8, 1)); ?></h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">
                        ممتاز
                    </p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-star text-3xl text-purple-600 dark:text-purple-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية والتقارير -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
    <!-- الطلبات النشطة -->
    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2 flex items-center">
                    <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3">
                        <i class="fas fa-chart-line text-white text-sm"></i>
                    </div>
                    تحليل المبيعات
                </h3>
                <p class="text-gray-600 dark:text-gray-400">أداء المبيعات خلال الأسبوع الماضي</p>
            </div>
            <div class="flex items-center space-x-3 space-x-reverse">
                <div class="relative">
                    <select class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg px-4 py-2 text-sm border-0 focus:ring-2 focus:ring-primary">
                        <option>آخر 7 أيام</option>
                        <option>آخر 30 يوم</option>
                        <option>آخر 3 أشهر</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="h-80">
            <canvas id="salesChart"></canvas>
        </div>
    </div>

    <!-- الإحصائيات السريعة -->
    <div class="space-y-6">
        <!-- أفضل الأطباق -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center mr-3">
                    <i class="fas fa-fire text-white text-sm"></i>
                </div>
                الأطباق الأكثر طلباً
            </h3>
            <div class="space-y-4">
                <?php if(isset($topMenuItems) && $topMenuItems->count() > 0): ?>
                    <?php $__currentLoopData = $topMenuItems->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm"><?php echo e($loop->iteration); ?></span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-white"><?php echo e($item->name ?? 'طبق مميز'); ?></p>
                                <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($item->orders_count ?? 0); ?> طلب</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-gray-800 dark:text-white"><?php echo e(number_format($item->price ?? 0, 2)); ?> د.ل</p>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-utensils text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                        <p class="text-gray-500 dark:text-gray-400">لا توجد بيانات متاحة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- الطلبات الحديثة -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-3">
                    <i class="fas fa-clock text-white text-sm"></i>
                </div>
                الطلبات الحديثة
            </h3>
            <div class="space-y-3">
                <?php if(isset($recentOrders) && $recentOrders->count() > 0): ?>
                    <?php $__currentLoopData = $recentOrders->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700/50 rounded-xl">
                        <div class="flex items-center">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-400 to-cyan-500 flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-xs">#<?php echo e($order->order_id ?? 0); ?></span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-800 dark:text-white text-sm"><?php echo e($order->user->first_name ?? 'عميل'); ?></p>
                                <p class="text-xs text-gray-500 dark:text-gray-400"><?php echo e($order->created_at->diffForHumans() ?? 'الآن'); ?></p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-gray-800 dark:text-white text-sm"><?php echo e(number_format($order->total_amount ?? 0, 2)); ?> د.ل</p>
                            <span class="text-xs px-2 py-1 rounded-full <?php echo e($order->status == 'completed' ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'); ?>">
                                <?php echo e($order->status ?? 'قيد المعالجة'); ?>

                            </span>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-shopping-cart text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                        <p class="text-gray-500 dark:text-gray-400">لا توجد طلبات حديثة</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني للمبيعات
const ctx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
        datasets: [{
            label: 'المبيعات (د.ل)',
            data: [<?php echo e(implode(',', array_column($salesData ?? [], 'amount'))); ?>],
            borderColor: 'rgb(99, 102, 241)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        }
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\ccss450\cs450level10\resources\views/admin/dashboard-new.blade.php ENDPATH**/ ?>