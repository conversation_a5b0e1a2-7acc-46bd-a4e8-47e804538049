<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Models\Notification;

class ContactController extends Controller
{
    /**
     * عرض صفحة الاتصال
     */
    public function index()
    {
        return view('contact');
    }

    /**
     * معالجة إرسال رسالة الاتصال
     */
    public function store(Request $request)
    {
        // التحقق من صحة البيانات
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
        ], [
            'name.required' => 'الاسم مطلوب',
            'name.max' => 'الاسم يجب أن يكون أقل من 255 حرف',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'email.max' => 'البريد الإلكتروني يجب أن يكون أقل من 255 حرف',
            'phone.max' => 'رقم الهاتف يجب أن يكون أقل من 20 رقم',
            'subject.required' => 'الموضوع مطلوب',
            'subject.max' => 'الموضوع يجب أن يكون أقل من 255 حرف',
            'message.required' => 'الرسالة مطلوبة',
            'message.max' => 'الرسالة يجب أن تكون أقل من 1000 حرف',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', 'يرجى تصحيح الأخطاء أدناه');
        }

        try {
            // إرسال إشعار لجميع الموظفين والمديرين
            $this->notifyStaff($request->all());

            return redirect()->back()->with('success', 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');

        } catch (\Exception $e) {
            // تسجيل الخطأ في السجل
            Log::error('Contact form error: ' . $e->getMessage());

            return redirect()->back()
                ->withInput()
                ->with('error', 'حدث خطأ أثناء إرسال الرسالة: ' . $e->getMessage());
        }
    }

    /**
     * إرسال إشعار لجميع الموظفين والمديرين
     */
    private function notifyStaff($contactData)
    {
        // الحصول على جميع المديرين والموظفين
        $staff = User::whereIn('user_type', ['admin', 'employee'])->get();

        $title = 'رسالة جديدة من العملاء';
        $message = "رسالة جديدة من: {$contactData['name']}\n";
        $message .= "البريد الإلكتروني: {$contactData['email']}\n";

        if (!empty($contactData['phone'])) {
            $message .= "الهاتف: {$contactData['phone']}\n";
        }

        $message .= "الموضوع: {$contactData['subject']}\n";
        $message .= "الرسالة: {$contactData['message']}";

        foreach ($staff as $user) {
            Notification::create([
                'user_id' => $user->user_id,
                'title' => $title,
                'message' => $message,
                'type' => 'contact',
                'is_read' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
