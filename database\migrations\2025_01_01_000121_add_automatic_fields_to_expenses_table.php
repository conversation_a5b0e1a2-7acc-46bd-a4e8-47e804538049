<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->boolean('is_automatic')->default(false)->after('payment_method');
            $table->unsignedBigInteger('order_id')->nullable()->after('is_automatic');
            $table->unsignedBigInteger('inventory_transaction_id')->nullable()->after('order_id');
            
            // إضافة فهارس
            $table->index('is_automatic');
            $table->index('order_id');
            $table->index('inventory_transaction_id');
        });
    }

    public function down()
    {
        Schema::table('expenses', function (Blueprint $table) {
            $table->dropIndex(['is_automatic']);
            $table->dropIndex(['order_id']);
            $table->dropIndex(['inventory_transaction_id']);
            
            $table->dropColumn(['is_automatic', 'order_id', 'inventory_transaction_id']);
        });
    }
};
