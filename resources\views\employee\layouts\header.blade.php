<!-- الهيدر -->
<header class="bg-white dark:bg-gray-800 shadow-lg z-10 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4 flex justify-between items-center">
        <!-- زر فتح/إغلاق القائمة الجانبية -->
        <button id="sidebarToggle" class="md:hidden p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
            <i class="fas fa-bars text-xl group-hover:scale-110 transition-transform"></i>
        </button>

        <!-- عنوان الصفحة - سيتم تغييره ديناميكياً -->
        <h1 id="pageTitle" class="text-2xl font-bold hidden md:block text-gray-800 dark:text-white">لوحة التحكم</h1>

        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- زر البحث -->
            <div class="relative">
                <form action="{{ route('search') }}" method="GET" class="hidden md:flex items-center">
                    <div class="relative">
                        <input type="text" name="query" placeholder="بحث..." class="w-64 px-4 py-3 pr-12 rounded-xl bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                        <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                <button id="mobileSearchBtn" class="md:hidden p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <i class="fas fa-search group-hover:scale-110 transition-transform"></i>
                </button>
            </div>

            <!-- زر سلة الطلبات -->
            <div class="relative">
                <a href="#" id="cartButton" class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group inline-block relative">
                    <i class="fas fa-shopping-cart group-hover:scale-110 transition-transform"></i>
                    <span class="cart-count absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xs w-6 h-6 flex items-center justify-center font-bold animate-pulse">0</span>
                </a>
                <!-- قائمة سلة الطلبات - ستظهر عند النقر -->
                <div id="cartMenu" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">سلة الطلبات</h3>
                        <a href="{{ route('employee.orders') }}" class="text-blue-600 dark:text-blue-400 text-sm hover:underline">إنشاء طلب</a>
                    </div>
                    <div id="cartItemsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بعناصر السلة من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>لا توجد عناصر في السلة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر الإشعارات -->
            <div class="relative">
                @php
                    $unreadCount = \App\Models\Notification::where('user_id', Auth::id())->where('is_read', false)->count();
                @endphp
                <button id="notificationBtn" class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group relative">
                    <i class="fas fa-bell group-hover:scale-110 transition-transform {{ $unreadCount > 0 ? 'animate-bounce' : '' }}"></i>
                    <span id="notificationCount" class="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xs min-w-[24px] h-6 flex items-center justify-center font-bold animate-pulse {{ $unreadCount > 0 ? '' : 'hidden' }}">{{ $unreadCount }}</span>
                </button>
                <!-- قائمة الإشعارات - ستظهر عند النقر -->
                <div id="notificationsMenu" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="markAllNotificationsAsRead()" class="text-xs text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors" title="تعليم الكل كمقروء">
                                <i class="fas fa-check-double"></i>
                            </button>
                            <a href="{{ route('employee.notifications') }}" class="text-blue-600 dark:text-blue-400 text-sm hover:underline">عرض الكل</a>
                        </div>
                    </div>
                    <div id="notificationsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بالإشعارات من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>جاري تحميل الإشعارات...</p>
                        </div>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 p-2">
                        <a href="{{ route('employee.notifications') }}" class="block w-full text-center py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors">
                            عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
            </div>

            <!-- زر تبديل الثيم -->
            <button data-theme-toggle class="theme-toggle p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group" title="تبديل الثيم">
                <i class="theme-icon fas fa-adjust group-hover:rotate-180 transition-transform duration-500"></i>
            </button>

            <!-- صورة المستخدم -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center p-2 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center text-white font-bold overflow-hidden ring-2 ring-white/20 group-hover:ring-white/40 transition-all duration-300">
                        @if(Auth::user()->profile_image)
                            <img src="{{ asset('storage/' . Auth::user()->profile_image) }}" alt="{{ Auth::user()->first_name }}" class="w-full h-full object-cover">
                        @else
                            {{ substr(Auth::user()->first_name, 0, 1) }}
                        @endif
                    </div>
                    <span class="mr-3 hidden md:block font-semibold">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</span>
                    <i class="fas fa-chevron-down text-xs mr-1 hidden md:block group-hover:rotate-180 transition-transform duration-300"></i>
                </button>

                <!-- قائمة المستخدم - مخفية افتراضياً -->
                <div id="userMenu" class="absolute left-0 mt-3 w-72 bg-white dark:bg-gray-800 rounded-2xl shadow-lg py-2 z-10 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold overflow-hidden ring-2 ring-gray-200 dark:ring-gray-600">
                                @if(Auth::user()->profile_image)
                                    <img src="{{ asset('storage/' . Auth::user()->profile_image) }}" alt="{{ Auth::user()->first_name }}" class="w-full h-full object-cover">
                                @else
                                    {{ substr(Auth::user()->first_name, 0, 1) }}
                                @endif
                            </div>
                            <div class="mr-3">
                                <p class="text-sm font-bold text-gray-800 dark:text-white">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 truncate">{{ Auth::user()->email }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="py-2">
                        <a href="{{ route('employee.profile') }}" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user-circle text-white text-sm"></i>
                            </div>
                            <span class="font-medium">الملف الشخصي</span>
                        </a>

                        @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
                        <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>
                        <div class="px-6 py-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium uppercase tracking-wider">التنقل السريع</span>
                        </div>
                        <a href="{{ route('admin.dashboard') }}" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user-shield text-white text-sm"></i>
                            </div>
                            <div>
                                <span class="font-medium block">واجهة المدير</span>
                                <span class="text-xs text-gray-500 dark:text-gray-400">العودة لوحة الإدارة</span>
                            </div>
                        </a>
                        @endif

                        <a href="{{ route('employee.settings') }}" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-cog text-white text-sm"></i>
                            </div>
                            <span class="font-medium">الإعدادات</span>
                        </a>

                        <a href="{{ route('employee.help') }}" class="flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-yellow-500 to-orange-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-question-circle text-white text-sm"></i>
                            </div>
                            <span class="font-medium">المساعدة</span>
                        </a>
                    </div>

                    <div class="border-t border-gray-200 dark:border-gray-600 pt-2">
                        <form action="{{ route('logout') }}" method="POST" class="w-full">
                            @csrf
                            <button type="submit" class="w-full flex items-center px-6 py-3 text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300 group">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                    <i class="fas fa-sign-out-alt text-white text-sm"></i>
                                </div>
                                <span class="font-medium">تسجيل الخروج</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>