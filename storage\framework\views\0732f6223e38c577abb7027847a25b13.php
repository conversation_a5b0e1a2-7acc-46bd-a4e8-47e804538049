<!-- قسم الحجز وآخر الأخبار -->
<div class="py-16 bg-white dark:bg-gray-800">
    <div class="container mx-auto px-4">
        <div class="bg-gradient-to-r from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20 rounded-2xl overflow-hidden shadow-lg">
            <div class="flex flex-col md:flex-row">
                <div class="md:w-1/2 p-8 md:p-12 flex flex-col justify-center">
                    <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">احجز طاولتك الآن</h2>
                    <p class="text-gray-600 dark:text-gray-300 mb-6">احجز طاولتك مسبقاً لضمان الحصول على أفضل الأماكن. استمتع بتجربة تناول طعام فريدة في أجواء مريحة.</p>
                    <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('customer.reservations.create')); ?>" class="btn-hover-effect bg-primary hover:bg-primary/90 text-white font-bold py-3 px-8 rounded-lg transition shadow-md w-full md:w-auto inline-block text-center">
                        <i class="fas fa-calendar-check ml-2"></i>
                        <span>احجز الآن</span>
                    </a>
                    <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>?redirect=reservations" class="btn-hover-effect bg-primary hover:bg-primary/90 text-white font-bold py-3 px-8 rounded-lg transition shadow-md w-full md:w-auto inline-block text-center">
                        <i class="fas fa-calendar-check ml-2"></i>
                        <span>احجز الآن</span>
                    </a>
                    <?php endif; ?>
                </div>
                <div class="md:w-1/2 relative">
                    <img src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80" alt="مطعمنا" class="w-full h-full object-cover">
                    <div class="absolute inset-0 bg-gradient-to-l from-black/50 to-transparent md:hidden"></div>
                </div>
            </div>
        </div>

        <div class="mt-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">آخر الأخبار والعروض</h2>
                <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">تعرف على آخر أخبارنا وعروضنا المميزة</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php $__empty_1 = true; $__currentLoopData = $specialOffers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $offer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <div class="card-hover bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md">
                    <div class="relative">
                        <img src="<?php echo e($offer->image_url ?? 'https://images.unsplash.com/photo-1544148103-0773bf10d330?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80'); ?>" alt="<?php echo e($offer->title); ?>" class="w-full h-48 object-cover">
                        <div class="absolute top-0 right-0 bg-accent text-darkText font-bold py-1 px-3 rounded-bl-lg">عرض خاص</div>
                    </div>
                    <div class="p-5">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white mb-2"><?php echo e($offer->title); ?></h3>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4"><?php echo e($offer->description); ?></p>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-gray-500 dark:text-gray-400 text-sm"><?php echo e(\Carbon\Carbon::parse($offer->start_date)->format('d M Y')); ?></span>
                            <button onclick="showOfferDetails('<?php echo e($offer->id); ?>')" class="text-primary hover:text-primary/80 transition">اقرأ المزيد</button>
                        </div>
                        <div class="flex gap-2">
                            <?php if($offer->id == 1): ?>
                                <!-- عرض الخصم - توجيه لصفحة العرض -->
                                <a href="<?php echo e(route('customer.offers.show', 'family-discount')); ?>" class="flex-1 bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-lg transition text-sm">
                                    عرض التفاصيل
                                </a>
                            <?php elseif($offer->id == 2): ?>
                                <!-- أطباق جديدة - توجيه لصفحة العرض -->
                                <a href="<?php echo e(route('customer.offers.show', 'new-dishes')); ?>" class="flex-1 bg-accent hover:bg-accent/90 text-darkText text-center py-2 px-4 rounded-lg transition text-sm">
                                    عرض التفاصيل
                                </a>
                            <?php else: ?>
                                <!-- ليالي الموسيقى - توجيه لصفحة العرض -->
                                <a href="<?php echo e(route('customer.offers.show', 'music-nights')); ?>" class="flex-1 bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-lg transition text-sm">
                                    عرض التفاصيل
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <!-- عروض افتراضية في حالة عدم وجود عروض -->
                <?php echo $__env->make('customer.components.default-offers', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Modal لعرض تفاصيل العرض -->
<div id="offerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full mx-4 p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 id="offerTitle" class="text-xl font-bold text-gray-800 dark:text-white"></h3>
            <button onclick="closeOfferModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="offerContent" class="text-gray-600 dark:text-gray-300">
            <!-- محتوى العرض سيتم تحميله هنا -->
        </div>
        <div class="mt-6 flex justify-end">
            <button onclick="closeOfferModal()" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg transition">
                إغلاق
            </button>
        </div>
    </div>
</div>

<script>
function showOfferDetails(offerId) {
    // بيانات العروض الافتراضية
    const offers = {
        '1': {
            title: 'خصم 20% على طلبات الوجبات العائلية',
            content: 'استمتع بخصم 20% على جميع الوجبات العائلية خلال عطلة نهاية الأسبوع. العرض ساري من الخميس إلى السبت. احجز طاولتك الآن! يشمل العرض جميع الوجبات العائلية والمشاركة.'
        },
        '2': {
            title: 'أطباق جديدة في قائمتنا',
            content: 'تعرف على أطباقنا الجديدة المستوحاة من المطبخ الآسيوي بلمسة عربية مميزة. نقدم لكم تشكيلة واسعة من الأطباق الشهية التي تجمع بين النكهات الأصيلة والحديثة. جرب النكهات الجديدة اليوم!'
        },
        '3': {
            title: 'ليالي الموسيقى الحية',
            content: 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات. استمتع بأجواء رائعة مع أفضل الفرق الموسيقية المحلية. احجز طاولتك مسبقاً لضمان أفضل الأماكن.'
        }
    };

    const offer = offers[offerId] || offers['1'];
    document.getElementById('offerTitle').textContent = offer.title;
    document.getElementById('offerContent').textContent = offer.content;
    document.getElementById('offerModal').classList.remove('hidden');
}

function closeOfferModal() {
    document.getElementById('offerModal').classList.add('hidden');
}

// إغلاق المودال عند النقر خارجه
document.getElementById('offerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeOfferModal();
    }
});
</script>
<?php /**PATH D:\ccss450\cs450level10\resources\views/customer/sections/reservation-offers.blade.php ENDPATH**/ ?>