/* تحسينات التخطيط العامة */
.compact-layout {
    padding: 0.5rem !important;
    margin: 0.25rem !important;
}

.compact-spacing {
    gap: 0.75rem !important;
}

.compact-card {
    padding: 0.75rem !important;
}

.compact-text {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
}

/* تحسينات الشريط الجانبي */
.sidebar-compact {
    padding: 0.5rem !important;
}

.sidebar-compact .nav-link {
    padding: 0.5rem 0.75rem !important;
    margin-bottom: 0.25rem !important;
}

/* تحسينات البطاقات */
.card-compact {
    padding: 0.75rem !important;
    margin-bottom: 0.75rem !important;
}

.card-compact h3 {
    font-size: 1rem !important;
    margin-bottom: 0.5rem !important;
}

.card-compact h4 {
    font-size: 0.875rem !important;
    margin-bottom: 0.25rem !important;
}

/* تحسينات الجداول */
.table-compact {
    font-size: 0.875rem !important;
}

.table-compact th,
.table-compact td {
    padding: 0.5rem !important;
}

/* تحسينات النماذج */
.form-compact .form-group {
    margin-bottom: 0.75rem !important;
}

.form-compact label {
    font-size: 0.875rem !important;
    margin-bottom: 0.25rem !important;
}

.form-compact input,
.form-compact select,
.form-compact textarea {
    padding: 0.5rem !important;
    font-size: 0.875rem !important;
}

/* تحسينات الأزرار */
.btn-compact {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* تحسينات المساحات */
.space-y-compact > * + * {
    margin-top: 0.5rem !important;
}

.space-x-compact > * + * {
    margin-right: 0.5rem !important;
}

/* تحسينات الرسوم البيانية */
.chart-compact {
    height: 200px !important;
}

/* تحسينات الإشعارات */
.notification-compact {
    padding: 0.5rem !important;
    margin-bottom: 0.5rem !important;
}

.notification-compact .notification-icon {
    width: 2rem !important;
    height: 2rem !important;
}

/* تحسينات القوائم */
.list-compact li {
    padding: 0.5rem !important;
    margin-bottom: 0.25rem !important;
}

/* تحسينات الشبكة */
.grid-compact {
    gap: 0.75rem !important;
}

/* تحسينات النصوص */
.text-compact {
    font-size: 0.875rem !important;
    line-height: 1.25rem !important;
}

.text-compact-sm {
    font-size: 0.75rem !important;
    line-height: 1rem !important;
}

/* تحسينات الحاويات */
.container-compact {
    max-width: 100% !important;
    padding: 0.5rem !important;
}

/* تحسينات الهوامش */
.m-compact {
    margin: 0.5rem !important;
}

.p-compact {
    padding: 0.5rem !important;
}

.mb-compact {
    margin-bottom: 0.5rem !important;
}

.mt-compact {
    margin-top: 0.5rem !important;
}

.pb-compact {
    padding-bottom: 0.5rem !important;
}

.pt-compact {
    padding-top: 0.5rem !important;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .mobile-compact {
        padding: 0.25rem !important;
        margin: 0.25rem !important;
    }

    .mobile-compact-text {
        font-size: 0.75rem !important;
    }
}

/* تحسينات الظلال */
.shadow-compact {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
}

/* تحسينات الحدود */
.border-compact {
    border-width: 1px !important;
}

.rounded-compact {
    border-radius: 0.375rem !important;
}

/* تحسينات الألوان */
.bg-compact {
    background-color: rgba(255, 255, 255, 0.95) !important;
}

.bg-compact-dark {
    background-color: rgba(31, 41, 55, 0.95) !important;
}

/* تحسينات التمرير */
.overflow-compact {
    overflow: hidden !important;
}

.overflow-y-compact {
    overflow-y: auto !important;
    max-height: 300px !important;
}

/* تحسينات الفليكس */
.flex-compact {
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

/* تحسينات الانتقالات */
.transition-compact {
    transition: all 0.2s ease-in-out !important;
}

/* تحسينات الهوفر */
.hover-compact:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* تحسينات الأيقونات */
.icon-compact {
    width: 1rem !important;
    height: 1rem !important;
}

.icon-compact-lg {
    width: 1.25rem !important;
    height: 1.25rem !important;
}

/* تحسينات الشارات */
.badge-compact {
    padding: 0.125rem 0.375rem !important;
    font-size: 0.75rem !important;
    border-radius: 9999px !important;
}

/* تحسينات القوائم المنسدلة */
.dropdown-compact {
    padding: 0.25rem !important;
}

.dropdown-compact .dropdown-item {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* تحسينات التبويبات */
.tab-compact {
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
}

/* تحسينات الأكورديون */
.accordion-compact .accordion-header {
    padding: 0.5rem !important;
}

.accordion-compact .accordion-body {
    padding: 0.75rem !important;
}

/* تحسينات المودال */
.modal-compact .modal-header {
    padding: 0.75rem !important;
}

.modal-compact .modal-body {
    padding: 0.75rem !important;
}

.modal-compact .modal-footer {
    padding: 0.5rem 0.75rem !important;
}

/* تحسينات التنبيهات */
.alert-compact {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* تحسينات شريط التقدم */
.progress-compact {
    height: 0.5rem !important;
}

/* تحسينات التسميات */
.label-compact {
    font-size: 0.75rem !important;
    padding: 0.125rem 0.25rem !important;
}

/* تحسينات الفواصل */
.divider-compact {
    margin: 0.5rem 0 !important;
    border-width: 1px !important;
}

/* تحسينات الكروت */
.card-header-compact {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

.card-body-compact {
    padding: 0.75rem !important;
}

.card-footer-compact {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
}

/* تحسينات الشريط الجانبي */
.sidebar-transition {
    transition: transform 0.3s ease-in-out;
}
