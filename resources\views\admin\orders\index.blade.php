@extends('layouts.admin')

@section('title', 'إدارة الطلبات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة الطلبات</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <form action="{{ route('admin.orders') }}" method="GET" class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <div class="relative">
                <input type="text" name="search" value="{{ request('search') }}" placeholder="بحث برقم الطلب أو اسم العميل..." class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                <button type="submit" class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search"></i>
                </button>
            </div>
            @if(request('status'))
                <input type="hidden" name="status" value="{{ request('status') }}">
            @endif
        </form>
        <a href="{{ route('admin.orders.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة طلب جديد</span>
        </a>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">قائمة الطلبات</h3>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.orders.index', ['status' => 'pending']) }}" class="text-yellow-500 hover:text-yellow-600 transition-all">
                <i class="fas fa-clock ml-1"></i>
                <span>قيد الانتظار</span>
            </a>
            <a href="{{ route('admin.orders.index', ['status' => 'preparing']) }}" class="text-blue-500 hover:text-blue-600 transition-all">
                <i class="fas fa-spinner ml-1"></i>
                <span>قيد التحضير</span>
            </a>
            <a href="{{ route('admin.orders', ['status' => 'completed']) }}" class="text-green-500 hover:text-green-600 transition-all">
                <i class="fas fa-check-circle ml-1"></i>
                <span>مكتملة</span>
            </a>
            <a href="{{ route('admin.orders', ['status' => 'canceled']) }}" class="text-red-500 hover:text-red-600 transition-all">
                <i class="fas fa-times-circle ml-1"></i>
                <span>ملغية</span>
            </a>
        </div>
    </div>

    @if($orders->isEmpty())
        <div class="p-6 text-center">
            <p class="text-gray-500 dark:text-gray-400">لا توجد طلبات مسجلة في النظام.</p>
            <a href="{{ route('admin.orders.create') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة طلب جديد</span>
            </a>
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الطلب</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الطاولة</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($orders as $order)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    #{{ $order->order_id }}
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500 dark:text-gray-300">
                                    @if($order->user)
                                        {{ $order->user->first_name }} {{ $order->user->last_name }}
                                    @else
                                        {{ $order->customer_name ?? 'زبون غير مسجل' }}
                                        <span class="text-xs text-gray-400 block">(غير مسجل)</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500 dark:text-gray-300">
                                    {{ $order->table ? 'طاولة ' . $order->table->table_number : 'طلب خارجي' }}
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500 dark:text-gray-300">
                                    {{ number_format($order->total_amount, 2) }} د.ل
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    @if($order->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100
                                    @elseif($order->status == 'preparing') bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100
                                    @elseif($order->status == 'completed') bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100
                                    @elseif($order->status == 'canceled') bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100
                                    @endif">
                                    @if($order->status == 'pending') قيد الانتظار
                                    @elseif($order->status == 'preparing') قيد التحضير
                                    @elseif($order->status == 'completed') مكتمل
                                    @elseif($order->status == 'canceled') ملغي
                                    @endif
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500 dark:text-gray-300">
                                    {{ $order->created_at->format('Y-m-d H:i') }}
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <a href="{{ route('admin.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 transition-all">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.orders.edit', $order->order_id) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
            {{ $orders->links() }}
        </div>
    @endif
</div>
@endsection
