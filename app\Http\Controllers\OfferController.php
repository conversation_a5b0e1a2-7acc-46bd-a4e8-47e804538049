<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class OfferController extends Controller
{
    public function index()
    {
        // بيانات العروض التجريبية
        $offers = collect([
            [
                'slug' => 'family-discount',
                'title' => 'خصم %25 على طلبات الوجبات العائلية',
                'description' => 'استمتع بخصم 25% على جميع الوجبات العائلية عند طلب وجبة لأكثر من 4 أشخاص',
                'image_url' => 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1',
                'discount' => 25,
                'type' => 'حجز',
                'is_active' => true,
                'start_date' => now()->subDays(5),
                'end_date' => now()->addDays(30),
                'terms' => [
                    'ساري على الوجبات العائلية فقط',
                    'الحد الأدنى 4 أشخاص',
                    'لا يشمل المشروبات',
                    'غير قابل للجمع مع عروض أخرى'
                ]
            ],
            [
                'slug' => 'new-dishes',
                'title' => 'تذوق أطباقنا الجديدة',
                'description' => 'اكتشف مجموعة من الأطباق الجديدة المضافة حديثاً إلى قائمتنا مع خصم خاص',
                'image_url' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b',
                'discount' => 15,
                'type' => 'طعام',
                'is_active' => true,
                'start_date' => now()->subDays(3),
                'end_date' => now()->addDays(15),
                'terms' => [
                    'ساري على الأطباق الجديدة فقط',
                    'خصم 15% على الطبق الواحد',
                    'متاح للطلب والتوصيل'
                ]
            ],
            [
                'slug' => 'music-nights',
                'title' => 'ليالي الموسيقى الحية',
                'description' => 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات',
                'image_url' => 'https://images.unsplash.com/photo-1519690889869-e705e59f72e1',
                'discount' => null,
                'type' => 'حجز',
                'is_active' => true,
                'start_date' => now(),
                'end_date' => null,
                'terms' => [
                    'كل يوم خميس من الساعة 7 مساءً',
                    'يتطلب حجز مسبق',
                    'رسوم دخول 10 د.ل للشخص الواحد'
                ]
            ]
        ]);

        return view('customer.offers.index', compact('offers'));
    }

    public function show($slug)
    {
        // البحث عن العرض بالـ slug
        $offers = collect([
            [
                'slug' => 'family-discount',
                'title' => 'خصم %25 على طلبات الوجبات العائلية',
                'description' => 'استمتع بخصم 25% على جميع الوجبات العائلية عند طلب وجبة لأكثر من 4 أشخاص. هذا العرض مثالي للعائلات الكبيرة والتجمعات الودية.',
                'image_url' => 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1',
                'discount' => 25,
                'type' => 'حجز',
                'is_active' => true,
                'start_date' => now()->subDays(5),
                'end_date' => now()->addDays(30),
                'terms' => [
                    'ساري على الوجبات العائلية فقط',
                    'الحد الأدنى 4 أشخاص',
                    'لا يشمل المشروبات',
                    'غير قابل للجمع مع عروض أخرى',
                    'يتطلب حجز مسبق'
                ],
                'details' => 'عرض خاص للعائلات يتيح لك الاستمتاع بأشهى الوجبات مع أحبائك بسعر مخفض. يشمل العرض جميع الأطباق الرئيسية في قائمة الوجبات العائلية.'
            ],
            [
                'slug' => 'new-dishes',
                'title' => 'تذوق أطباقنا الجديدة',
                'description' => 'اكتشف مجموعة من الأطباق الجديدة المضافة حديثاً إلى قائمتنا مع خصم خاص للتجربة الأولى.',
                'image_url' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b',
                'discount' => 15,
                'type' => 'طعام',
                'is_active' => true,
                'start_date' => now()->subDays(3),
                'end_date' => now()->addDays(15),
                'terms' => [
                    'ساري على الأطباق الجديدة فقط',
                    'خصم 15% على الطبق الواحد',
                    'متاح للطلب والتوصيل',
                    'لا يشمل الحلويات'
                ],
                'details' => 'تشكيلة متنوعة من الأطباق الجديدة التي أضفناها مؤخراً، مستوحاة من المطابخ العالمية ومحضرة بأجود المكونات.'
            ],
            [
                'slug' => 'music-nights',
                'title' => 'ليالي الموسيقى الحية',
                'description' => 'انضم إلينا كل خميس لليالي الموسيقى الحية مع تشكيلة من أشهى المأكولات في أجواء رائعة.',
                'image_url' => 'https://images.unsplash.com/photo-1519690889869-e705e59f72e1',
                'discount' => null,
                'type' => 'حجز',
                'is_active' => true,
                'start_date' => now(),
                'end_date' => null,
                'terms' => [
                    'كل يوم خميس من الساعة 7 مساءً',
                    'يتطلب حجز مسبق',
                    'رسوم دخول 10 د.ل للشخص الواحد',
                    'الحجز متاح حتى الساعة 6 مساءً'
                ],
                'details' => 'أمسية موسيقية رائعة مع فرقة موسيقية محلية تقدم أجمل الألحان العربية والعالمية، مع قائمة طعام خاصة للمناسبة.'
            ]
        ]);

        $offer = $offers->firstWhere('slug', $slug);

        if (!$offer) {
            abort(404, 'العرض غير موجود');
        }

        // تحويل إلى array للتوافق مع العرض
        $offer = collect($offer)->toArray();

        // العروض المشابهة (باستثناء العرض الحالي)
        $relatedOffers = $offers->where('slug', '!=', $slug)->take(3);

        return view('customer.offers.show', compact('offer', 'relatedOffers'));
    }
}
