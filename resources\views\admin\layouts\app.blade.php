<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Eat Hub - نظام إدارة المطعم للمديرين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',      // بنفسجي عصري
                        secondary: '#10b981',    // أخضر زمردي
                        accent: '#f59e0b',       // برتقالي ذهبي
                        warmBrown: '#C8A080',    // بني دافئ للراحة
                        darkText: '#1f2937',     // رمادي داكن للنصوص
                        glass: 'rgba(255, 255, 255, 0.1)',
                        'gradient-start': '#667eea',
                        'gradient-end': '#764ba2',
                        'neon-blue': '#00d4ff',
                        'neon-purple': '#b794f6',
                        'cyber-green': '#00ff88',
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in': 'fadeIn 0.6s ease-out',
                        'bounce-slow': 'bounce 3s infinite',
                        'pulse-slow': 'pulse 3s infinite',
                        'spin-slow': 'spin 8s linear infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        glow: {
                            '0%': { boxShadow: '0 0 5px rgba(99, 102, 241, 0.5)' },
                            '100%': { boxShadow: '0 0 20px rgba(99, 102, 241, 0.8)' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(100px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                    },
                    backdropBlur: {
                        xs: '2px',
                    },
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            font-family: 'Cairo', sans-serif;
        }

        /* تأثيرات الزجاج */
        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .glass-dark {
            background: rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* تأثيرات النيون */
        .neon-glow {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
        }

        .neon-text {
            text-shadow: 0 0 10px rgba(99, 102, 241, 0.8);
        }

        /* تدرجات ملونة */
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* تأثيرات الحركة */
        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        /* تأثيرات الانتقال */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        /* تخصيص شريط التمرير */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }

        /* تأثيرات الظلال */
        .shadow-glass {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .shadow-neon {
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
        }

        /* تأثيرات الخلفية */
        .bg-pattern {
            background-image: 
                radial-gradient(circle at 25px 25px, rgba(99, 102, 241, 0.1) 2px, transparent 0),
                radial-gradient(circle at 75px 75px, rgba(16, 185, 129, 0.1) 2px, transparent 0);
            background-size: 100px 100px;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .glass {
                backdrop-filter: blur(5px);
            }
        }

        /* تأثيرات التحميل */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* تحسينات الطباعة */
        @media print {
            .no-print { display: none !important; }
            .glass { background: white !important; backdrop-filter: none !important; }
        }
    </style>
    @yield('styles')
</head>
<body class="bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 min-h-screen bg-pattern">
    <div class="flex h-screen overflow-hidden">
        <!-- القائمة الجانبية -->
        @include('admin.layouts.sidebar')

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col md:mr-80 min-h-screen">
            <!-- الهيدر -->
            @include('admin.layouts.header')

            <!-- المحتوى -->
            <main class="flex-1 p-6 overflow-y-auto">
                <div class="max-w-7xl mx-auto">
                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- سكريبت الثيم المظلم -->
    <script>
        // تطبيق الثيم المحفوظ
        if (localStorage.getItem('darkMode') === 'enabled' || 
            (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
        }

        // تبديل الثيم
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.querySelector('[data-theme-toggle]');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    document.documentElement.classList.toggle('dark');
                    localStorage.setItem('darkMode', 
                        document.documentElement.classList.contains('dark') ? 'enabled' : 'disabled'
                    );
                });
            }
        });
    </script>

    @yield('scripts')
</body>
</html>
