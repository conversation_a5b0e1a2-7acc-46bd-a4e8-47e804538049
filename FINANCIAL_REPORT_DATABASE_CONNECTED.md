# 🎯 تم ربط التقرير المالي بقاعدة البيانات بنجاح!

## 🔄 التحديثات المطبقة:

### 1. 🛠️ تحديث ReportController:

#### إضافة الـ imports المطلوبة:
```php
use App\Models\Payment;
use App\Models\MenuItem;
use App\Models\User;
use App\Models\OrderItem;
use Carbon\Carbon;
```

#### تطوير الـ financial method:
```php
public function financial(Request $request)
{
    // تحديد الفترة الزمنية (افتراضياً الشهر الحالي)
    $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
    $endDate = $request->get('end_date', Carbon::now()->endOfMonth());
    
    // تحويل التواريخ إلى Carbon instances
    $startDate = Carbon::parse($startDate);
    $endDate = Carbon::parse($endDate);

    // حساب إجمالي المبيعات من جدول Payments
    $totalSales = Payment::whereHas('order', function($query) use ($startDate, $endDate) {
        $query->whereBetween('created_at', [$startDate, $endDate])
              ->where('status', '!=', 'cancelled');
    })->sum('amount');

    // حساب إجمالي المصروفات من جدول Expenses
    $totalExpenses = Expense::whereBetween('expense_date', [$startDate, $endDate])
        ->sum('amount');

    // حساب صافي الربح
    $netProfit = $totalSales - $totalExpenses;

    // حساب متوسط قيمة الطلب
    $ordersCount = Order::whereBetween('created_at', [$startDate, $endDate])
        ->where('status', '!=', 'cancelled')
        ->count();
    $averageOrderValue = $ordersCount > 0 ? $totalSales / $ordersCount : 0;

    // بيانات المبيعات والمصروفات لآخر 12 شهر
    $monthlyData = [];
    for ($i = 11; $i >= 0; $i--) {
        $monthStart = Carbon::now()->subMonths($i)->startOfMonth();
        $monthEnd = Carbon::now()->subMonths($i)->endOfMonth();
        
        $monthlySales = Payment::whereHas('order', function($query) use ($monthStart, $monthEnd) {
            $query->whereBetween('created_at', [$monthStart, $monthEnd])
                  ->where('status', '!=', 'cancelled');
        })->sum('amount');
        
        $monthlyExpenses = Expense::whereBetween('expense_date', [$monthStart, $monthEnd])
            ->sum('amount');
        
        $monthlyData[] = [
            'month' => $monthStart->format('M'),
            'sales' => $monthlySales,
            'expenses' => $monthlyExpenses,
            'profit' => $monthlySales - $monthlyExpenses
        ];
    }

    // توزيع المصروفات حسب الفئة
    $expensesByCategory = Expense::whereBetween('expense_date', [$startDate, $endDate])
        ->select('category', DB::raw('SUM(amount) as total'))
        ->groupBy('category')
        ->get();

    // أفضل المنتجات مبيعاً
    $topProducts = OrderItem::whereHas('order', function($query) use ($startDate, $endDate) {
        $query->whereBetween('created_at', [$startDate, $endDate])
              ->where('status', '!=', 'cancelled');
    })
    ->select('menu_item_id', DB::raw('SUM(quantity) as total_quantity'), DB::raw('SUM(price * quantity) as total_revenue'))
    ->with('menuItem')
    ->groupBy('menu_item_id')
    ->orderBy('total_revenue', 'desc')
    ->limit(10)
    ->get();

    // أداء الموظفين (الطلبات المدخلة)
    $employeePerformance = Order::whereBetween('created_at', [$startDate, $endDate])
        ->select('created_by', DB::raw('COUNT(*) as orders_count'), DB::raw('SUM(total_amount) as total_sales'))
        ->with('creator')
        ->groupBy('created_by')
        ->orderBy('total_sales', 'desc')
        ->limit(10)
        ->get();

    // إحصائيات إضافية
    $stats = [
        'total_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')->count(),
        'completed_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'completed')->count(),
        'cancelled_orders' => Order::whereBetween('created_at', [$startDate, $endDate])
            ->where('status', 'cancelled')->count(),
        'active_customers' => Order::whereBetween('created_at', [$startDate, $endDate])
            ->distinct('customer_id')->count('customer_id'),
    ];

    return view('admin.reports.financial', compact(
        'totalSales', 'totalExpenses', 'netProfit', 'averageOrderValue',
        'monthlyData', 'expensesByCategory', 'topProducts', 'employeePerformance',
        'stats', 'startDate', 'endDate'
    ));
}
```

### 2. 🎨 تحديث صفحة التقرير المالي:

#### إضافة فلتر التاريخ:
```blade
<!-- فلتر التاريخ -->
<form method="GET" class="flex items-center space-x-4 space-x-reverse">
    <div class="flex items-center space-x-2 space-x-reverse">
        <label class="text-sm text-gray-600 dark:text-gray-400">من:</label>
        <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
               class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white">
    </div>
    <div class="flex items-center space-x-2 space-x-reverse">
        <label class="text-sm text-gray-600 dark:text-gray-400">إلى:</label>
        <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
               class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white">
    </div>
    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary/90">
        تطبيق
    </button>
</form>
```

#### تحديث البطاقات الإحصائية:
```blade
<!-- إجمالي المبيعات -->
<h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($totalSales, 2) }} د.ل</h3>
<span>{{ $stats['completed_orders'] }} طلب مكتمل</span>

<!-- إجمالي المصروفات -->
<h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($totalExpenses, 2) }} د.ل</h3>
<span>{{ $expensesByCategory->count() }} فئة مصروفات</span>

<!-- صافي الربح -->
<h3 class="text-2xl font-bold {{ $netProfit >= 0 ? 'text-green-600' : 'text-red-600' }} dark:text-white">
    {{ number_format($netProfit, 2) }} د.ل
</h3>
<span>{{ $netProfit >= 0 ? 'ربح' : 'خسارة' }}</span>

<!-- متوسط قيمة الطلب -->
<h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($averageOrderValue, 2) }} د.ل</h3>
<span>{{ $stats['total_orders'] }} طلب إجمالي</span>
```

#### تحديث جدول أفضل المنتجات:
```blade
@forelse($topProducts as $product)
<tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
    <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">
        {{ $product->menuItem->name ?? 'منتج محذوف' }}
    </td>
    <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
        {{ number_format($product->total_quantity) }}
    </td>
    <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
        {{ number_format($product->total_revenue, 2) }} د.ل
    </td>
    <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
        {{ number_format($product->total_revenue / $product->total_quantity, 2) }} د.ل
    </td>
</tr>
@empty
<tr>
    <td colspan="4" class="py-6 text-center text-gray-500 dark:text-gray-400">
        لا توجد بيانات مبيعات في هذه الفترة
    </td>
</tr>
@endforelse
```

#### تحديث جدول أداء الموظفين:
```blade
@forelse($employeePerformance as $employee)
<tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
    <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">
        {{ $employee->creator->first_name ?? 'غير محدد' }} {{ $employee->creator->last_name ?? '' }}
    </td>
    <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
        {{ number_format($employee->orders_count) }}
    </td>
    <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
        {{ number_format($employee->total_sales, 2) }} د.ل
    </td>
    <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
        {{ number_format($employee->total_sales / $employee->orders_count, 2) }} د.ل
    </td>
</tr>
@empty
<tr>
    <td colspan="4" class="py-6 text-center text-gray-500 dark:text-gray-400">
        لا توجد بيانات أداء موظفين في هذه الفترة
    </td>
</tr>
@endforelse
```

#### تحديث المخططات البيانية:
```javascript
// بيانات المخطط من الخادم
const monthlyData = @json($monthlyData);

const salesExpensesOptions = {
    series: [{
        name: 'المبيعات',
        data: monthlyData.map(item => item.sales)
    }, {
        name: 'المصروفات',
        data: monthlyData.map(item => item.expenses)
    }],
    // ...
    xaxis: {
        categories: monthlyData.map(item => item.month),
    }
};

// مخطط توزيع المصروفات
const expensesData = @json($expensesByCategory);

const expensesDistOptions = {
    series: expensesData.map(item => item.total),
    labels: expensesData.map(item => item.category || 'غير محدد'),
};
```

## 📊 البيانات المربوطة الآن:

### 1. البطاقات الإحصائية:
- ✅ **إجمالي المبيعات**: من جدول `payments` مع ربط `orders`
- ✅ **إجمالي المصروفات**: من جدول `expenses`
- ✅ **صافي الربح**: حساب تلقائي (المبيعات - المصروفات)
- ✅ **متوسط قيمة الطلب**: حساب تلقائي (إجمالي المبيعات ÷ عدد الطلبات)

### 2. المخططات البيانية:
- ✅ **مخطط المبيعات مقابل المصروفات**: بيانات آخر 12 شهر
- ✅ **مخطط توزيع المصروفات**: حسب الفئات من قاعدة البيانات

### 3. الجداول التفصيلية:
- ✅ **أفضل المنتجات مبيعاً**: من جدول `order_items` مع ربط `menu_items`
- ✅ **أداء الموظفين**: من جدول `orders` مع ربط `users`

### 4. الفلاتر والتحكم:
- ✅ **فلتر التاريخ**: يمكن تحديد فترة زمنية مخصصة
- ✅ **البيانات التفاعلية**: تتحدث حسب الفترة المحددة

## 🎯 المميزات الجديدة:

### 1. البيانات الحقيقية:
- **لا توجد بيانات ثابتة** - كل شيء من قاعدة البيانات
- **تحديث فوري** عند إضافة طلبات أو مصروفات جديدة
- **دقة عالية** في الحسابات والإحصائيات

### 2. الفلترة المتقدمة:
- **فلتر بالتاريخ**: من تاريخ إلى تاريخ
- **افتراضي ذكي**: الشهر الحالي كفترة افتراضية
- **مرونة في الاستعلام**: يمكن عرض أي فترة زمنية

### 3. التفاعل والاستجابة:
- **مخططات تفاعلية**: تتحدث حسب البيانات الحقيقية
- **جداول ديناميكية**: تظهر "لا توجد بيانات" عند عدم وجود معلومات
- **ألوان ذكية**: الربح أخضر والخسارة حمراء

### 4. الأداء المحسن:
- **استعلامات محسنة**: استخدام `whereHas` و `with` للعلاقات
- **تجميع البيانات**: `groupBy` و `sum` لتحسين الأداء
- **حد أقصى للنتائج**: `limit(10)` للجداول

## 🔍 كيفية اختبار التقرير:

### 1. إضافة بيانات تجريبية:
```sql
-- إضافة طلبات تجريبية
INSERT INTO orders (customer_name, total_amount, status, created_at) VALUES 
('أحمد محمد', 150.00, 'completed', NOW()),
('فاطمة علي', 200.50, 'completed', NOW()),
('محمد سعد', 175.25, 'completed', NOW());

-- إضافة مدفوعات
INSERT INTO payments (order_id, amount, payment_method, status) VALUES 
(1, 150.00, 'cash', 'completed'),
(2, 200.50, 'card', 'completed'),
(3, 175.25, 'cash', 'completed');

-- إضافة مصروفات
INSERT INTO expenses (description, amount, category, expense_date) VALUES 
('شراء مكونات', 500.00, 'مكونات', NOW()),
('فاتورة كهرباء', 200.00, 'مرافق', NOW()),
('راتب موظف', 1500.00, 'رواتب', NOW());
```

### 2. فحص النتائج:
- **اذهب إلى**: `http://localhost:8000/admin/reports/financial`
- **تحقق من البطاقات**: يجب أن تظهر الأرقام الحقيقية
- **اختبر الفلتر**: غير التاريخ وتحقق من تحديث البيانات
- **تحقق من الجداول**: يجب أن تظهر المنتجات والموظفين الحقيقيين

### 3. اختبار المخططات:
- **مخطط المبيعات**: يجب أن يظهر بيانات آخر 12 شهر
- **مخطط المصروفات**: يجب أن يظهر توزيع الفئات الحقيقية

## 🚀 النتيجة النهائية:

**✅ التقرير المالي الآن مربوط بالكامل بقاعدة البيانات!**

- 📊 **بيانات حقيقية** من جداول النظام
- 🎯 **دقة عالية** في الحسابات
- 🔄 **تحديث فوري** عند إضافة بيانات جديدة
- 🎨 **واجهة تفاعلية** مع فلاتر متقدمة
- 📈 **مخططات ديناميكية** تعكس الوضع الحقيقي
- 📋 **جداول تفصيلية** للمنتجات والموظفين

**الآن يمكن للأدمن الحصول على تقرير مالي شامل ودقيق يعكس الوضع الحقيقي للمطعم! 💰📈**
