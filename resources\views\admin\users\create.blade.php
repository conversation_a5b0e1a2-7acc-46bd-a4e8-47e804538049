@extends('layouts.admin')

@section('title', 'إضافة مستخدم جديد - نظام إدارة المطعم')

@section('page-title', 'إضافة مستخدم جديد')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إضافة مستخدم جديد</h2>
        <a href="{{ route('admin.users') }}" class="bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg flex items-center">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للقائمة</span>
        </a>
    </div>

    @if($errors->any())
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <div class="flex items-center mb-2">
            <i class="fas fa-exclamation-circle ml-2 text-xl"></i>
            <strong class="font-bold">يرجى تصحيح الأخطاء التالية:</strong>
        </div>
        <ul class="list-disc list-inside">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden p-6">
        <form action="{{ route('admin.users.store') }}" method="POST">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول</label>
                    <input type="text" name="first_name" id="first_name" value="{{ old('first_name') }}" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                </div>
                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير</label>
                    <input type="text" name="last_name" id="last_name" value="{{ old('last_name') }}" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                </div>
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                    <input type="email" name="email" id="email" value="{{ old('email') }}" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                </div>
                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone') }}" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة المرور</label>
                    <input type="password" name="password" id="password" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                </div>
                <div>
                    <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تأكيد كلمة المرور</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                    <p class="text-xs text-gray-500 mt-1">يجب أن تتطابق مع كلمة المرور</p>
                </div>
                <div>
                    <label for="user_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">نوع المستخدم</label>
                    <select name="user_type" id="user_type" class="w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-primary" required>
                        <option value="admin" {{ old('user_type') == 'admin' ? 'selected' : '' }}>مدير</option>
                        <option value="employee" {{ old('user_type') == 'employee' ? 'selected' : '' }}>موظف</option>
                        <option value="customer" {{ old('user_type') == 'customer' ? 'selected' : '' }}>عميل</option>
                    </select>
                </div>
                <div>
                    <label class="flex items-center mt-6">
                        <input type="hidden" name="is_active" value="0">
                        <input type="checkbox" name="is_active" id="is_active" value="1" class="form-checkbox h-5 w-5 text-primary" {{ old('is_active') ? 'checked' : '' }}>
                        <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">حساب نشط</span>
                    </label>
                </div>
            </div>
            <div class="mt-6">
                <button type="submit" class="bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg">
                    <i class="fas fa-save ml-2"></i>
                    <span>حفظ المستخدم</span>
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.querySelector('form');
        const firstNameInput = document.getElementById('first_name');
        const lastNameInput = document.getElementById('last_name');
        const emailInput = document.getElementById('email');
        const phoneInput = document.getElementById('phone');
        const passwordInput = document.getElementById('password');
        const passwordConfirmInput = document.getElementById('password_confirmation');

        // إضافة تحقق من الحقول في جانب العميل
        form.addEventListener('submit', function(e) {
            let isValid = true;
            let errorMessages = [];

            // التحقق من الاسم الأول
            if (!firstNameInput.value.trim()) {
                isValid = false;
                errorMessages.push('حقل الاسم الأول مطلوب');
                firstNameInput.classList.add('border-red-500');
            } else if (firstNameInput.value.length > 50) {
                isValid = false;
                errorMessages.push('حقل الاسم الأول يجب ألا يتجاوز 50 حرفًا');
                firstNameInput.classList.add('border-red-500');
            } else {
                firstNameInput.classList.remove('border-red-500');
            }

            // التحقق من الاسم الأخير
            if (!lastNameInput.value.trim()) {
                isValid = false;
                errorMessages.push('حقل الاسم الأخير مطلوب');
                lastNameInput.classList.add('border-red-500');
            } else if (lastNameInput.value.length > 50) {
                isValid = false;
                errorMessages.push('حقل الاسم الأخير يجب ألا يتجاوز 50 حرفًا');
                lastNameInput.classList.add('border-red-500');
            } else {
                lastNameInput.classList.remove('border-red-500');
            }

            // التحقق من البريد الإلكتروني
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailInput.value.trim()) {
                isValid = false;
                errorMessages.push('حقل البريد الإلكتروني مطلوب');
                emailInput.classList.add('border-red-500');
            } else if (!emailRegex.test(emailInput.value)) {
                isValid = false;
                errorMessages.push('يجب أن يكون حقل البريد الإلكتروني عنوان بريد إلكتروني صالح');
                emailInput.classList.add('border-red-500');
            } else {
                emailInput.classList.remove('border-red-500');
            }

            // التحقق من رقم الهاتف
            if (!phoneInput.value.trim()) {
                isValid = false;
                errorMessages.push('حقل رقم الهاتف مطلوب');
                phoneInput.classList.add('border-red-500');
            } else if (phoneInput.value.length > 20) {
                isValid = false;
                errorMessages.push('حقل رقم الهاتف يجب ألا يتجاوز 20 حرفًا');
                phoneInput.classList.add('border-red-500');
            } else {
                phoneInput.classList.remove('border-red-500');
            }

            // التحقق من كلمة المرور
            if (!passwordInput.value.trim()) {
                isValid = false;
                errorMessages.push('حقل كلمة المرور مطلوب');
                passwordInput.classList.add('border-red-500');
            } else if (passwordInput.value.length < 8) {
                isValid = false;
                errorMessages.push('يجب أن يكون طول حقل كلمة المرور على الأقل 8 أحرف');
                passwordInput.classList.add('border-red-500');
            } else {
                passwordInput.classList.remove('border-red-500');
            }

            // التحقق من تطابق كلمة المرور
            if (passwordInput.value !== passwordConfirmInput.value) {
                isValid = false;
                errorMessages.push('تأكيد حقل كلمة المرور غير متطابق');
                passwordConfirmInput.classList.add('border-red-500');
            } else {
                passwordConfirmInput.classList.remove('border-red-500');
            }

            // إذا كان هناك أخطاء، منع إرسال النموذج وعرض الأخطاء
            if (!isValid) {
                e.preventDefault();

                // إنشاء عنصر لعرض الأخطاء
                const errorDiv = document.createElement('div');
                errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                errorDiv.setAttribute('role', 'alert');

                const headerDiv = document.createElement('div');
                headerDiv.className = 'flex items-center mb-2';
                headerDiv.innerHTML = '<i class="fas fa-exclamation-circle ml-2 text-xl"></i><strong class="font-bold">يرجى تصحيح الأخطاء التالية:</strong>';

                const errorList = document.createElement('ul');
                errorList.className = 'list-disc list-inside';

                errorMessages.forEach(message => {
                    const listItem = document.createElement('li');
                    listItem.textContent = message;
                    errorList.appendChild(listItem);
                });

                errorDiv.appendChild(headerDiv);
                errorDiv.appendChild(errorList);

                // إضافة عنصر الأخطاء قبل النموذج
                const formContainer = form.parentElement;

                // إزالة أي رسائل خطأ سابقة
                const existingErrors = formContainer.querySelector('[role="alert"]');
                if (existingErrors) {
                    formContainer.removeChild(existingErrors);
                }

                formContainer.insertBefore(errorDiv, form);

                // التمرير إلى أعلى الصفحة
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        });
    });
</script>
@endsection
