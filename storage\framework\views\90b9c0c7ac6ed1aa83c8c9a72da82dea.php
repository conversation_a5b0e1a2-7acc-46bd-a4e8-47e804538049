<!-- قسم التقييمات -->
<div class="py-16 bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">ماذا يقول عملاؤنا</h2>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">تجارب حقيقية لعملائنا الكرام</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php $__empty_1 = true; $__currentLoopData = $latestReviews; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $review): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="card-hover bg-white dark:bg-gray-800 rounded-lg p-6 shadow-md">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center overflow-hidden ml-3">
                        <span class="text-primary text-xl font-bold"><?php echo e(substr($review->first_name, 0, 1)); ?></span>
                    </div>
                    <div>
                        <h4 class="font-bold text-gray-800 dark:text-white"><?php echo e($review->first_name); ?> <?php echo e($review->last_name); ?></h4>
                        <div class="text-yellow-400 flex text-sm">
                            <?php for($i = 1; $i <= 5; $i++): ?>
                                <?php if($i <= $review->rating): ?>
                                    <i class="fas fa-star"></i>
                                <?php elseif($i - 0.5 <= $review->rating): ?>
                                    <i class="fas fa-star-half-alt"></i>
                                <?php else: ?>
                                    <i class="far fa-star"></i>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 italic">"<?php echo e($review->comment); ?>"</p>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <!-- تقييمات افتراضية في حالة عدم وجود تقييمات -->
            <?php echo $__env->make('customer.components.default-reviews', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <?php endif; ?>
        </div>

        <!-- زر عرض جميع التقييمات -->
        <div class="mt-12 text-center">
            <button onclick="showAllReviews()" class="btn-hover-effect inline-block bg-white dark:bg-gray-800 border-2 border-primary text-primary hover:bg-primary hover:text-white dark:hover:bg-primary font-bold py-3 px-8 rounded-full text-lg transition">
                <i class="fas fa-comments ml-2"></i>
                عرض جميع التقييمات
            </button>
        </div>
    </div>
</div>

<!-- Modal لعرض جميع التقييمات -->
<div id="reviewsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div class="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-2xl font-bold text-gray-800 dark:text-white">جميع التقييمات</h3>
            <button onclick="closeReviewsModal()" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>
        <div class="p-6 overflow-y-auto max-h-[70vh]">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="allReviewsContainer">
                <!-- التقييمات ستتم إضافتها هنا -->
            </div>
        </div>
        <div class="p-6 border-t border-gray-200 dark:border-gray-700 flex justify-end">
            <button onclick="closeReviewsModal()" class="bg-primary hover:bg-primary/90 text-white px-6 py-2 rounded-lg transition">
                إغلاق
            </button>
        </div>
    </div>
</div>

<script>
function showAllReviews() {
    const allReviews = [
        {
            name: 'محمد العلي',
            initial: 'م',
            rating: 5,
            comment: 'تجربة رائعة وطعام لذيذ جداً! البرجر كان شهي والخدمة ممتازة. سأعود بالتأكيد مع العائلة.'
        },
        {
            name: 'فاطمة أحمد',
            initial: 'ف',
            rating: 4.5,
            comment: 'الطعام لذيذ والأجواء رائعة. أعجبني تنوع القائمة والأسعار معقولة. الخدمة سريعة ومهذبة.'
        },
        {
            name: 'عبدالله سالم',
            initial: 'ع',
            rating: 4,
            comment: 'مطعم ممتاز! الطعام طازج والطعم أصيل. المكان نظيف والموظفون ودودون. أنصح بتجربة البيتزا الخاصة بهم.'
        },
        {
            name: 'سارة محمود',
            initial: 'س',
            rating: 5,
            comment: 'أفضل مطعم في المنطقة! الطعام شهي والخدمة سريعة. أحب الأجواء الهادئة والديكور الجميل.'
        },
        {
            name: 'أحمد حسن',
            initial: 'أ',
            rating: 4.5,
            comment: 'تجربة ممتازة! الطعام طازج ولذيذ. الأسعار مناسبة والموقع مريح. سأعود قريباً مع الأصدقاء.'
        },
        {
            name: 'نور الدين',
            initial: 'ن',
            rating: 5,
            comment: 'مطعم رائع بكل المقاييس! الطعام عالي الجودة والخدمة احترافية. أنصح الجميع بزيارته.'
        }
    ];

    const container = document.getElementById('allReviewsContainer');
    container.innerHTML = '';

    allReviews.forEach(review => {
        const stars = generateStars(review.rating);
        const reviewHTML = `
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div class="flex items-center mb-3">
                    <div class="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20 flex items-center justify-center ml-3">
                        <span class="text-primary text-lg font-bold">${review.initial}</span>
                    </div>
                    <div>
                        <h4 class="font-bold text-gray-800 dark:text-white">${review.name}</h4>
                        <div class="text-yellow-400 flex text-sm">
                            ${stars}
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 text-sm italic">"${review.comment}"</p>
            </div>
        `;
        container.innerHTML += reviewHTML;
    });

    document.getElementById('reviewsModal').classList.remove('hidden');
}

function generateStars(rating) {
    let stars = '';
    for (let i = 1; i <= 5; i++) {
        if (i <= rating) {
            stars += '<i class="fas fa-star"></i>';
        } else if (i - 0.5 <= rating) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        } else {
            stars += '<i class="far fa-star"></i>';
        }
    }
    return stars;
}

function closeReviewsModal() {
    document.getElementById('reviewsModal').classList.add('hidden');
}

// إغلاق المودال عند النقر خارجه
document.getElementById('reviewsModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeReviewsModal();
    }
});
</script>
<?php /**PATH D:\ccss450\cs450level10\resources\views/customer/sections/reviews.blade.php ENDPATH**/ ?>