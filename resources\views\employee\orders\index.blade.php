@extends('employee.layouts.app')

@section('title', 'إدارة الطلبات')

@section('content')
<div id="orders-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة الطلبات</h2>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <form action="{{ route('employee.orders') }}" method="GET" class="relative">
                @if(request('status'))
                    <input type="hidden" name="status" value="{{ request('status') }}">
                @endif
                <input type="text" name="search" placeholder="بحث برقم الطلب أو اسم العميل..." value="{{ request('search') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                <button type="submit" class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            <a href="{{ route('employee.orders.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إنشاء طلب</span>
            </a>
        </div>
    </div>

    @if(session('success'))
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('success') }}</span>
    </div>
    @endif

    @if(session('error'))
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
        <span class="block sm:inline">{{ session('error') }}</span>
    </div>
    @endif

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">إجمالي الطلبات</h5>
                    <p class="text-3xl font-bold text-primary">{{ $orderStats['total'] }}</p>
                </div>
                <div class="text-4xl text-primary">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">طلبات قيد الانتظار</h5>
                    <p class="text-3xl font-bold text-yellow-500">{{ $orderStats['pending'] }}</p>
                </div>
                <div class="text-4xl text-yellow-500">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">طلبات قيد التحضير</h5>
                    <p class="text-3xl font-bold text-blue-500">{{ $orderStats['preparing'] }}</p>
                </div>
                <div class="text-4xl text-blue-500">
                    <i class="fas fa-utensils"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-center">
                <div>
                    <h5 class="text-lg font-semibold text-gray-800 dark:text-white">طلبات مكتملة</h5>
                    <p class="text-3xl font-bold text-green-500">{{ $orderStats['completed'] }}</p>
                </div>
                <div class="text-4xl text-green-500">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="flex flex-wrap border-b border-gray-200 dark:border-gray-700">
            <a href="{{ route('employee.orders') }}" class="px-6 py-3 {{ !request('status') ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-list-ul ml-1"></i>جميع الطلبات
            </a>
            <a href="{{ route('employee.orders', ['status' => 'pending']) }}" class="px-6 py-3 {{ request('status') == 'pending' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-clock ml-1"></i>قيد الانتظار
            </a>
            <a href="{{ route('employee.orders', ['status' => 'preparing']) }}" class="px-6 py-3 {{ request('status') == 'preparing' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-utensils ml-1"></i>قيد التحضير
            </a>
            <a href="{{ route('employee.orders', ['status' => 'completed']) }}" class="px-6 py-3 {{ request('status') == 'completed' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-check-circle ml-1"></i>مكتمل
            </a>
            <a href="{{ route('employee.orders', ['status' => 'canceled']) }}" class="px-6 py-3 {{ request('status') == 'canceled' ? 'border-b-2 border-primary text-primary' : 'text-gray-600 dark:text-gray-300 hover:text-primary' }} font-medium">
                <i class="fas fa-times-circle ml-1"></i>ملغي
            </a>
        </div>

        <div class="p-6">
            <div class="mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 md:mb-0">سجل الطلبات</h3>
                <div class="flex space-x-2 space-x-reverse">
                    <form action="{{ route('employee.orders') }}" method="GET" class="flex space-x-2 space-x-reverse">
                        @if(request('status'))
                            <input type="hidden" name="status" value="{{ request('status') }}">
                        @endif
                        @if(request('search'))
                            <input type="hidden" name="search" value="{{ request('search') }}">
                        @endif
                        <div class="relative">
                            <input type="date" name="date" value="{{ request('date') }}" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                        <div class="relative">
                            <select name="payment_method" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border border-gray-300 dark:border-gray-600 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                                <option value="">جميع طرق الدفع</option>
                                <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                                <option value="credit_card" {{ request('payment_method') == 'credit_card' ? 'selected' : '' }}>بطاقة ائتمان</option>
                                <option value="wallet" {{ request('payment_method') == 'wallet' ? 'selected' : '' }}>محفظة رقمية</option>
                            </select>
                        </div>
                        <button type="submit" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md p-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                            <i class="fas fa-filter"></i>
                        </button>
                    </form>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700">
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                رقم الطلب
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                العميل
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                المنتجات
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                المبلغ
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                طريقة الدفع
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الحالة
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                تاريخ الطلب
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                الإجراءات
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($orders as $order)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                <span class="bg-primary/10 text-primary px-2 py-1 rounded-md font-bold">#{{ $order->order_id }}</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center text-primary font-bold ml-2">
                                        {{ substr($order->user->first_name, 0, 1) }}
                                    </div>
                                    <span>{{ $order->user->first_name }} {{ $order->user->last_name }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if($order->items->count() > 0)
                                    <div class="flex flex-col">
                                        @foreach($order->items->take(2) as $item)
                                            <span class="inline-flex items-center">
                                                <i class="fas fa-utensils text-xs text-primary ml-1"></i>
                                                {{ $item->menuItem->name ?? 'غير معروف' }}
                                                <span class="text-xs text-gray-500 dark:text-gray-400 mr-1">({{ $item->quantity }})</span>
                                            </span>
                                        @endforeach
                                        @if($order->items->count() > 2)
                                            <span class="text-xs text-primary mt-1">+ {{ $order->items->count() - 2 }} عناصر أخرى</span>
                                        @endif
                                    </div>
                                @else
                                    <span class="text-gray-400 dark:text-gray-500">لا توجد عناصر</span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                                <span class="text-primary font-bold">{{ number_format($order->total_amount, 2) }}</span> <span class="text-xs text-gray-500 dark:text-gray-400">د.ل</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                @if(isset($order->payments) && $order->payments->count() > 0)
                                    @if($order->payments->first()->payment_method == 'cash')
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-money-bill-wave text-green-500 ml-1"></i>
                                            <span>نقدي</span>
                                        </span>
                                    @else
                                        <span class="inline-flex items-center">
                                            <i class="fas fa-credit-card text-blue-500 ml-1"></i>
                                            <span>بطاقة ائتمان</span>
                                        </span>
                                    @endif
                                @else
                                    <span class="inline-flex items-center text-gray-400 dark:text-gray-500">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>غير مدفوع</span>
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @if($order->status == 'pending')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                        <i class="fas fa-clock ml-1"></i>
                                        <span>قيد الانتظار</span>
                                    </span>
                                @elseif($order->status == 'preparing')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                        <i class="fas fa-utensils ml-1"></i>
                                        <span>قيد التحضير</span>
                                    </span>
                                @elseif($order->status == 'completed')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                        <i class="fas fa-check-circle ml-1"></i>
                                        <span>مكتمل</span>
                                    </span>
                                @elseif($order->status == 'canceled')
                                    <span class="inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                        <i class="fas fa-times-circle ml-1"></i>
                                        <span>ملغي</span>
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                                {{ $order->created_at->format('Y-m-d H:i') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @if($order->status == 'pending')
                                        <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="preparing">
                                            <button type="submit" class="p-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50">
                                                <i class="fas fa-utensils"></i>
                                            </button>
                                        </form>
                                    @elseif($order->status == 'preparing')
                                        <form action="{{ route('employee.orders.update-status', $order->order_id) }}" method="POST" class="inline">
                                            @csrf
                                            @method('PUT')
                                            <input type="hidden" name="status" value="completed">
                                            <button type="submit" class="p-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @endif
                                    <a href="{{ route('employee.orders.show', $order->order_id) }}" class="p-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($order->status == 'pending' || $order->status == 'preparing')
                                    <a href="{{ route('employee.orders.edit', $order->order_id) }}" class="p-1 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 hover:bg-yellow-200 dark:hover:bg-yellow-900/50">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                                لا توجد طلبات متاحة
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700 dark:text-gray-300">
                    @if($orders->total() > 0)
                        عرض <span class="font-medium">{{ $orders->firstItem() }}</span> إلى <span class="font-medium">{{ $orders->lastItem() }}</span> من <span class="font-medium">{{ $orders->total() }}</span> طلب
                    @else
                        لا توجد طلبات
                    @endif
                </div>
                <div class="flex items-center space-x-2 space-x-reverse">
                    {{ $orders->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
