@extends('employee.layouts.app')

@section('content')
<!-- Hero Section -->
<div class="relative mb-8 overflow-hidden">
    <div class="bg-white dark:bg-gray-800 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-black text-gray-800 dark:text-white mb-3">
                    مرحباً، <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{{ Auth::user()->first_name ?? 'الموظف' }}</span>!
                    <span class="inline-block animate-bounce">👋</span>
                </h1>
                <p class="text-gray-600 dark:text-gray-400 text-lg font-medium">
                    إليك نظرة عامة على أنشطة المطعم اليوم
                </p>
            </div>
            <div class="hidden md:block">
                <div class="w-24 h-24 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-3xl font-bold">
                    {{ substr(Auth::user()->first_name ?? 'M', 0, 1) }}
                </div>
            </div>
        </div>
    </div>
</div>

<div id="dashboard-page" class="page">
    <div class="mb-8">
        <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-6">نظرة عامة</h2>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
            <!-- بطاقة إحصائية - الطلبات اليوم -->
            <div class="relative bg-gradient-to-br from-cyan-50 via-blue-50 to-indigo-100 dark:from-cyan-900/30 dark:via-blue-900/30 dark:to-indigo-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-cyan-200/60 dark:border-cyan-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-cyan-400 via-blue-500 to-indigo-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-cyan-400/20 to-blue-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-cyan-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الطلبات اليوم</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $todayStats['ordersCount'] ?? 0 }}</h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                {{ $todayStats['totalSales'] ?? 0 }} د.ل
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-cyan-500 via-blue-600 to-indigo-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-shopping-cart text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - الحجوزات اليوم -->
            <div class="relative bg-gradient-to-br from-emerald-50 via-teal-50 to-green-100 dark:from-emerald-900/30 dark:via-teal-900/30 dark:to-green-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-emerald-200/60 dark:border-emerald-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-emerald-400 via-teal-500 to-green-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-emerald-400/20 to-teal-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-green-500/20 to-lime-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-emerald-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الحجوزات اليوم</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $todayStats['reservationsCount'] ?? 0 }}</h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                نشطة
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-emerald-500 via-teal-600 to-green-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-calendar-check text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - الطلبات المعلقة -->
            <div class="relative bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-100 dark:from-orange-900/30 dark:via-amber-900/30 dark:to-yellow-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-orange-200/60 dark:border-orange-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-400 via-amber-500 to-yellow-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-amber-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-yellow-500/20 to-orange-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-orange-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الطلبات المعلقة</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $todayStats['pendingOrdersCount'] ?? 0 }}</h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-orange-400 to-amber-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                في الانتظار
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-orange-500 via-amber-600 to-yellow-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-clock text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>

            <!-- بطاقة إحصائية - الطاولات المتاحة -->
            <div class="relative bg-gradient-to-br from-purple-50 via-violet-50 to-indigo-100 dark:from-purple-900/30 dark:via-violet-900/30 dark:to-indigo-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-purple-200/60 dark:border-purple-700/60 group transform hover:-translate-y-2 transition-all duration-500">
                <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-purple-400 via-violet-500 to-indigo-600 rounded-t-3xl"></div>
                <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-violet-500/20 rounded-full blur-xl"></div>
                <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full blur-lg"></div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-purple-400 rounded-full animate-ping"></div>

                <div class="flex justify-between items-start relative z-10">
                    <div class="flex-1">
                        <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">الطاولات المتاحة</p>
                        <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $tableStats['available'] ?? 0 }}</h3>
                        <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                            <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full animate-pulse shadow-lg"></div>
                            <p class="text-gray-800 dark:text-white text-base font-bold">
                                من {{ $tableStats['total'] ?? 0 }} طاولة
                            </p>
                        </div>
                    </div>
                    <div class="rounded-3xl bg-gradient-to-br from-purple-500 via-violet-600 to-indigo-700 p-6 shadow-2xl group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 border-4 border-white/30">
                        <i class="fas fa-chair text-white text-3xl drop-shadow-lg"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
        <!-- الطلبات النشطة -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2 flex items-center">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3">
                            <i class="fas fa-shopping-cart text-white text-sm"></i>
                        </div>
                        الطلبات النشطة
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">طلبات تحتاج إلى معالجة</p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="relative">
                        <select id="orderStatusFilter" class="appearance-none bg-gray-100 border border-gray-300 text-gray-800 py-2 px-4 pr-10 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                            <option value="all">جميع الحالات</option>
                            <option value="pending">قيد الانتظار</option>
                            <option value="preparing">قيد التحضير</option>
                            <option value="completed">مكتمل</option>
                            <option value="canceled">ملغي</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center px-3 text-gray-600">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    <a href="{{ route('employee.orders') }}" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl text-sm font-semibold hover:scale-105 transition-transform duration-300">عرض الكل</a>
                </div>
            </div>

            <!-- شريط البحث السريع -->
            <div class="mb-6">
                <div class="relative">
                    <input type="text" id="quickOrderSearch" placeholder="بحث سريع في الطلبات..." class="w-full px-6 py-4 pr-14 rounded-2xl bg-gray-100 border border-gray-300 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 text-sm">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
                        <i class="fas fa-search text-gray-500"></i>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto rounded-2xl bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200 border-b border-gray-200 dark:border-gray-600">
                            <th class="py-4 px-6 text-right font-semibold">#</th>
                            <th class="py-4 px-6 text-right font-semibold">العميل</th>
                            <th class="py-4 px-6 text-right font-semibold">المنتجات</th>
                            <th class="py-4 px-6 text-right font-semibold">المبلغ</th>
                            <th class="py-4 px-6 text-right font-semibold">الحالة</th>
                            <th class="py-4 px-6 text-right font-semibold">الوقت</th>
                            <th class="py-4 px-6 text-right font-semibold">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10" id="activeOrdersTable">
                        @forelse($pendingOrders as $order)
                        <tr class="hover:bg-white/5 order-row transition-all duration-300 modern-card" data-status="{{ $order->status }}">
                            <td class="py-4 px-6">
                                <span class="font-bold text-white">#{{ $order->order_id }}</span>
                            </td>
                            <td class="py-4 px-6">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary flex items-center justify-center text-white text-sm font-bold mr-3">
                                        {{ substr($order->user->first_name, 0, 1) }}
                                    </div>
                                    <span class="text-white/90 font-medium">{{ $order->user->first_name }} {{ $order->user->last_name }}</span>
                                </div>
                            </td>
                            <td class="py-4 px-6">
                                @if($order->items->count() > 0)
                                    <div class="flex flex-wrap gap-2">
                                    @foreach($order->items->take(2) as $item)
                                        <span class="inline-block px-3 py-1 bg-gray-100 border border-gray-300 rounded-xl text-xs text-gray-800">
                                            {{ $item->menuItem->name ?? 'غير معروف' }}
                                            @if($item->quantity > 1)
                                                <span class="text-blue-600 font-bold">×{{ $item->quantity }}</span>
                                            @endif
                                        </span>
                                    @endforeach
                                    @if($order->items->count() > 2)
                                        <span class="inline-block px-3 py-1 bg-gray-100 border border-gray-300 rounded-xl text-xs text-gray-600">+{{ $order->items->count() - 2 }}</span>
                                    @endif
                                    </div>
                                @else
                                    <span class="text-gray-500">لا توجد عناصر</span>
                                @endif
                            </td>
                            <td class="py-4 px-6 font-bold text-gray-800">{{ $order->total_amount }} د.ل</td>
                            <td class="py-4 px-6">
                                @if($order->status == 'pending')
                                    <span class="px-3 py-2 bg-yellow-100 text-yellow-800 border border-yellow-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-yellow-500 rounded-full inline-block mr-2"></span>
                                        قيد الانتظار
                                    </span>
                                @elseif($order->status == 'preparing')
                                    <span class="px-3 py-2 bg-blue-100 text-blue-800 border border-blue-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-blue-500 rounded-full inline-block mr-2"></span>
                                        قيد التحضير
                                    </span>
                                @elseif($order->status == 'completed')
                                    <span class="px-3 py-2 bg-green-100 text-green-800 border border-green-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-green-500 rounded-full inline-block mr-2"></span>
                                        مكتمل
                                    </span>
                                @elseif($order->status == 'canceled')
                                    <span class="px-3 py-2 bg-red-100 text-red-800 border border-red-300 rounded-xl text-xs font-bold flex items-center w-fit">
                                        <span class="w-2 h-2 bg-red-500 rounded-full inline-block mr-2"></span>
                                        ملغي
                                    </span>
                                @endif
                            </td>
                            <td class="py-4 px-6">
                                <div class="flex flex-col">
                                    <span class="text-white font-semibold">{{ $order->created_at->format('H:i') }}</span>
                                    <span class="text-xs text-white/60">{{ $order->created_at->diffForHumans() }}</span>
                                </div>
                            </td>
                            <td class="py-4 px-6">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @if($order->status == 'pending')
                                        <a href="{{ route('employee.orders.update-status', ['id' => $order->order_id, 'status' => 'preparing']) }}" class="p-2 rounded-xl bg-gradient-to-r from-indigo-500/20 to-purple-500/20 hover:from-indigo-500/30 hover:to-purple-500/30 text-indigo-300 hover:text-indigo-200 transition-all duration-300 group border border-indigo-500/30" title="بدء التحضير">
                                            <i class="fas fa-utensils group-hover:scale-110 transition-transform"></i>
                                        </a>
                                    @elseif($order->status == 'preparing')
                                        <a href="{{ route('employee.orders.update-status', ['id' => $order->order_id, 'status' => 'completed']) }}" class="p-2 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 text-green-300 hover:text-green-200 transition-all duration-300 group border border-green-500/30" title="إكمال الطلب">
                                            <i class="fas fa-check-circle group-hover:scale-110 transition-transform"></i>
                                        </a>
                                    @endif
                                    <a href="{{ route('employee.orders.show', $order->order_id) }}" class="p-2 rounded-xl bg-gray-100 hover:bg-gray-200 text-gray-600 hover:text-gray-800 transition-all duration-300 group" title="عرض التفاصيل">
                                        <i class="fas fa-eye group-hover:scale-110 transition-transform"></i>
                                    </a>
                                    <button type="button" class="p-2 rounded-xl bg-gradient-to-r from-gray-500/20 to-slate-500/20 hover:from-gray-500/30 hover:to-slate-500/30 text-gray-300 hover:text-gray-200 transition-all duration-300 group border border-gray-500/30 print-receipt" data-order-id="{{ $order->order_id }}" title="طباعة الإيصال">
                                        <i class="fas fa-print group-hover:scale-110 transition-transform"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="py-12 text-center">
                                <div class="flex flex-col items-center justify-center">
                                    <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center mb-4">
                                        <i class="fas fa-clipboard-list text-3xl text-gray-400 dark:text-gray-300"></i>
                                    </div>
                                    <p class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد طلبات نشطة حالياً</p>
                                    <a href="{{ route('employee.orders') }}" class="mt-3 px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform duration-300">إنشاء طلب جديد</a>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- سكريبت البحث السريع وتصفية الطلبات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const quickSearch = document.getElementById('quickOrderSearch');
                    const statusFilter = document.getElementById('orderStatusFilter');
                    const orderRows = document.querySelectorAll('.order-row');

                    // البحث السريع
                    quickSearch.addEventListener('input', filterOrders);

                    // تصفية حسب الحالة
                    statusFilter.addEventListener('change', filterOrders);

                    function filterOrders() {
                        const searchTerm = quickSearch.value.toLowerCase();
                        const statusValue = statusFilter.value;

                        orderRows.forEach(row => {
                            const orderStatus = row.getAttribute('data-status');
                            const orderText = row.textContent.toLowerCase();

                            const matchesSearch = searchTerm === '' || orderText.includes(searchTerm);
                            const matchesStatus = statusValue === 'all' || orderStatus === statusValue;

                            row.style.display = (matchesSearch && matchesStatus) ? '' : 'none';
                        });

                        // عرض رسالة إذا لم يتم العثور على نتائج
                        const visibleRows = document.querySelectorAll('.order-row[style=""]').length;
                        const noResultsRow = document.getElementById('noResultsRow');

                        if (visibleRows === 0) {
                            if (!noResultsRow) {
                                const tbody = document.getElementById('activeOrdersTable');
                                const newRow = document.createElement('tr');
                                newRow.id = 'noResultsRow';
                                newRow.innerHTML = `
                                    <td colspan="7" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                        <div class="flex flex-col items-center justify-center">
                                            <i class="fas fa-search text-4xl mb-3 text-gray-300 dark:text-gray-600"></i>
                                            <p>لم يتم العثور على نتائج</p>
                                        </div>
                                    </td>
                                `;
                                tbody.appendChild(newRow);
                            }
                        } else if (noResultsRow) {
                            noResultsRow.remove();
                        }
                    }

                    // طباعة الإيصال
                    const printButtons = document.querySelectorAll('.print-receipt');
                    printButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const orderId = this.getAttribute('data-order-id');
                            // هنا يمكن إضافة كود لطباعة الإيصال
                            alert('جاري طباعة إيصال للطلب رقم ' + orderId);
                        });
                    });
                });
            </script>
        </div>

        <!-- حجوزات اليوم -->
        <div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white mb-2 flex items-center">
                        <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-check text-white text-sm"></i>
                        </div>
                        حجوزات اليوم
                    </h3>
                    <p class="text-white/70">{{ $todayReservations->count() }} حجز</p>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <a href="{{ route('employee.reservations.create') }}" class="p-3 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 text-green-300 hover:text-green-200 transition-all duration-300 group border border-green-500/30" title="إضافة حجز جديد">
                        <i class="fas fa-plus group-hover:scale-110 transition-transform"></i>
                    </a>
                    <a href="{{ route('employee.reservations') }}" class="px-4 py-2 bg-gradient-to-r from-primary to-secondary text-white rounded-xl text-sm font-semibold hover:scale-105 transition-transform duration-300 neon-glow">عرض الكل</a>
                </div>
            </div>

            <!-- الجدول الزمني للحجوزات -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-bold text-white">الجدول الزمني</h4>
                    <div class="flex text-sm space-x-4 space-x-reverse">
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full inline-block mr-2 animate-pulse"></span>
                            <span class="text-green-300">قادم</span>
                        </span>
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full inline-block mr-2 animate-pulse"></span>
                            <span class="text-blue-300">حالي</span>
                        </span>
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-gray-400 to-slate-400 rounded-full inline-block mr-2"></span>
                            <span class="text-gray-300">مضى</span>
                        </span>
                    </div>
                </div>
                <div class="relative h-12 bg-gray-100 rounded-2xl overflow-hidden border border-gray-300">
                    <div class="absolute inset-0 flex">
                        @php
                            $now = \Carbon\Carbon::now();
                            $startOfDay = $now->copy()->startOfDay();
                            $endOfDay = $now->copy()->endOfDay();
                            $totalMinutes = $startOfDay->diffInMinutes($endOfDay);
                            $minutesSinceMidnight = $startOfDay->diffInMinutes($now);
                            $currentTimePosition = ($minutesSinceMidnight / $totalMinutes) * 100;
                        @endphp

                        <!-- مؤشر الوقت الحالي -->
                        <div class="absolute h-full w-1 bg-gradient-to-b from-red-400 to-pink-400 z-10 rounded-full animate-pulse" style="left: {{ $currentTimePosition }}%"></div>

                        <!-- الحجوزات -->
                        @foreach($todayReservations as $reservation)
                            @php
                                $reservationTime = $reservation->reservation_time;
                                $minutesSinceMidnightForReservation = $startOfDay->diffInMinutes($reservationTime);
                                $position = ($minutesSinceMidnightForReservation / $totalMinutes) * 100;
                                $isPast = $reservationTime->isPast();
                                $isCurrent = $reservationTime->isPast() && $reservationTime->addHours(1)->isFuture();
                                $bgColor = $isPast ? ($isCurrent ? 'from-blue-400 to-cyan-400' : 'from-gray-400 to-slate-400') : 'from-green-400 to-emerald-400';
                            @endphp
                            <div class="absolute h-8 w-8 bg-gradient-to-br {{ $bgColor }} rounded-full top-2 -ml-4 flex items-center justify-center text-white text-xs font-bold cursor-pointer reservation-marker hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-white/30" style="left: {{ $position }}%" data-reservation-id="{{ $reservation->reservation_id }}" title="{{ $reservation->user->first_name }} {{ $reservation->user->last_name }} - {{ $reservationTime->format('H:i') }}">
                                {{ $reservation->table->table_number }}
                            </div>
                        @endforeach
                    </div>

                    <!-- مقياس الساعات -->
                    <div class="absolute inset-x-0 -bottom-6 h-4 flex">
                        @for($hour = 0; $hour < 24; $hour += 3)
                            <div class="flex-1 border-r border-white/20 relative">
                                <span class="absolute -bottom-5 right-0 text-xs text-white/60">{{ sprintf('%02d:00', $hour) }}</span>
                            </div>
                        @endfor
                    </div>
                </div>
            </div>

            <!-- قائمة الحجوزات -->
            <div class="space-y-4 max-h-80 overflow-y-auto custom-scrollbar">
                @forelse($todayReservations as $reservation)
                    @php
                        $isPast = $reservation->reservation_time->isPast();
                        $isCurrent = $reservation->reservation_time->isPast() && $reservation->reservation_time->addHours(1)->isFuture();
                        $borderColor = $isPast ? ($isCurrent ? 'from-blue-400 to-cyan-400' : 'from-gray-400 to-slate-400') : 'from-green-400 to-emerald-400';
                        $bgColor = $isPast ? ($isCurrent ? 'bg-gradient-to-r from-blue-500/10 to-cyan-500/10' : 'bg-gradient-to-r from-gray-500/10 to-slate-500/10') : 'bg-gradient-to-r from-green-500/10 to-emerald-500/10';
                    @endphp
                    <div class="p-4 rounded-2xl bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 shadow-sm reservation-item card-hover" id="reservation-{{ $reservation->reservation_id }}">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center mb-3">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br {{ $borderColor }} flex items-center justify-center text-white text-sm font-bold mr-3">
                                        {{ substr($reservation->user->first_name, 0, 1) }}
                                    </div>
                                    <div>
                                        <p class="font-bold text-white text-lg">{{ $reservation->user->first_name }} {{ $reservation->user->last_name }}</p>
                                        <div class="flex items-center mt-1 text-sm text-white/70 space-x-4 space-x-reverse">
                                            <span class="flex items-center">
                                                <i class="fas fa-chair mr-2 text-white/60"></i>
                                                طاولة #{{ $reservation->table->table_number }}
                                            </span>
                                            <span class="flex items-center">
                                                <i class="fas fa-users mr-2 text-white/60"></i>
                                                {{ $reservation->guests_count }} أشخاص
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-left">
                                <p class="font-bold text-white text-xl mb-1">{{ $reservation->reservation_time->format('H:i') }}</p>
                                <p class="text-sm">
                                    @if($isPast)
                                        @if($isCurrent)
                                            <span class="text-blue-300 flex items-center">
                                                <i class="fas fa-clock mr-2 animate-pulse"></i>حالياً
                                            </span>
                                        @else
                                            <span class="text-gray-300 flex items-center">
                                                <i class="fas fa-history mr-2"></i>مضى {{ $reservation->reservation_time->diffForHumans() }}
                                            </span>
                                        @endif
                                    @else
                                        <span class="text-green-300 flex items-center">
                                            <i class="fas fa-hourglass-start mr-2 animate-pulse"></i>قادم {{ $reservation->reservation_time->diffForHumans() }}
                                        </span>
                                    @endif
                                </p>
                            </div>
                        </div>
                        <div class="flex justify-end mt-4 space-x-2 space-x-reverse">
                            <button type="button" class="px-3 py-2 rounded-xl bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900 transition-all duration-300 group text-sm" onclick="window.location.href='{{ route('employee.reservations.show', $reservation->reservation_id) }}'">
                                <i class="fas fa-eye mr-2 group-hover:scale-110 transition-transform"></i>عرض
                            </button>
                            @if(!$isPast || $isCurrent)
                                <button type="button" class="px-3 py-2 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 hover:from-blue-500/30 hover:to-cyan-500/30 text-blue-300 hover:text-blue-200 transition-all duration-300 group border border-blue-500/30 text-sm" onclick="window.location.href='{{ route('employee.reservations.edit', $reservation->reservation_id) }}'">
                                    <i class="fas fa-edit mr-2 group-hover:scale-110 transition-transform"></i>تعديل
                                </button>
                                <button type="button" class="px-3 py-2 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 hover:from-green-500/30 hover:to-emerald-500/30 text-green-300 hover:text-green-200 transition-all duration-300 group border border-green-500/30 text-sm" onclick="alert('تم تأكيد الحضور للحجز رقم {{ $reservation->reservation_id }}')">
                                    <i class="fas fa-check-circle mr-2 group-hover:scale-110 transition-transform"></i>تأكيد الحضور
                                </button>
                            @endif
                        </div>
                    </div>
                @empty
                    <div class="bg-white dark:bg-gray-700 rounded-2xl p-8 text-center border border-gray-200 dark:border-gray-600 shadow-sm">
                        <div class="w-20 h-20 rounded-full bg-gray-100 dark:bg-gray-600 flex items-center justify-center mb-4 mx-auto">
                            <i class="fas fa-calendar-day text-3xl text-gray-400 dark:text-gray-300"></i>
                        </div>
                        <p class="text-gray-800 dark:text-white text-xl font-bold mb-2">لا توجد حجوزات لهذا اليوم</p>
                        <a href="{{ route('employee.reservations') }}" class="mt-4 inline-block px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl font-semibold hover:scale-105 transition-transform duration-300">
                            <i class="fas fa-plus mr-2"></i>إضافة حجز جديد
                        </a>
                    </div>
                @endforelse
            </div>

            <!-- سكريبت التفاعل مع الحجوزات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // التفاعل مع مؤشرات الحجوزات في الجدول الزمني
                    const reservationMarkers = document.querySelectorAll('.reservation-marker');

                    reservationMarkers.forEach(marker => {
                        marker.addEventListener('click', function() {
                            const reservationId = this.getAttribute('data-reservation-id');
                            const reservationItem = document.getElementById('reservation-' + reservationId);

                            // تمرير إلى عنصر الحجز في القائمة
                            if (reservationItem) {
                                reservationItem.scrollIntoView({ behavior: 'smooth', block: 'center' });

                                // إضافة تأثير وميض للعنصر
                                reservationItem.classList.add('bg-primary/20');
                                setTimeout(() => {
                                    reservationItem.classList.remove('bg-primary/20');
                                }, 1000);
                            }
                        });
                    });
                });
            </script>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- حالة الطاولات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">حالة الطاولات</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        <span class="text-green-500">{{ $tableStats['available'] ?? 0 }}</span> متاحة،
                        <span class="text-red-500">{{ $tableStats['occupied'] ?? 0 }}</span> مشغولة،
                        <span class="text-yellow-500">{{ $tableStats['reserved'] ?? 0 }}</span> محجوزة
                    </p>
                </div>
                <div class="flex items-center">
                    <div class="relative ml-2">
                        <select id="tableAreaFilter" class="appearance-none bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-1 px-3 pr-8 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="all">جميع المناطق</option>
                            <option value="indoor">داخلي</option>
                            <option value="outdoor">خارجي</option>
                            <option value="vip">VIP</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>
                    <a href="{{ route('employee.tables') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
            </div>

            <!-- مخطط المطعم -->
            <div class="restaurant-layout mb-4 p-4 bg-gray-50 dark:bg-gray-700/30 rounded-lg">
                <div class="flex justify-between items-center mb-2">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">مخطط المطعم</h4>
                    <div class="flex text-xs">
                        <span class="ml-2 flex items-center">
                            <span class="w-3 h-3 bg-green-500/20 border border-green-500 rounded-sm inline-block ml-1"></span>
                            متاح
                        </span>
                        <span class="ml-2 flex items-center">
                            <span class="w-3 h-3 bg-red-500/20 border border-red-500 rounded-sm inline-block ml-1"></span>
                            مشغول
                        </span>
                        <span class="flex items-center">
                            <span class="w-3 h-3 bg-yellow-500/20 border border-yellow-500 rounded-sm inline-block ml-1"></span>
                            محجوز
                        </span>
                    </div>
                </div>

                <div class="restaurant-map relative h-64 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                    <!-- مناطق المطعم -->
                    <div class="absolute inset-0">
                        <!-- منطقة داخلية -->
                        <div class="absolute top-2 right-2 bottom-2 left-1/2 border border-gray-400 dark:border-gray-500 rounded-lg bg-gray-100/50 dark:bg-gray-800/50 p-2">
                            <div class="text-xs text-gray-500 dark:text-gray-400 absolute top-1 right-2">المنطقة الداخلية</div>
                            <div class="table-grid h-full pt-5">
                                @foreach($tables as $table)
                                    @if($table->area == 'indoor')
                                        <div class="restaurant-table table-{{ $table->status }} cursor-pointer" data-table-id="{{ $table->table_id }}" data-area="{{ $table->area }}">
                                            <span class="table-number">{{ $table->table_number }}</span>
                                            <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>

                        <!-- منطقة خارجية -->
                        <div class="absolute top-2 left-2 bottom-2 right-1/2 mr-2 border border-gray-400 dark:border-gray-500 rounded-lg bg-gray-100/50 dark:bg-gray-800/50 p-2">
                            <div class="text-xs text-gray-500 dark:text-gray-400 absolute top-1 right-2">المنطقة الخارجية</div>
                            <div class="table-grid h-full pt-5">
                                @foreach($tables as $table)
                                    @if($table->area == 'outdoor')
                                        <div class="restaurant-table table-{{ $table->status }} cursor-pointer" data-table-id="{{ $table->table_id }}" data-area="{{ $table->area }}">
                                            <span class="table-number">{{ $table->table_number }}</span>
                                            <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>

                        <!-- منطقة VIP -->
                        <div class="absolute bottom-2 left-1/4 right-1/4 h-16 border border-primary dark:border-primary rounded-lg bg-primary/10 dark:bg-primary/20 p-2">
                            <div class="text-xs text-primary absolute top-1 right-2">VIP</div>
                            <div class="flex justify-center items-center h-full">
                                @foreach($tables as $table)
                                    @if($table->area == 'vip')
                                        <div class="restaurant-table table-{{ $table->status }} cursor-pointer mx-1" data-table-id="{{ $table->table_id }}" data-area="{{ $table->area }}">
                                            <span class="table-number">{{ $table->table_number }}</span>
                                            <span class="table-capacity">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة الطاولات -->
            <div class="table-grid" id="tablesGrid">
                @forelse($tables as $table)
                <div class="restaurant-table table-{{ $table->status }} cursor-pointer" data-table-id="{{ $table->table_id }}" data-area="{{ $table->area }}">
                    <span class="table-number">{{ $table->table_number }}</span>
                    <div class="flex flex-col items-center">
                        <span class="table-capacity mb-1">{{ $table->capacity }} <i class="fas fa-user-friends text-xs"></i></span>
                        <span class="table-status
                            @if($table->status == 'available')
                                bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300
                            @elseif($table->status == 'occupied')
                                bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300
                            @elseif($table->status == 'reserved')
                                bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300
                            @endif
                        ">
                            @if($table->status == 'available')
                                متاح
                            @elseif($table->status == 'occupied')
                                مشغول
                            @elseif($table->status == 'reserved')
                                محجوز
                            @endif
                        </span>
                    </div>
                </div>
                @empty
                <div class="col-span-full text-center py-4 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-chair text-4xl mb-2 text-gray-300 dark:text-gray-600"></i>
                    <p>لا توجد طاولات متاحة</p>
                </div>
                @endforelse
            </div>

            <!-- سكريبت تصفية الطاولات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    const areaFilter = document.getElementById('tableAreaFilter');
                    const tables = document.querySelectorAll('.restaurant-table');

                    // تصفية حسب المنطقة
                    areaFilter.addEventListener('change', filterTables);

                    function filterTables() {
                        const areaValue = areaFilter.value;

                        tables.forEach(table => {
                            const tableArea = table.getAttribute('data-area');

                            if (areaValue === 'all' || tableArea === areaValue) {
                                table.style.display = '';
                            } else {
                                table.style.display = 'none';
                            }
                        });
                    }

                    // التفاعل مع الطاولات
                    tables.forEach(table => {
                        table.addEventListener('click', function() {
                            const tableId = this.getAttribute('data-table-id');
                            const tableStatus = this.classList.contains('table-available') ? 'available' :
                                              (this.classList.contains('table-occupied') ? 'occupied' : 'reserved');

                            // إظهار قائمة الإجراءات المتاحة للطاولة
                            showTableActions(tableId, tableStatus);
                        });
                    });

                    function showTableActions(tableId, status) {
                        let actions = '';

                        if (status === 'available') {
                            actions = `
                                <a href="{{ route('employee.reservations.create') }}?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-calendar-plus ml-2"></i>إنشاء حجز
                                </a>
                                <a href="{{ route('employee.orders.create') }}?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-utensils ml-2"></i>إنشاء طلب
                                </a>
                            `;
                        } else if (status === 'occupied') {
                            actions = `
                                <a href="{{ route('employee.tables.view-order') }}?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-eye ml-2"></i>عرض الطلب
                                </a>
                                <a href="{{ route('employee.tables.free') }}?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-check-circle ml-2"></i>تحرير الطاولة
                                </a>
                            `;
                        } else if (status === 'reserved') {
                            actions = `
                                <a href="{{ route('employee.reservations') }}?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-calendar-check ml-2"></i>عرض الحجز
                                </a>
                                <a href="{{ route('employee.tables.cancel-reservation') }}?table_id=${tableId}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                    <i class="fas fa-times-circle ml-2"></i>إلغاء الحجز
                                </a>
                            `;
                        }

                        // إنشاء قائمة منبثقة
                        const popup = document.createElement('div');
                        popup.className = 'fixed inset-0 z-50 flex items-center justify-center';
                        popup.innerHTML = `
                            <div class="fixed inset-0 bg-black bg-opacity-50" id="tableActionsOverlay"></div>
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl z-10 w-72 overflow-hidden">
                                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                                    <h3 class="font-bold text-gray-800 dark:text-white">إجراءات الطاولة #${tableId}</h3>
                                    <button id="closeTableActions" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="py-2">
                                    ${actions}
                                </div>
                            </div>
                        `;

                        document.body.appendChild(popup);

                        // إغلاق القائمة المنبثقة
                        document.getElementById('closeTableActions').addEventListener('click', function() {
                            document.body.removeChild(popup);
                        });

                        document.getElementById('tableActionsOverlay').addEventListener('click', function() {
                            document.body.removeChild(popup);
                        });
                    }
                });
            </script>
        </div>

        <!-- أحدث الإشعارات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">أحدث الإشعارات</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        <span class="text-primary">{{ $notifications->where('is_read', 0)->count() }}</span> غير مقروءة من أصل {{ $notifications->count() }}
                    </p>
                </div>
                <div class="flex items-center">
                    <button id="markAllAsRead" class="p-1.5 bg-primary/10 text-primary rounded-md hover:bg-primary/20 ml-2" title="تعليم الكل كمقروء">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <a href="{{ route('employee.notifications') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
                </div>
            </div>

            <!-- تصفية الإشعارات -->
            <div class="mb-4 flex">
                <button class="notification-filter px-3 py-1 text-xs rounded-full mr-2 bg-primary text-white" data-filter="all">الكل</button>
                <button class="notification-filter px-3 py-1 text-xs rounded-full mr-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" data-filter="unread">غير مقروءة</button>
                <button class="notification-filter px-3 py-1 text-xs rounded-full mr-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" data-filter="orders">الطلبات</button>
                <button class="notification-filter px-3 py-1 text-xs rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300" data-filter="system">النظام</button>
            </div>

            <div class="space-y-3 max-h-80 overflow-y-auto" id="notificationsContainer">
                @forelse($notifications as $notification)
                <div class="p-3 rounded-lg border-r-4 {{ $notification->is_read ? 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700/30' : 'border-primary bg-primary/5 dark:bg-primary/10' }} relative notification-item {{ $notification->is_read ? 'read' : 'unread' }} {{ $notification->type }}" data-notification-id="{{ $notification->notification_id }}">
                    <div class="flex justify-between">
                        <div class="flex items-start">
                            <div class="mt-0.5 ml-3">
                                @if($notification->type == 'order')
                                    <span class="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-500 flex items-center justify-center">
                                        <i class="fas fa-shopping-cart"></i>
                                    </span>
                                @elseif($notification->type == 'reservation')
                                    <span class="w-8 h-8 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-500 flex items-center justify-center">
                                        <i class="fas fa-calendar-check"></i>
                                    </span>
                                @elseif($notification->type == 'system')
                                    <span class="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-900/30 text-gray-500 flex items-center justify-center">
                                        <i class="fas fa-cog"></i>
                                    </span>
                                @else
                                    <span class="w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                                        <i class="fas fa-bell"></i>
                                    </span>
                                @endif
                            </div>
                            <div>
                                <p class="text-gray-700 dark:text-gray-300 {{ $notification->is_read ? '' : 'font-medium' }}">{{ $notification->message }}</p>
                                <div class="flex items-center mt-1">
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $notification->created_at->diffForHumans() }}</p>
                                    @if($notification->link)
                                        <a href="{{ $notification->link }}" class="text-xs text-primary hover:underline mr-3">عرض التفاصيل</a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div>
                            @if(!$notification->is_read)
                                <button class="mark-as-read p-1 text-gray-400 hover:text-primary" title="تعليم كمقروء" data-id="{{ $notification->notification_id }}">
                                    <i class="fas fa-check"></i>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
                @empty
                <div class="p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-center" id="emptyNotifications">
                    <i class="fas fa-bell-slash text-4xl text-gray-300 dark:text-gray-600 mb-2"></i>
                    <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات جديدة</p>
                </div>
                @endforelse
            </div>

            @if($notifications->count() > 0)
            <div class="mt-4 text-center">
                <a href="{{ route('employee.notifications') }}" class="text-primary hover:underline text-sm">عرض كل الإشعارات</a>
            </div>
            @endif

            <!-- سكريبت التفاعل مع الإشعارات -->
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // تصفية الإشعارات
                    const filterButtons = document.querySelectorAll('.notification-filter');
                    const notifications = document.querySelectorAll('.notification-item');
                    const emptyNotifications = document.getElementById('emptyNotifications');

                    filterButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            // تحديث حالة الأزرار
                            filterButtons.forEach(btn => {
                                btn.classList.remove('bg-primary', 'text-white');
                                btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
                            });

                            this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
                            this.classList.add('bg-primary', 'text-white');

                            const filter = this.getAttribute('data-filter');

                            // تصفية الإشعارات
                            let visibleCount = 0;

                            notifications.forEach(notification => {
                                if (filter === 'all' ||
                                    (filter === 'unread' && notification.classList.contains('unread')) ||
                                    (filter === 'orders' && notification.classList.contains('order')) ||
                                    (filter === 'system' && notification.classList.contains('system'))) {
                                    notification.style.display = '';
                                    visibleCount++;
                                } else {
                                    notification.style.display = 'none';
                                }
                            });

                            // إظهار رسالة إذا لم يتم العثور على إشعارات
                            if (visibleCount === 0 && !emptyNotifications) {
                                const container = document.getElementById('notificationsContainer');
                                const emptyMessage = document.createElement('div');
                                emptyMessage.id = 'emptyFilteredNotifications';
                                emptyMessage.className = 'p-4 rounded-lg border border-gray-200 dark:border-gray-700 text-center';
                                emptyMessage.innerHTML = `
                                    <i class="fas fa-filter text-4xl text-gray-300 dark:text-gray-600 mb-2"></i>
                                    <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات تطابق الفلتر المحدد</p>
                                `;
                                container.appendChild(emptyMessage);
                            } else if (visibleCount > 0) {
                                const emptyFiltered = document.getElementById('emptyFilteredNotifications');
                                if (emptyFiltered) {
                                    emptyFiltered.remove();
                                }
                            }
                        });
                    });

                    // تعليم الإشعار كمقروء
                    const markAsReadButtons = document.querySelectorAll('.mark-as-read');

                    markAsReadButtons.forEach(button => {
                        button.addEventListener('click', function() {
                            const notificationId = this.getAttribute('data-id');
                            const notification = document.querySelector(`.notification-item[data-notification-id="${notificationId}"]`);

                            // هنا يمكن إضافة طلب AJAX لتحديث حالة الإشعار في قاعدة البيانات

                            // تحديث واجهة المستخدم
                            notification.classList.remove('unread', 'border-primary', 'bg-primary/5', 'dark:bg-primary/10');
                            notification.classList.add('read', 'border-gray-300', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700/30');

                            // إزالة زر التعليم كمقروء
                            this.remove();

                            // تحديث عداد الإشعارات غير المقروءة
                            const unreadCount = document.querySelectorAll('.notification-item.unread').length;
                            const countElement = document.querySelector('.text-primary');
                            if (countElement) {
                                countElement.textContent = unreadCount;
                            }

                            // تحديث عداد الإشعارات في الهيدر
                            const headerCount = document.getElementById('notificationCount');
                            if (headerCount) {
                                headerCount.textContent = unreadCount;
                            }
                        });
                    });

                    // تعليم كل الإشعارات كمقروءة
                    const markAllAsReadButton = document.getElementById('markAllAsRead');

                    markAllAsReadButton.addEventListener('click', function() {
                        const unreadNotifications = document.querySelectorAll('.notification-item.unread');

                        unreadNotifications.forEach(notification => {
                            // هنا يمكن إضافة طلب AJAX لتحديث حالة الإشعار في قاعدة البيانات

                            // تحديث واجهة المستخدم
                            notification.classList.remove('unread', 'border-primary', 'bg-primary/5', 'dark:bg-primary/10');
                            notification.classList.add('read', 'border-gray-300', 'dark:border-gray-600', 'bg-gray-50', 'dark:bg-gray-700/30');

                            // إزالة زر التعليم كمقروء
                            const markButton = notification.querySelector('.mark-as-read');
                            if (markButton) {
                                markButton.remove();
                            }
                        });

                        // تحديث عداد الإشعارات غير المقروءة
                        const countElement = document.querySelector('.text-primary');
                        if (countElement) {
                            countElement.textContent = '0';
                        }

                        // تحديث عداد الإشعارات في الهيدر
                        const headerCount = document.getElementById('notificationCount');
                        if (headerCount) {
                            headerCount.textContent = '0';
                        }
                    });
                });
            </script>
        </div>
    </div>
</div>
@endsection