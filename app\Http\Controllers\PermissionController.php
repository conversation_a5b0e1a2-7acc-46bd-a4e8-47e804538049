<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Validator;

class PermissionController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('permission:users.permissions');
    }

    /**
     * عرض صفحة إدارة الصلاحيات
     */
    public function index()
    {
        $users = User::with('roles', 'permissions')->paginate(15);
        $roles = Role::with('permissions')->get();
        $permissions = Permission::all()->groupBy(function($permission) {
            return explode('.', $permission->name)[0];
        });

        return view('admin.permissions.index', compact('users', 'roles', 'permissions'));
    }

    /**
     * عرض صفحة تعديل صلاحيات مستخدم
     */
    public function editUser($userId)
    {
        $user = User::with('roles', 'permissions')->findOrFail($userId);
        $roles = Role::all();
        $permissions = Permission::all()->groupBy(function($permission) {
            return explode('.', $permission->name)[0];
        });

        return view('admin.permissions.edit-user', compact('user', 'roles', 'permissions'));
    }

    /**
     * تحديث صلاحيات مستخدم
     */
    public function updateUser(Request $request, $userId)
    {
        $user = User::findOrFail($userId);

        $validator = Validator::make($request->all(), [
            'roles' => 'array',
            'roles.*' => 'exists:roles,name',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // تحديث الأدوار
        if ($request->has('roles')) {
            $user->syncRoles($request->roles);
        } else {
            $user->syncRoles([]);
        }

        // تحديث الصلاحيات المباشرة
        if ($request->has('permissions')) {
            $user->syncPermissions($request->permissions);
        } else {
            $user->syncPermissions([]);
        }

        return redirect()->route('admin.permissions.index')
                        ->with('success', "تم تحديث صلاحيات {$user->first_name} {$user->last_name} بنجاح");
    }

    /**
     * عرض صفحة إدارة الأدوار
     */
    public function roles()
    {
        $roles = Role::with('permissions')->get();
        $permissions = Permission::all()->groupBy(function($permission) {
            return explode('.', $permission->name)[0];
        });

        return view('admin.permissions.roles', compact('roles', 'permissions'));
    }

    /**
     * إنشاء دور جديد
     */
    public function createRole(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:roles,name',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ], [
            'name.required' => 'اسم الدور مطلوب',
            'name.unique' => 'اسم الدور موجود بالفعل',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $role = Role::create(['name' => $request->name]);

        if ($request->has('permissions')) {
            $role->givePermissionTo($request->permissions);
        }

        return redirect()->route('admin.permissions.roles')
                        ->with('success', "تم إنشاء الدور '{$request->name}' بنجاح");
    }

    /**
     * تحديث دور
     */
    public function updateRole(Request $request, $roleId)
    {
        $role = Role::findOrFail($roleId);

        $validator = Validator::make($request->all(), [
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        } else {
            $role->syncPermissions([]);
        }

        return redirect()->route('admin.permissions.roles')
                        ->with('success', "تم تحديث صلاحيات الدور '{$role->name}' بنجاح");
    }

    /**
     * حذف دور
     */
    public function deleteRole($roleId)
    {
        $role = Role::findOrFail($roleId);

        // منع حذف الأدوار الأساسية
        if (in_array($role->name, ['admin', 'employee', 'manager'])) {
            return redirect()->back()->with('error', 'لا يمكن حذف الأدوار الأساسية');
        }

        $role->delete();

        return redirect()->route('admin.permissions.roles')
                        ->with('success', "تم حذف الدور '{$role->name}' بنجاح");
    }

    /**
     * نسخ صلاحيات من مستخدم لآخر
     */
    public function copyPermissions(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'from_user' => 'required|exists:users,user_id',
            'to_user' => 'required|exists:users,user_id|different:from_user',
        ], [
            'from_user.required' => 'يجب اختيار المستخدم المصدر',
            'to_user.required' => 'يجب اختيار المستخدم الهدف',
            'to_user.different' => 'لا يمكن نسخ الصلاحيات لنفس المستخدم',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $fromUser = User::findOrFail($request->from_user);
        $toUser = User::findOrFail($request->to_user);

        // نسخ الأدوار
        $toUser->syncRoles($fromUser->getRoleNames());

        // نسخ الصلاحيات المباشرة
        $toUser->syncPermissions($fromUser->getDirectPermissions()->pluck('name'));

        return redirect()->route('admin.permissions.index')
                        ->with('success', "تم نسخ صلاحيات {$fromUser->first_name} إلى {$toUser->first_name} بنجاح");
    }
}
