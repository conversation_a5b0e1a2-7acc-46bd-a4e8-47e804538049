<?php

namespace App\Http\Controllers;

use App\Models\Review;
use App\Models\Order;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;

class ReviewController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // Admin methods
    public function adminIndex()
    {
        $this->middleware('admin');

        $reviews = Review::with(['user', 'order'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.reviews.index', compact('reviews'));
    }

    // Employee methods
    public function employeeIndex()
    {
        $this->middleware('employee');

        $reviews = Review::with(['user', 'order'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('employee.reviews.index', compact('reviews'));
    }

    // Customer methods
    public function customerIndex()
    {
        $reviews = Review::where('user_id', Auth::id())
            ->with('order')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('customer.reviews.index', compact('reviews'));
    }

    public function create($order_id)
    {
        $order = Order::with('items.menuItem')
            ->where('user_id', Auth::id())
            ->where('status', 'completed')
            ->findOrFail($order_id);

        // Check if a review already exists for this order
        $existingReview = Review::where('order_id', $order_id)->first();

        if ($existingReview) {
            return redirect()->route('customer.reviews')->with('error', 'لقد قمت بالفعل بتقييم هذا الطلب');
        }

        return view('customer.reviews.create', compact('order'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,order_id',
            'menu_item_id' => 'nullable|exists:menu_items,item_id',
            'rating' => 'required|integer|between:1,5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Check if user owns the order
        $order = Order::where('user_id', Auth::id())
            ->where('order_id', $request->order_id)
            ->where('status', 'completed')
            ->first();

        if (!$order) {
            return redirect()->route('customer.reviews')->with('error', 'لا يمكنك تقييم هذا الطلب');
        }

        // Check if a review already exists for this order and menu item
        $query = Review::where('order_id', $request->order_id);

        if ($request->has('menu_item_id')) {
            $query->where('menu_item_id', $request->menu_item_id);
        } else {
            $query->whereNull('menu_item_id');
        }

        $existingReview = $query->first();

        if ($existingReview) {
            return redirect()->route('customer.reviews')->with('error', 'لقد قمت بالفعل بتقييم هذا العنصر في الطلب');
        }

        DB::beginTransaction();

        try {
            // Create review
            $review = Review::create([
                'user_id' => Auth::id(),
                'order_id' => $request->order_id,
                'menu_item_id' => $request->menu_item_id,
                'rating' => $request->rating,
                'comment' => $request->comment,
            ]);

            // Notify admins about the new review
            $this->notifyAdminsAboutNewReview($review);

            DB::commit();

            return redirect()->route('customer.reviews')->with('success', 'شكراً لك على التقييم');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إرسال التقييم: ' . $e->getMessage())->withInput();
        }
    }

    // Edit review (for customer)
    public function edit($id)
    {
        $review = Review::with('order.items.menuItem')
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        // Check if the review is older than 7 days
        $reviewDate = new \DateTime($review->created_at);
        $now = new \DateTime();
        $interval = $reviewDate->diff($now);

        if ($interval->days > 7) {
            return redirect()->route('customer.reviews')->with('error', 'لا يمكن تعديل التقييم بعد 7 أيام من إنشائه');
        }

        return view('customer.reviews.edit', compact('review'));
    }

    public function update(Request $request, $id)
    {
        $review = Review::where('user_id', Auth::id())->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|between:1,5',
            'comment' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Check if the review is older than 7 days
        $reviewDate = new \DateTime($review->created_at);
        $now = new \DateTime();
        $interval = $reviewDate->diff($now);

        if ($interval->days > 7) {
            return redirect()->route('customer.reviews')->with('error', 'لا يمكن تعديل التقييم بعد 7 أيام من إنشائه');
        }

        $review->update([
            'rating' => $request->rating,
            'comment' => $request->comment,
        ]);

        return redirect()->route('customer.reviews')->with('success', 'تم تحديث التقييم بنجاح');
    }

    // Delete review (for customer)
    public function delete($id)
    {
        $review = Review::where('user_id', Auth::id())->findOrFail($id);

        // Check if the review is older than 7 days
        $reviewDate = new \DateTime($review->created_at);
        $now = new \DateTime();
        $interval = $reviewDate->diff($now);

        if ($interval->days > 7) {
            return redirect()->route('customer.reviews')->with('error', 'لا يمكن حذف التقييم بعد 7 أيام من إنشائه');
        }

        $review->delete();

        return redirect()->route('customer.reviews')->with('success', 'تم حذف التقييم بنجاح');
    }

    // Admin can respond to reviews
    public function respond(Request $request, $id)
    {
        $this->middleware('admin');

        $review = Review::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'response' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $review->update([
            'admin_response' => $request->response,
            'response_date' => now(),
        ]);

        // Notify the customer about the response
        Notification::create([
            'user_id' => $review->user_id,
            'message' => 'تم الرد على تقييمك للطلب رقم #' . $review->order_id,
            'is_read' => false,
        ]);

        return redirect()->route('admin.reviews')->with('success', 'تم إضافة الرد بنجاح');
    }

    // Helper methods
    private function notifyAdminsAboutNewReview($review)
    {
        // Find admin users
        $admins = DB::table('users')
            ->where('user_type', 'admin')
            ->where('is_active', true)
            ->get();

        $userName = Auth::user()->first_name . ' ' . Auth::user()->last_name;

        // Get menu item name if available
        $menuItemInfo = '';
        if ($review->menu_item_id) {
            $menuItem = DB::table('menu_items')->where('item_id', $review->menu_item_id)->first();
            if ($menuItem) {
                $menuItemInfo = ' للمنتج "' . $menuItem->name . '"';
            }
        }

        foreach ($admins as $admin) {
            Notification::create([
                'user_id' => $admin->user_id,
                'message' => 'تقييم جديد من ' . $userName . ' للطلب رقم #' . $review->order_id . $menuItemInfo . ' بتقييم ' . $review->rating . ' نجوم',
                'is_read' => false,
            ]);
        }
    }
}