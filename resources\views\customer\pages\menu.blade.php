<!-- صفحة قائمة الطعام -->
<div id="menu-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">قائمة الطعام</h1>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">اكتشف تشكيلتنا الواسعة من الأطباق الشهية المحضرة بعناية فائقة</p>
        </div>

        <!-- فلاتر القائمة -->
        <div class="flex flex-wrap justify-center gap-4 mb-8">
            <button class="menu-filter-btn active px-6 py-2 rounded-full bg-primary text-white" data-category="all">
                جميع الأطباق
            </button>
            <button class="menu-filter-btn px-6 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition" data-category="main">
                الأطباق الرئيسية
            </button>
            <button class="menu-filter-btn px-6 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition" data-category="appetizer">
                المقبلات
            </button>
            <button class="menu-filter-btn px-6 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition" data-category="dessert">
                الحلويات
            </button>
            <button class="menu-filter-btn px-6 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition" data-category="beverage">
                المشروبات
            </button>
        </div>

        <!-- عناصر القائمة -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" id="menu-items-container">
            <!-- الأطباق الرئيسية -->
            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden" data-category="main">
                <div class="h-48 overflow-hidden relative">
                    <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="برجر لحم أنجوس" class="menu-item-image w-full h-full object-cover">
                    <div class="absolute top-4 right-4 bg-white dark:bg-gray-900 text-primary rounded-full py-1 px-3 text-sm font-semibold">
                        الأكثر مبيعاً
                    </div>
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">برجر لحم أنجوس</h3>
                        <span class="font-bold text-primary">55 د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">برجر لحم بقري فاخر مع صلصة خاصة وجبنة شيدر وخضار طازجة</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span class="text-gray-600 dark:text-gray-400 mr-1">4.8</span>
                        </div>
                        <button class="btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>

            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden" data-category="main">
                <div class="h-48 overflow-hidden relative">
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="بيتزا سوبريم" class="menu-item-image w-full h-full object-cover">
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">بيتزا سوبريم</h3>
                        <span class="font-bold text-primary">65 د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">بيتزا مع صلصة طماطم، جبنة موزاريلا، فلفل، زيتون، بصل، فطر وبيبروني</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="far fa-star"></i>
                            <span class="text-gray-600 dark:text-gray-400 mr-1">4.2</span>
                        </div>
                        <button class="btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>

            <!-- المقبلات -->
            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden" data-category="appetizer">
                <div class="h-48 overflow-hidden relative">
                    <img src="https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="سلطة سيزر" class="menu-item-image w-full h-full object-cover">
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">سلطة سيزر بالدجاج</h3>
                        <span class="font-bold text-primary">45 د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">خس روماني، صدر دجاج مشوي، جبنة بارميزان، خبز محمص مع صلصة سيزر</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span class="text-gray-600 dark:text-gray-400 mr-1">4.9</span>
                        </div>
                        <button class="btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>

            <!-- الحلويات -->
            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden" data-category="dessert">
                <div class="h-48 overflow-hidden relative">
                    <img src="https://images.unsplash.com/photo-1551024506-0bccd828d307?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="تشيز كيك" class="menu-item-image w-full h-full object-cover">
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">تشيز كيك بالفراولة</h3>
                        <span class="font-bold text-primary">35 د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">تشيز كيك كريمي مع صلصة الفراولة الطازجة وقطع الفراولة</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span class="text-gray-600 dark:text-gray-400 mr-1">4.6</span>
                        </div>
                        <button class="btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>

            <!-- المشروبات -->
            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden" data-category="beverage">
                <div class="h-48 overflow-hidden relative">
                    <img src="https://images.unsplash.com/photo-1461023058943-07fcbe16d735?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="عصير طازج" class="menu-item-image w-full h-full object-cover">
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">عصير برتقال طازج</h3>
                        <span class="font-bold text-primary">20 د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">عصير برتقال طبيعي 100% معصور طازجاً بدون إضافات</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <span class="text-gray-600 dark:text-gray-400 mr-1">5.0</span>
                        </div>
                        <button class="btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                    </div>
                </div>
            </div>

            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden" data-category="beverage">
                <div class="h-48 overflow-hidden relative">
                    <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80" alt="قهوة" class="menu-item-image w-full h-full object-cover">
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">قهوة عربية مميزة</h3>
                        <span class="font-bold text-primary">25 د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">قهوة عربية أصيلة محمصة بعناية مع الهيل والزعفران</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star-half-alt"></i>
                            <span class="text-gray-600 dark:text-gray-400 mr-1">4.7</span>
                        </div>
                        @auth
                        <button class="add-to-cart btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition"
                                onclick="addToCart({{ $loop->index + 1 }}, 'برجر لحم الخروف', 35.00)"
                                data-item-id="{{ $loop->index + 1 }}">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                        @else
                        <a href="{{ route('login') }}" class="bg-gray-400 hover:bg-gray-500 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-sign-in-alt ml-1"></i>سجل دخول للطلب
                        </a>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// فلترة عناصر القائمة
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.menu-filter-btn');
    const menuItems = document.querySelectorAll('.menu-item-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');

            // تحديث حالة الأزرار
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-primary', 'text-white');
                btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            });

            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');

            // فلترة العناصر
            menuItems.forEach(item => {
                if (category === 'all' || item.getAttribute('data-category') === category) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
});

// إضافة للسلة
function addToCart(itemId, itemName, itemPrice) {
    const button = document.querySelector(`[data-item-id="${itemId}"]`);
    const originalText = button.innerHTML;

    // تغيير النص أثناء التحميل
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-1"></i>جاري الإضافة...';
    button.disabled = true;

    fetch('/customer/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            menu_item_id: itemId,
            quantity: 1
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // تحديث عداد السلة في الـ header
            updateCartCount(data.cart_count);

            // تغيير النص مؤقتاً
            button.innerHTML = '<i class="fas fa-check ml-1"></i>تم الإضافة!';
            button.classList.remove('bg-primary', 'hover:bg-primary/90');
            button.classList.add('bg-green-500');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-500');
                button.classList.add('bg-primary', 'hover:bg-primary/90');
                button.disabled = false;
            }, 2000);

            showNotification(data.message, 'success');
        } else {
            button.innerHTML = originalText;
            button.disabled = false;
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalText;
        button.disabled = false;
        showNotification('حدث خطأ أثناء إضافة العنصر للسلة', 'error');
    });
}

// تحديث عداد السلة
function updateCartCount(count) {
    const cartCountElement = document.querySelector('.cart-count');
    if (cartCountElement) {
        cartCountElement.textContent = count;
        if (count > 0) {
            cartCountElement.style.display = 'inline';
        }
    }
}

// إظهار الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    if (type === 'success') {
        notification.classList.add('bg-green-500', 'text-white');
    } else if (type === 'error') {
        notification.classList.add('bg-red-500', 'text-white');
    } else {
        notification.classList.add('bg-blue-500', 'text-white');
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إظهار الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>
