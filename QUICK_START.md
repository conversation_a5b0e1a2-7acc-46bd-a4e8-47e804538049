# 🚀 البدء السريع - Eat Hub Restaurant System

## للمستعجلين - 5 دقائق فقط!

### الطريقة الأسرع (Windows):
```bash
# 1. افتح Command Prompt في مجلد المشروع
# 2. شغل الملف التلقائي
setup.bat
```

### الطريقة الأسرع (Linux/Mac):
```bash
# 1. افتح Terminal في مجلد المشروع
# 2. اجعل الملف قابل للتنفيذ وشغله
chmod +x setup.sh
./setup.sh
```

---

## الطريقة اليدوية (خطوة بخطوة):

### 1️⃣ تثبيت التبعيات
```bash
composer install
npm install
```

### 2️⃣ إعداد البيئة
```bash
# Windows
copy .env.example .env

# Linux/Mac
cp .env.example .env
```

### 3️⃣ إنشاء قاعدة البيانات
```sql
-- في MySQL
CREATE DATABASE eat_hub_new CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4️⃣ تعديل ملف .env
```env
DB_DATABASE=eat_hub_new
DB_USERNAME=root
DB_PASSWORD=your_password_here
```

### 5️⃣ إعداد Laravel
```bash
php artisan key:generate
php artisan migrate
php artisan db:seed
php artisan storage:link
npm run build
```

### 6️⃣ تشغيل الخادم
```bash
php artisan serve
```

---

## 🔑 بيانات تسجيل الدخول الافتراضية

**المدير:**
- البريد: `<EMAIL>`
- كلمة المرور: `A178a2002`

---

## 🆘 حل المشاكل الشائعة

### مشكلة قاعدة البيانات:
```bash
# تأكد من تشغيل MySQL
# تحقق من بيانات الاتصال في .env
php artisan tinker
DB::connection()->getPdo();
```

### مشكلة الصلاحيات (Linux/Mac):
```bash
chmod -R 775 storage bootstrap/cache
```

### مشكلة الذاكرة المؤقتة:
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

---

## 📱 الوصول للنظام

بعد التشغيل، افتح المتصفح على:
**http://localhost:8000**

---

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من ملفات السجل في `storage/logs/`
2. تأكد من تشغيل MySQL
3. راجع إعدادات `.env`

---

**نصيحة:** استخدم `setup.bat` أو `setup.sh` لإعداد تلقائي سريع! 🎯
