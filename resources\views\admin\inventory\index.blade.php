@extends('layouts.admin')

@section('title', 'إدارة المخزون')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة المخزون</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <a href="{{ route('admin.inventory.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مخزون</span>
        </a>
        <a href="{{ route('admin.ingredients.create') }}" class="bg-secondary hover:bg-secondary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مكون</span>
        </a>
        <a href="{{ route('admin.inventory.export') }}" class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-download ml-2"></i>
            <span>تصدير CSV</span>
        </a>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- إجمالي المكونات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                <i class="fas fa-boxes text-blue-600 dark:text-blue-300 text-xl"></i>
            </div>
            <div class="mr-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المكونات</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $totalIngredients }}</p>
            </div>
        </div>
    </div>

    <!-- قيمة المخزون -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <i class="fas fa-dollar-sign text-green-600 dark:text-green-300 text-xl"></i>
            </div>
            <div class="mr-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">قيمة المخزون</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalInventoryValue, 2) }} د.ل</p>
            </div>
        </div>
    </div>

    <!-- المخزون المنخفض -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-300 text-xl"></i>
            </div>
            <div class="mr-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">مخزون منخفض</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $lowStockCount }}</p>
            </div>
        </div>
    </div>

    <!-- قريب الانتهاء -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
                <i class="fas fa-clock text-red-600 dark:text-red-300 text-xl"></i>
            </div>
            <div class="mr-4">
                <p class="text-sm font-medium text-gray-600 dark:text-gray-400">قريب الانتهاء</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $nearExpiryCount }}</p>
            </div>
        </div>
    </div>
</div>

<!-- رسم بياني سريع لحالة المخزون -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">توزيع حالة المخزون</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <span class="text-2xl font-bold text-green-600 dark:text-green-300">{{ $totalIngredients - $lowStockCount - $outOfStockCount }}</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">متوفر</p>
        </div>
        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                <span class="text-2xl font-bold text-yellow-600 dark:text-yellow-300">{{ $lowStockCount }}</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">مخزون منخفض</p>
        </div>
        <div class="text-center">
            <div class="w-16 h-16 mx-auto mb-2 rounded-full bg-red-100 dark:bg-red-900 flex items-center justify-center">
                <span class="text-2xl font-bold text-red-600 dark:text-red-300">{{ $outOfStockCount }}</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400">نفد المخزون</p>
        </div>
    </div>
</div>

<!-- شريط البحث والفلاتر -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
    <form action="{{ route('admin.inventory') }}" method="GET" class="flex flex-col lg:flex-row gap-4">
        <!-- البحث -->
        <div class="flex-1">
            <div class="relative">
                <input type="text" name="search" value="{{ request('search') }}" placeholder="البحث عن مكون..." class="w-full px-4 py-2 pr-10 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- فلتر الحالة -->
        <div class="lg:w-48">
            <select name="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                <option value="">جميع الحالات</option>
                <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>نشط</option>
                <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>غير نشط</option>
            </select>
        </div>

        <!-- فلتر مستوى المخزون -->
        <div class="lg:w-48">
            <select name="stock_level" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                <option value="">جميع المستويات</option>
                <option value="low" {{ request('stock_level') === 'low' ? 'selected' : '' }}>مخزون منخفض</option>
                <option value="out" {{ request('stock_level') === 'out' ? 'selected' : '' }}>نفد المخزون</option>
            </select>
        </div>

        <!-- أزرار -->
        <div class="flex gap-2">
            <button type="submit" class="px-6 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-all">
                <i class="fas fa-search ml-1"></i>
                بحث
            </button>
            <a href="{{ route('admin.inventory') }}" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-all">
                <i class="fas fa-times ml-1"></i>
                إعادة تعيين
            </a>
        </div>
    </form>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">المكونات والمخزون</h3>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.inventory.transactions') }}" class="text-primary hover:text-primary/80 transition-all">
                <i class="fas fa-exchange-alt ml-1"></i>
                <span>سجل المعاملات</span>
            </a>
            <a href="{{ route('admin.inventory.low-stock') }}" class="text-red-500 hover:text-red-600 transition-all">
                <i class="fas fa-exclamation-triangle ml-1"></i>
                <span>المخزون المنخفض</span>
            </a>
        </div>
    </div>

    @if($ingredients->isEmpty())
        <div class="p-6 text-center">
            <p class="text-gray-500 dark:text-gray-400">لا توجد مكونات مسجلة في النظام.</p>
            <a href="{{ route('admin.ingredients.create') }}" class="mt-4 inline-block bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة مكون جديد</span>
            </a>
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المكون</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الوحدة</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية المتوفرة</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الانتهاء</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($ingredients as $ingredient)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ $ingredient->name }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500 dark:text-gray-300">{{ $ingredient->unit }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                @php
                                    $totalQuantity = $ingredient->inventory->sum('quantity');
                                    $stockLevel = '';
                                    $stockColor = '';

                                    if ($totalQuantity == 0) {
                                        $stockLevel = 'نفد المخزون';
                                        $stockColor = 'text-red-600 dark:text-red-400';
                                    } elseif ($totalQuantity < 10) {
                                        $stockLevel = 'مخزون منخفض';
                                        $stockColor = 'text-yellow-600 dark:text-yellow-400';
                                    } else {
                                        $stockLevel = 'متوفر';
                                        $stockColor = 'text-green-600 dark:text-green-400';
                                    }
                                @endphp
                                <div class="flex items-center">
                                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ number_format($totalQuantity, 2) }} {{ $ingredient->unit }}
                                    </span>
                                    <span class="mr-2 text-xs {{ $stockColor }} font-medium">
                                        ({{ $stockLevel }})
                                    </span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                @if($ingredient->inventory->isNotEmpty() && $ingredient->inventory->first()->expiry_date)
                                    @php
                                        $nearestExpiry = $ingredient->inventory->sortBy('expiry_date')->first()->expiry_date;
                                        $daysUntilExpiry = now()->diffInDays($nearestExpiry, false);
                                        $expiryClass = '';
                                        $expiryIcon = '';

                                        if ($daysUntilExpiry < 0) {
                                            $expiryClass = 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
                                            $expiryIcon = 'fas fa-exclamation-circle';
                                        } elseif ($daysUntilExpiry <= 7) {
                                            $expiryClass = 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20';
                                            $expiryIcon = 'fas fa-exclamation-triangle';
                                        } elseif ($daysUntilExpiry <= 30) {
                                            $expiryClass = 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
                                            $expiryIcon = 'fas fa-clock';
                                        } else {
                                            $expiryClass = 'text-green-600 dark:text-green-400';
                                            $expiryIcon = 'fas fa-check-circle';
                                        }
                                    @endphp
                                    <div class="flex items-center">
                                        <i class="{{ $expiryIcon }} text-xs ml-1"></i>
                                        <span class="text-sm {{ $expiryClass }} px-2 py-1 rounded-md font-medium">
                                            {{ $nearestExpiry->format('Y-m-d') }}
                                        </span>
                                    </div>
                                @else
                                    <span class="text-sm text-gray-400 dark:text-gray-500">-</span>
                                @endif
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $ingredient->is_active ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100' }}">
                                    {{ $ingredient->is_active ? 'نشط' : 'غير نشط' }}
                                </span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    <a href="{{ route('admin.inventory.create', ['ingredient_id' => $ingredient->ingredient_id]) }}" class="text-primary hover:text-primary/80 transition-all">
                                        <i class="fas fa-plus-circle"></i>
                                    </a>
                                    <a href="{{ route('admin.ingredients.edit', $ingredient->ingredient_id) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="deleteIngredient({{ $ingredient->ingredient_id }})" class="text-red-500 hover:text-red-700 transition-all">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @if($ingredient->inventory->isNotEmpty())
                            <tr class="bg-gray-50 dark:bg-gray-700">
                                <td colspan="6" class="px-4 py-2">
                                    <div class="text-sm text-gray-700 dark:text-gray-300 font-semibold mb-2">تفاصيل المخزون:</div>
                                    <div class="overflow-x-auto">
                                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                            <thead>
                                                <tr>
                                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية</th>
                                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التكلفة للوحدة</th>
                                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الانتهاء</th>
                                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">تاريخ الإضافة</th>
                                                    <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody class="divide-y divide-gray-200 dark:divide-gray-600">
                                                @foreach($ingredient->inventory as $inventory)
                                                    <tr>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                                            {{ number_format($inventory->quantity, 2) }} {{ $ingredient->unit }}
                                                        </td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                                            {{ number_format($inventory->cost_per_unit, 2) }} د.ل
                                                        </td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                                            {{ $inventory->expiry_date ? $inventory->expiry_date->format('Y-m-d') : '-' }}
                                                        </td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                                            {{ $inventory->created_at->format('Y-m-d') }}
                                                        </td>
                                                        <td class="px-3 py-2 whitespace-nowrap text-sm font-medium">
                                                            <div class="flex items-center space-x-2 space-x-reverse">
                                                                <a href="{{ route('admin.inventory.edit', $inventory->inventory_id) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                                                    <i class="fas fa-edit"></i>
                                                                </a>
                                                                <button onclick="deleteInventory({{ $inventory->inventory_id }})" class="text-red-500 hover:text-red-700 transition-all">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        @endif
                    @endforeach
                </tbody>
            </table>
        </div>
        <div class="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
            {{ $ingredients->links() }}
        </div>
    @endif
</div>

<!-- Modal for deleting ingredient -->
<div id="deleteIngredientModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-700 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المكون؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDeleteIngredient" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">إلغاء</button>
            <form id="deleteIngredientForm" method="POST">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-all">حذف</button>
            </form>
        </div>
    </div>
</div>

<!-- Modal for deleting inventory -->
<div id="deleteInventoryModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full p-6">
        <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-700 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المخزون؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDeleteInventory" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">إلغاء</button>
            <form id="deleteInventoryForm" method="POST">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-all">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function deleteIngredient(id) {
        const deleteModal = document.getElementById('deleteIngredientModal');
        const deleteForm = document.getElementById('deleteIngredientForm');
        const cancelDelete = document.getElementById('cancelDeleteIngredient');

        deleteForm.action = "{{ route('admin.ingredients.delete', '') }}/" + id;
        deleteModal.classList.remove('hidden');

        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    function deleteInventory(id) {
        const deleteModal = document.getElementById('deleteInventoryModal');
        const deleteForm = document.getElementById('deleteInventoryForm');
        const cancelDelete = document.getElementById('cancelDeleteInventory');

        deleteForm.action = "{{ route('admin.inventory.delete', '') }}/" + id;
        deleteModal.classList.remove('hidden');

        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    // تم نقل وظيفة التصدير إلى الخادم للحصول على دعم أفضل للغة العربية

    // إضافة تأثيرات بصرية للبطاقات
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.bg-white.dark\\:bg-gray-800.rounded-lg.shadow-md.p-6');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
                this.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '';
            });
        });

        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            // يمكن إضافة AJAX call هنا لتحديث الإحصائيات
        }, 30000);
    });
</script>

<!-- تحسينات إضافية -->
<script>
    // إضافة تأثيرات للصفوف عند التمرير
    document.addEventListener('DOMContentLoaded', function() {
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(59, 130, 246, 0.05)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
        });
    });
</script>
@endsection
