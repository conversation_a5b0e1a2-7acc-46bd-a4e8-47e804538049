@extends('admin.layouts.app')

@section('content')
<!-- Hero Section -->
<div class="relative mb-8 overflow-hidden">
    <div class="bg-white dark:bg-gray-800 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-black text-gray-800 dark:text-white mb-3">
                    إدارة <span class="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">الطلبات</span>
                    <span class="inline-block animate-bounce">🛒</span>
                </h1>
                <p class="text-gray-600 dark:text-gray-400 text-lg font-medium">
                    متابعة ومعالجة جميع طلبات العملاء
                </p>
            </div>
            <div class="hidden md:block">
                <div class="w-32 h-32 rounded-full bg-gradient-to-br from-orange-400 via-red-500 to-pink-600 flex items-center justify-center animate-float shadow-2xl">
                    <i class="fas fa-shopping-cart text-white text-4xl"></i>
                </div>
            </div>
        </div>
        
        <!-- أزرار الإجراءات السريعة -->
        <div class="flex flex-wrap gap-3 mt-6">
            @can('orders.create')
            <a href="{{ route('admin.orders.create') }}"
               class="px-6 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-2xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-plus ml-2"></i>
                طلب جديد
            </a>
            @endcan
            
            @can('orders.export')
            <a href="{{ route('admin.orders.export') }}"
               class="px-6 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-2xl hover:from-blue-600 hover:to-cyan-600 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-download ml-2"></i>
                تصدير البيانات
            </a>
            @endcan
        </div>
    </div>
</div>

<!-- بطاقات الإحصائيات -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
    <!-- إجمالي الطلبات -->
    <div class="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 dark:from-blue-900/30 dark:via-indigo-900/30 dark:to-purple-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-blue-200/60 dark:border-blue-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">إجمالي الطلبات</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $orderStats['total'] ?? 0 }}</h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">جميع الطلبات</p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-shopping-cart text-3xl text-blue-600 dark:text-blue-400"></i>
            </div>
        </div>
    </div>

    <!-- طلبات قيد الانتظار -->
    <div class="relative bg-gradient-to-br from-yellow-50 via-amber-50 to-orange-100 dark:from-yellow-900/30 dark:via-amber-900/30 dark:to-orange-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-yellow-200/60 dark:border-yellow-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-yellow-400 via-amber-500 to-orange-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-yellow-400/20 to-amber-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">قيد الانتظار</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $orderStats['pending'] ?? 0 }}</h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">يحتاج معالجة</p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-clock text-3xl text-yellow-600 dark:text-yellow-400"></i>
            </div>
        </div>
    </div>

    <!-- طلبات قيد التحضير -->
    <div class="relative bg-gradient-to-br from-orange-50 via-red-50 to-pink-100 dark:from-orange-900/30 dark:via-red-900/30 dark:to-pink-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-orange-200/60 dark:border-orange-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-400 via-red-500 to-pink-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-orange-400/20 to-red-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">قيد التحضير</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $orderStats['preparing'] ?? 0 }}</h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-orange-400 to-red-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">في المطبخ</p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-fire text-3xl text-orange-600 dark:text-orange-400"></i>
            </div>
        </div>
    </div>

    <!-- طلبات مكتملة -->
    <div class="relative bg-gradient-to-br from-green-50 via-emerald-50 to-teal-100 dark:from-green-900/30 dark:via-emerald-900/30 dark:to-teal-900/40 rounded-3xl shadow-2xl hover:shadow-3xl p-8 card-hover overflow-hidden border-2 border-green-200/60 dark:border-green-700/60 group transform hover:-translate-y-2 transition-all duration-500">
        <div class="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-green-400 via-emerald-500 to-teal-600 rounded-t-3xl"></div>
        <div class="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-green-400/20 to-emerald-500/20 rounded-full blur-xl"></div>

        <div class="flex justify-between items-start relative z-10">
            <div class="flex-1">
                <p class="text-gray-800 dark:text-white text-lg font-bold mb-3 tracking-wide">مكتملة</p>
                <h3 class="text-5xl font-black text-gray-900 dark:text-white mb-4 leading-tight">{{ $orderStats['completed'] ?? 0 }}</h3>
                <div class="flex items-center space-x-3 space-x-reverse bg-white/80 dark:bg-gray-800/80 rounded-2xl p-3 backdrop-blur-sm border border-gray-200 dark:border-gray-600">
                    <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full animate-pulse shadow-lg"></div>
                    <p class="text-gray-800 dark:text-white text-base font-bold">تم التسليم</p>
                </div>
            </div>
            <div class="bg-white/20 dark:bg-gray-800/20 rounded-2xl p-4 backdrop-blur-sm border border-white/30 dark:border-gray-600/30 group-hover:scale-110 transition-transform duration-300">
                <i class="fas fa-check-circle text-3xl text-green-600 dark:text-green-400"></i>
            </div>
        </div>
    </div>
</div>

<!-- البحث والفلترة -->
<div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 mb-8">
    <form method="GET" action="{{ route('admin.orders.index') }}" class="flex flex-wrap gap-4 items-end">
        <div class="flex-1 min-w-64">
            <label class="block text-sm font-bold text-gray-700 dark:text-gray-300 mb-2">البحث</label>
            <div class="relative">
                <input type="text" name="search" value="{{ request('search') }}" 
                       placeholder="البحث برقم الطلب أو اسم العميل..."
                       class="w-full px-4 py-3 pr-12 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-primary focus:border-transparent text-gray-800 dark:text-white">
                <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                    <i class="fas fa-search text-gray-400"></i>
                </div>
            </div>
        </div>
        
        <div class="min-w-48">
            <label class="block text-sm font-bold text-gray-700 dark:text-gray-300 mb-2">الحالة</label>
            <select name="status" class="w-full px-4 py-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl focus:ring-2 focus:ring-primary focus:border-transparent text-gray-800 dark:text-white">
                <option value="">جميع الحالات</option>
                <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد الانتظار</option>
                <option value="preparing" {{ request('status') == 'preparing' ? 'selected' : '' }}>قيد التحضير</option>
                <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                <option value="canceled" {{ request('status') == 'canceled' ? 'selected' : '' }}>ملغي</option>
            </select>
        </div>
        
        <div class="flex gap-3">
            <button type="submit" 
                    class="px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-2xl hover:from-primary/90 hover:to-secondary/90 transition-all duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-search ml-2"></i>
                بحث
            </button>
            
            <a href="{{ route('admin.orders.index') }}" 
               class="px-6 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-2xl hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-300 flex items-center">
                <i class="fas fa-times ml-2"></i>
                مسح
            </a>
        </div>
    </form>
</div>

<!-- جدول الطلبات -->
<div class="bg-white dark:bg-gray-800 rounded-3xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-2xl font-bold text-gray-800 dark:text-white flex items-center">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3">
                <i class="fas fa-list text-white text-sm"></i>
            </div>
            قائمة الطلبات
        </h3>
        <p class="text-gray-600 dark:text-gray-400 mt-1">جميع طلبات العملاء مع إمكانية الإدارة الكاملة</p>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700/50">
                <tr>
                    <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">رقم الطلب</th>
                    <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                    <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                    <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                    <th class="px-6 py-4 text-right text-sm font-bold text-gray-700 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($orders ?? [] as $order)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3">
                                <span class="text-white font-bold text-sm">#{{ $order->order_id ?? 0 }}</span>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ $order->user->first_name ?? 'عميل' }} {{ $order->user->last_name ?? '' }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            {{ $order->user->email ?? 'غير محدد' }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-lg font-bold text-gray-900 dark:text-white">
                            {{ number_format($order->total_amount ?? 0, 2) }} د.ل
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-3 py-1 text-sm font-bold rounded-full
                            @if($order->status == 'completed') bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300
                            @elseif($order->status == 'preparing') bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300
                            @elseif($order->status == 'pending') bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300
                            @else bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 @endif">
                            @if($order->status == 'completed') مكتمل
                            @elseif($order->status == 'preparing') قيد التحضير
                            @elseif($order->status == 'pending') قيد الانتظار
                            @else ملغي @endif
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {{ $order->created_at->format('Y-m-d H:i') ?? 'غير محدد' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            @can('orders.view')
                            <a href="{{ route('admin.orders.show', $order) }}" 
                               class="p-2 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors"
                               title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </a>
                            @endcan
                            
                            @can('orders.edit')
                            <a href="{{ route('admin.orders.edit', $order) }}" 
                               class="p-2 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-lg hover:bg-yellow-200 dark:hover:bg-yellow-900/50 transition-colors"
                               title="تعديل">
                                <i class="fas fa-edit"></i>
                            </a>
                            @endcan
                            
                            @can('orders.delete')
                            <button onclick="deleteOrder({{ $order->order_id }})" 
                                    class="p-2 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors"
                                    title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                            @endcan
                        </div>
                    </td>
                </tr>
                @empty
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <i class="fas fa-shopping-cart text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">لا توجد طلبات</h3>
                            <p class="text-gray-500 dark:text-gray-400">لم يتم العثور على أي طلبات تطابق معايير البحث</p>
                        </div>
                    </td>
                </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    @if(isset($orders) && $orders->hasPages())
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        {{ $orders->links() }}
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
function deleteOrder(orderId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        fetch(`/admin/orders/${orderId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الطلب: ' + (data.message || 'خطأ غير معروف'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الطلب');
        });
    }
}
</script>
@endsection
