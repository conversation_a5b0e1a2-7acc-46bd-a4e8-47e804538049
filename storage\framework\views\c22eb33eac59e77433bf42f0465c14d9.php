<?php $__env->startSection('title', 'إدارة قائمة الطعام - لوحة تحكم Eat Hub'); ?>

<?php $__env->startSection('page-title', 'إدارة قائمة الطعام'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6 h-full">
    <!-- العنوان الرئيسي -->
    <div class="flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">إدارة قائمة الطعام</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">إدارة عناصر القائمة والأصناف</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <form action="<?php echo e(route('admin.menu')); ?>" method="GET" class="flex space-x-2 space-x-reverse">
                <div class="relative">
                    <input type="text" name="search" value="<?php echo e(request('search')); ?>" placeholder="بحث..." class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                    <button type="submit" class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-search"></i>
                    </button>
                    <?php if(request('category')): ?>
                        <input type="hidden" name="category" value="<?php echo e(request('category')); ?>">
                    <?php endif; ?>
                </div>
            </form>
            <a href="<?php echo e(route('admin.menu.create')); ?>" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-lg flex items-center transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة منتج</span>
            </a>
        </div>
    </div>

<div class="mb-6 flex flex-wrap gap-2">
    <a href="<?php echo e(route('admin.menu')); ?>" class="px-4 py-2 <?php echo e(request('category') ? 'bg-white dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600' : 'bg-primary text-white'); ?> rounded-md">الكل</a>
    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <a href="<?php echo e(route('admin.menu', ['category' => $key, 'search' => request('search')])); ?>" class="px-4 py-2 <?php echo e(request('category') == $key ? 'bg-primary text-white' : 'bg-white dark:bg-gray-700 text-gray-800 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600'); ?> rounded-md"><?php echo e($label); ?></a>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <?php if($menuItems->count() > 0): ?>
        <?php $__currentLoopData = $menuItems; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="relative h-48">
                <?php if($item->image_path): ?>
                <img src="<?php echo e(asset('storage/' . $item->image_path)); ?>" alt="<?php echo e($item->name); ?>" class="w-full h-full object-cover">
                <?php else: ?>
                <div class="w-full h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <i class="fas fa-image text-gray-400 text-5xl"></i>
                </div>
                <?php endif; ?>
                <div class="absolute top-2 right-2">
                    <?php if($item->is_available): ?>
                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">متوفر</span>
                    <?php else: ?>
                    <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs">غير متوفر</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="font-bold text-lg text-gray-800 dark:text-white"><?php echo e($item->name); ?></h3>
                    <span class="font-bold text-primary"><?php echo e(number_format($item->price, 2)); ?> د.ل</span>
                </div>
                <p class="text-gray-600 dark:text-gray-300 text-sm mb-4"><?php echo e($item->description ?? 'لا يوجد وصف'); ?></p>
                <div class="flex justify-between items-center">
                    <?php
                        $categoryLabels = [
                            'main' => 'الأطباق الرئيسية',
                            'appetizer' => 'المقبلات',
                            'dessert' => 'الحلويات',
                            'beverage' => 'المشروبات'
                        ];
                        $categoryColors = [
                            'main' => 'blue',
                            'appetizer' => 'green',
                            'dessert' => 'purple',
                            'beverage' => 'yellow'
                        ];
                        $color = $categoryColors[$item->category] ?? 'gray';
                    ?>
                    <span class="text-xs bg-<?php echo e($color); ?>-100 dark:bg-<?php echo e($color); ?>-900/30 text-<?php echo e($color); ?>-800 dark:text-<?php echo e($color); ?>-300 px-2 py-1 rounded-full">
                        <?php echo e($categoryLabels[$item->category] ?? $item->category); ?>

                    </span>
                    <div class="flex space-x-2 space-x-reverse">
                        <a href="<?php echo e(route('admin.menu.show', $item->item_id)); ?>" class="text-gray-500 hover:text-gray-700 transition-all">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="<?php echo e(route('admin.menu.edit', $item->item_id)); ?>" class="text-blue-500 hover:text-blue-700 transition-all">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button onclick="deleteMenuItem(<?php echo e($item->item_id); ?>)" class="text-red-500 hover:text-red-700 transition-all">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <?php else: ?>
        <div class="col-span-3 p-8 text-center">
            <div class="text-gray-500 dark:text-gray-400 mb-4">
                <i class="fas fa-utensils text-5xl mb-4"></i>
                <?php if(request('search') || request('category')): ?>
                    <h3 class="text-xl font-bold">لا توجد نتائج مطابقة للبحث</h3>
                    <p class="mt-2">جرب تغيير معايير البحث أو التصفية</p>
                    <a href="<?php echo e(route('admin.menu')); ?>" class="inline-block mt-4 bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md transition-all">
                        <i class="fas fa-undo ml-2"></i>
                        <span>إعادة ضبط البحث</span>
                    </a>
                <?php else: ?>
                    <h3 class="text-xl font-bold">لا توجد منتجات في القائمة</h3>
                    <p class="mt-2">قم بإضافة منتجات جديدة للقائمة من خلال زر "إضافة منتج"</p>
                    <a href="<?php echo e(route('admin.menu.create')); ?>" class="inline-block mt-4 bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md transition-all">
                        <i class="fas fa-plus ml-2"></i>
                        <span>إضافة منتج</span>
                    </a>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- ترقيم الصفحات -->
<?php if($menuItems->hasPages()): ?>
<div class="mt-6 flex justify-center">
    <div class="pagination-wrapper">
        <?php echo e($menuItems->appends(request()->query())->links('pagination.tailwind')); ?>

    </div>
</div>
<?php endif; ?>

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا العنصر من القائمة؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    function deleteMenuItem(id) {
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelDelete = document.getElementById('cancelDelete');

        deleteForm.action = "<?php echo e(route('admin.menu.delete', '')); ?>/" + id;
        deleteModal.classList.remove('hidden');

        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }

    // تحسين تجربة البحث
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('input[name="search"]');
        const searchForm = searchInput.closest('form');

        // إرسال النموذج عند الضغط على Enter
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchForm.submit();
            }
        });

        // مسح حقل البحث عند النقر على زر X
        const clearSearch = document.createElement('button');
        clearSearch.type = 'button';
        clearSearch.className = 'absolute right-3 top-2.5 text-gray-500 dark:text-gray-400 hidden';
        clearSearch.innerHTML = '<i class="fas fa-times"></i>';

        searchInput.parentNode.appendChild(clearSearch);

        // إظهار زر المسح عندما يكون هناك نص في حقل البحث
        function toggleClearButton() {
            if (searchInput.value.length > 0) {
                clearSearch.classList.remove('hidden');
            } else {
                clearSearch.classList.add('hidden');
            }
        }

        // تنفيذ الدالة عند تحميل الصفحة
        toggleClearButton();

        // تنفيذ الدالة عند كتابة نص
        searchInput.addEventListener('input', toggleClearButton);

        // مسح النص وإعادة التركيز على حقل البحث
        clearSearch.addEventListener('click', function() {
            searchInput.value = '';
            searchInput.focus();
            toggleClearButton();
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\ccss450\cs450level10\resources\views/admin/menu/index.blade.php ENDPATH**/ ?>