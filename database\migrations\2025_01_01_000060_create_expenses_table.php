<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB; // أضف هذا السطر

return new class extends Migration
{
    public function up()
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id('expense_id');
            $table->decimal('amount', 10, 2);
            $table->enum('category', ['ingredients', 'utilities', 'salaries', 'maintenance', 'other']);
            $table->text('description');
            $table->date('expense_date');
            $table->foreignId('recorded_by')->constrained('users')->onDelete('cascade');
            $table->enum('payment_method', ['cash', 'bank_transfer', 'cheque']);
            $table->timestamps();
        });

        // استبدل $table->check() بهذا السطر:
        DB::statement('ALTER TABLE expenses ADD CONSTRAINT expenses_amount_check CHECK (amount > 0)');
    }

    public function down()
    {
        Schema::dropIfExists('expenses');
    }
};