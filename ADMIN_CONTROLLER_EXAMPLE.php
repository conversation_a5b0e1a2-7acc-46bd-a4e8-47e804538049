<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;

/**
 * مثال على كيفية استخدام نظام الصلاحيات الجديد للمديرين
 * هذا مثال توضيحي فقط - ليس للاستخدام المباشر
 */
class AdminControllerExample extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // لا نحتاج middleware للصلاحيات للمديرين - هم يملكون كل شيء تلقائياً
    }

    /**
     * عرض الطلبات - المديرون يرون كل شيء، الموظفون يرون طلباتهم فقط
     */
    public function orders(Request $request)
    {
        $query = Order::with(['user', 'items.menuItem']);

        // المديرون يرون جميع الطلبات، الموظفون يرون طلباتهم فقط
        if (!Gate::allows('full-data-access')) {
            $query->where('user_id', auth()->id());
        }

        // تصفية حسب الحالة (للجميع)
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $orders = $query->paginate(15);

        return view('admin.orders.index', compact('orders'));
    }

    /**
     * حذف طلب - المديرون يمكنهم حذف أي طلب
     */
    public function deleteOrder($id)
    {
        $order = Order::findOrFail($id);

        // التحقق من الصلاحية
        if (!auth()->user()->can('orders.delete')) {
            // المديرون لن يدخلوا هنا أبداً لأن can() تعيد true دائماً لهم
            abort(403, 'ليس لديك صلاحية لحذف الطلبات');
        }

        // التحقق من ملكية الطلب (للموظفين فقط)
        if (!Gate::allows('delete-any-data') && $order->user_id !== auth()->id()) {
            abort(403, 'يمكنك حذف طلباتك فقط');
        }

        $order->delete();

        return redirect()->back()->with('success', 'تم حذف الطلب بنجاح');
    }

    /**
     * إدارة المستخدمين - للمديرين فقط
     */
    public function manageUsers()
    {
        // التحقق من كون المستخدم مدير
        if (!Gate::allows('is-admin')) {
            abort(403, 'هذه الصفحة للمديرين فقط');
        }

        $users = User::paginate(15);

        return view('admin.users.index', compact('users'));
    }

    /**
     * حذف مستخدم - للمديرين فقط
     */
    public function deleteUser($id)
    {
        // استخدام Gate مخصص للتحقق من صلاحية إدارة المستخدمين
        if (!Gate::allows('manage-users')) {
            abort(403, 'ليس لديك صلاحية لحذف المستخدمين');
        }

        $user = User::findOrFail($id);

        // منع المدير من حذف نفسه
        if ($user->id === auth()->id()) {
            return redirect()->back()->with('error', 'لا يمكنك حذف حسابك الخاص');
        }

        $user->delete();

        return redirect()->back()->with('success', 'تم حذف المستخدم بنجاح');
    }

    /**
     * التقارير المالية - للمديرين فقط
     */
    public function financialReports()
    {
        // استخدام Gate مخصص للتقارير
        if (!Gate::allows('access-all-reports')) {
            abort(403, 'ليس لديك صلاحية للوصول للتقارير المالية');
        }

        // كود التقارير المالية
        $totalRevenue = Order::where('status', 'completed')->sum('total_amount');
        $monthlyRevenue = Order::where('status', 'completed')
            ->whereMonth('created_at', now()->month)
            ->sum('total_amount');

        return view('admin.reports.financial', compact('totalRevenue', 'monthlyRevenue'));
    }

    /**
     * إعدادات النظام - للمديرين فقط
     */
    public function systemSettings()
    {
        // المديرون فقط يمكنهم الوصول للإعدادات
        if (!auth()->user()->isAdmin()) {
            abort(403, 'هذه الصفحة للمديرين فقط');
        }

        return view('admin.settings.index');
    }

    /**
     * مثال على استخدام الصلاحيات في API
     */
    public function apiOrderStatus(Request $request, $id)
    {
        $order = Order::findOrFail($id);

        // التحقق من الصلاحية
        if (!auth()->user()->can('orders.status')) {
            return response()->json([
                'error' => 'ليس لديك صلاحية لتغيير حالة الطلب'
            ], 403);
        }

        // التحقق من ملكية الطلب (للموظفين فقط)
        if (!Gate::allows('edit-any-data') && $order->user_id !== auth()->id()) {
            return response()->json([
                'error' => 'يمكنك تعديل طلباتك فقط'
            ], 403);
        }

        $order->update(['status' => $request->status]);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة الطلب'
        ]);
    }

    /**
     * مثال على عرض البيانات حسب الصلاحيات
     */
    public function dashboard()
    {
        $data = [];

        // إحصائيات الطلبات
        if (auth()->user()->can('orders.view')) {
            if (Gate::allows('full-data-access')) {
                // المديرون يرون جميع الطلبات
                $data['total_orders'] = Order::count();
                $data['pending_orders'] = Order::where('status', 'pending')->count();
            } else {
                // الموظفون يرون طلباتهم فقط
                $data['total_orders'] = Order::where('user_id', auth()->id())->count();
                $data['pending_orders'] = Order::where('user_id', auth()->id())
                    ->where('status', 'pending')->count();
            }
        }

        // إحصائيات المستخدمين (للمديرين فقط)
        if (Gate::allows('manage-users')) {
            $data['total_users'] = User::count();
            $data['active_users'] = User::where('is_active', true)->count();
        }

        // التقارير المالية (للمديرين فقط)
        if (Gate::allows('access-all-reports')) {
            $data['total_revenue'] = Order::where('status', 'completed')->sum('total_amount');
            $data['monthly_revenue'] = Order::where('status', 'completed')
                ->whereMonth('created_at', now()->month)
                ->sum('total_amount');
        }

        return view('admin.dashboard', compact('data'));
    }

    /**
     * مثال على استخدام middleware مخصص
     */
    public function sensitiveData()
    {
        // يمكن استخدام middleware مخصص للتحقق من صلاحيات معقدة
        $this->middleware(function ($request, $next) {
            // المديرون يمرون تلقائياً
            if (auth()->user()->isAdmin()) {
                return $next($request);
            }

            // للموظفين، تحقق من صلاحيات محددة
            if (!auth()->user()->can('sensitive.data.access')) {
                abort(403, 'ليس لديك صلاحية للوصول لهذه البيانات الحساسة');
            }

            return $next($request);
        });

        return view('admin.sensitive-data');
    }
}

/**
 * ملاحظات مهمة:
 * 
 * 1. المديرون (user_type = 'admin') يملكون صلاحيات كاملة تلقائياً
 * 2. استخدم Gate::allows('is-admin') للتحقق من كون المستخدم مدير
 * 3. استخدم Gate::allows('full-data-access') للوصول الكامل للبيانات
 * 4. استخدم auth()->user()->can('permission') للتحقق من الصلاحيات العادية
 * 5. المديرون يتجاوزون جميع فحوصات can() تلقائياً
 * 6. استخدم Gates المخصصة للمنطق المعقد
 * 7. لا تحتاج لمنح المديرين صلاحيات محددة - هم يملكون كل شيء
 */
