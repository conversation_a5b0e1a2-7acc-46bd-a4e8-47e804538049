@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">احجز طاولة (اختبار)</h1>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                احجز طاولتك الآن واستمتع بتجربة طعام لا تُنسى في أجواء مريحة
            </p>
        </div>

        <div class="max-w-2xl mx-auto">
            <!-- رسائل النجاح والخطأ -->
            @if(session('success'))
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {{ session('error') }}
                </div>
            @endif

            @if($errors->any())
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <ul>
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-8">
                <form action="{{ route('test.reservation.store') }}" method="POST">
                    @csrf

                    <!-- معلومات الحجز -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- تاريخ الحجز -->
                        <div>
                            <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                تاريخ الحجز
                            </label>
                            <input type="date"
                                   id="reservation_date"
                                   name="reservation_date"
                                   min="{{ date('Y-m-d') }}"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                   required>
                        </div>

                        <!-- وقت الحجز -->
                        <div>
                            <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                وقت الحجز
                            </label>
                            <select id="reservation_time"
                                    name="reservation_time"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    required>
                                <option value="">اختر الوقت</option>
                                <option value="12:00">12:00 ظهراً</option>
                                <option value="12:30">12:30 ظهراً</option>
                                <option value="13:00">1:00 ظهراً</option>
                                <option value="13:30">1:30 ظهراً</option>
                                <option value="14:00">2:00 ظهراً</option>
                                <option value="14:30">2:30 ظهراً</option>
                                <option value="15:00">3:00 عصراً</option>
                                <option value="19:00">7:00 مساءً</option>
                                <option value="19:30">7:30 مساءً</option>
                                <option value="20:00">8:00 مساءً</option>
                                <option value="20:30">8:30 مساءً</option>
                                <option value="21:00">9:00 مساءً</option>
                                <option value="21:30">9:30 مساءً</option>
                                <option value="22:00">10:00 مساءً</option>
                            </select>
                        </div>
                    </div>

                    <!-- عدد الأشخاص -->
                    <div class="mb-6">
                        <label for="party_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            عدد الأشخاص
                        </label>
                        <select id="party_size"
                                name="party_size"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر عدد الأشخاص</option>
                            @for($i = 1; $i <= 10; $i++)
                            <option value="{{ $i }}">{{ $i }} {{ $i == 1 ? 'شخص' : 'أشخاص' }}</option>
                            @endfor
                        </select>
                    </div>

                    <!-- زر البحث عن الطاولات -->
                    <div class="mb-6">
                        <button type="button" id="checkAvailabilityBtn"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-search ml-2"></i>
                            البحث عن الطاولات المتاحة
                        </button>
                    </div>

                    <!-- عرض الطاولات المتاحة -->
                    <div id="availableTablesSection" class="mb-6 hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                            اختر الطاولة المناسبة:
                        </label>
                        <div id="availableTablesList" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- سيتم ملء هذا القسم بـ JavaScript -->
                        </div>
                        <input type="hidden" name="selected_table" id="selected_table" required>
                    </div>

                    <!-- ملاحظات خاصة -->
                    <div class="mb-6">
                        <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            ملاحظات خاصة (اختياري)
                        </label>
                        <textarea id="special_requests"
                                  name="special_requests"
                                  rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="أي طلبات خاصة أو ملاحظات..."></textarea>
                    </div>

                    <!-- معلومات المستخدم للاختبار -->
                    <div class="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <h3 class="text-lg font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                            معلومات الاختبار
                        </h3>
                        <p class="text-yellow-700 dark:text-yellow-300 text-sm">
                            سيتم إنشاء الحجز باستخدام user_id = 1 للاختبار
                        </p>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit"
                                class="flex-1 bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-calendar-check ml-2"></i>
                            تأكيد الحجز (اختبار)
                        </button>
                        <a href="{{ route('customer.index') }}"
                           class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-bold py-3 px-6 rounded-lg transition text-center">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر النموذج
    const checkAvailabilityBtn = document.getElementById('checkAvailabilityBtn');
    const availableTablesSection = document.getElementById('availableTablesSection');
    const availableTablesList = document.getElementById('availableTablesList');
    const selectedTableInput = document.getElementById('selected_table');
    const reservationForm = document.querySelector('form');

    // التحقق من توفر الطاولات
    checkAvailabilityBtn.addEventListener('click', function() {
        const date = document.getElementById('reservation_date').value;
        const time = document.getElementById('reservation_time').value;
        const partySize = document.getElementById('party_size').value;

        if (!date || !time || !partySize) {
            alert('يرجى إدخال التاريخ والوقت وعدد الأشخاص أولاً');
            return;
        }

        // تعطيل الزر وإظهار حالة التحميل
        checkAvailabilityBtn.disabled = true;
        checkAvailabilityBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البحث...';

        // إرسال طلب AJAX
        fetch('{{ route("test.reservation.check-availability") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                date: date,
                time: time,
                party_size: parseInt(partySize)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAvailableTables(data.available_tables);
                alert(data.message);
            } else {
                alert(data.message);
                availableTablesSection.classList.add('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء البحث عن الطاولات');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            checkAvailabilityBtn.disabled = false;
            checkAvailabilityBtn.innerHTML = '<i class="fas fa-search ml-2"></i>البحث عن الطاولات المتاحة';
        });
    });

    // عرض الطاولات المتاحة
    function displayAvailableTables(tables) {
        availableTablesList.innerHTML = '';

        if (tables.length === 0) {
            availableTablesSection.classList.add('hidden');
            return;
        }

        tables.forEach(table => {
            const tableCard = document.createElement('div');
            tableCard.className = 'table-option border border-gray-300 dark:border-gray-600 rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-primary/5 transition';
            tableCard.dataset.tableId = table.table_id;

            tableCard.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-bold text-gray-800 dark:text-white">طاولة #${table.table_number}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">${table.location}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">سعة: ${table.capacity} أشخاص</p>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-chair text-2xl"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <span class="inline-block bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs">
                        متاحة
                    </span>
                </div>
            `;

            tableCard.addEventListener('click', function() {
                // إزالة التحديد من جميع الطاولات
                document.querySelectorAll('.table-option').forEach(option => {
                    option.classList.remove('border-primary', 'bg-primary/10');
                    option.classList.add('border-gray-300', 'dark:border-gray-600');
                });

                // تحديد الطاولة المختارة
                this.classList.remove('border-gray-300', 'dark:border-gray-600');
                this.classList.add('border-primary', 'bg-primary/10');

                selectedTableInput.value = table.table_id;
            });

            availableTablesList.appendChild(tableCard);
        });

        availableTablesSection.classList.remove('hidden');
    }

    // التحقق من النموذج قبل الإرسال
    reservationForm.addEventListener('submit', function(e) {
        // التحقق من اختيار طاولة
        if (!selectedTableInput.value) {
            e.preventDefault();
            alert('يرجى البحث عن الطاولات المتاحة واختيار طاولة');
            return;
        }

        // إظهار رسالة تأكيد
        alert('جاري معالجة طلب الحجز...');
    });
});
</script>

@include('customer.partials.scripts')

</body>
</html>
