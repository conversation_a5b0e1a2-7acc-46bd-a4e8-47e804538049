@extends('layouts.admin')

@section('title', 'الملف الشخصي - لوحة تحكم Eat Hub')

@section('page-title', 'الملف الشخصي')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">الملف الشخصي</h2>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        @if(session('success'))
        <div class="mb-4 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 p-4 rounded-md">
            {{ session('success') }}
        </div>
        @endif

        @if(session('error'))
        <div class="mb-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 p-4 rounded-md">
            {{ session('error') }}
        </div>
        @endif

        @if($errors->any())
        <div class="mb-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 p-4 rounded-md">
            <ul class="list-disc list-inside">
                @foreach($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <div class="flex flex-col md:flex-row">
            <div class="md:w-1/3 mb-6 md:mb-0 flex flex-col items-center">
                <div class="relative group">
                    <div class="w-32 h-32 rounded-full bg-primary flex items-center justify-center text-white text-4xl font-bold mb-4 overflow-hidden">
                        @if($user->profile_image)
                            <img src="{{ asset('storage/' . $user->profile_image) }}" alt="{{ $user->first_name }}" class="w-full h-full object-cover">
                        @else
                            {{ substr($user->first_name, 0, 1) }}
                        @endif
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer">
                        <label for="profile_image" class="text-white text-sm cursor-pointer">
                            <i class="fas fa-camera mr-1"></i>
                            تغيير الصورة
                        </label>
                    </div>
                </div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white">{{ $user->first_name }} {{ $user->last_name }}</h3>
                <p class="text-gray-600 dark:text-gray-400">{{ $user->user_type == 'admin' ? 'مدير النظام' : ($user->user_type == 'employee' ? 'موظف' : 'عميل') }}</p>

                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        <i class="fas fa-envelope ml-1"></i> {{ $user->email }}
                    </p>
                    @if($user->phone)
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <i class="fas fa-phone ml-1"></i> {{ $user->phone }}
                    </p>
                    @endif
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        <i class="fas fa-calendar ml-1"></i> تاريخ التسجيل: {{ \Carbon\Carbon::parse($user->created_at)->format('Y/m/d') }}
                    </p>
                </div>
            </div>

            <div class="md:w-2/3 md:pr-8">
                <form action="{{ route('admin.profile.update') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                    @csrf
                    @method('PUT')
                    <input type="file" id="profile_image" name="profile_image" class="hidden" accept="image/*" onchange="document.getElementById('submit-form').click()">

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-gray-700 dark:text-gray-300 mb-1">الاسم الأول</label>
                            <input type="text" id="first_name" name="first_name" value="{{ $user->first_name }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>

                        <div>
                            <label for="last_name" class="block text-gray-700 dark:text-gray-300 mb-1">الاسم الأخير</label>
                            <input type="text" id="last_name" name="last_name" value="{{ $user->last_name }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                        </div>
                    </div>

                    <div>
                        <label for="email" class="block text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" value="{{ $user->email }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div>
                        <label for="phone" class="block text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف</label>
                        <input type="text" id="phone" name="phone" value="{{ $user->phone }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div>
                        <label for="address" class="block text-gray-700 dark:text-gray-300 mb-1">العنوان</label>
                        <textarea id="address" name="address" rows="2" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">{{ $user->address ?? '' }}</textarea>
                    </div>

                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                        <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إعدادات التطبيق</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="language" class="block text-gray-700 dark:text-gray-300 mb-1">اللغة المفضلة</label>
                                <select id="language" name="language" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="ar" {{ ($user->language ?? 'ar') == 'ar' ? 'selected' : '' }}>العربية</option>
                                    <option value="en" {{ ($user->language ?? '') == 'en' ? 'selected' : '' }}>الإنجليزية</option>
                                </select>
                            </div>

                            <div>
                                <label for="theme" class="block text-gray-700 dark:text-gray-300 mb-1">المظهر المفضل</label>
                                <select id="theme" name="theme" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                    <option value="light" {{ ($user->theme ?? '') == 'light' ? 'selected' : '' }}>فاتح</option>
                                    <option value="dark" {{ ($user->theme ?? '') == 'dark' ? 'selected' : '' }}>داكن</option>
                                    <option value="system" {{ ($user->theme ?? 'system') == 'system' ? 'selected' : '' }}>حسب النظام</option>
                                </select>
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="flex items-center">
                                <input type="checkbox" name="notifications_enabled" value="1" class="ml-2 text-primary focus:ring-primary" {{ ($user->notifications_enabled ?? 1) ? 'checked' : '' }}>
                                <span class="text-gray-700 dark:text-gray-300">تفعيل الإشعارات</span>
                            </label>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                        <h4 class="text-lg font-bold text-gray-800 dark:text-white mb-4">تغيير كلمة المرور</h4>

                        <div class="space-y-4">
                            <div>
                                <label for="current_password" class="block text-gray-700 dark:text-gray-300 mb-1">كلمة المرور الحالية</label>
                                <input type="password" id="current_password" name="current_password" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>

                            <div>
                                <label for="password" class="block text-gray-700 dark:text-gray-300 mb-1">كلمة المرور الجديدة</label>
                                <input type="password" id="password" name="password" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>

                            <div>
                                <label for="password_confirmation" class="block text-gray-700 dark:text-gray-300 mb-1">تأكيد كلمة المرور الجديدة</label>
                                <input type="password" id="password_confirmation" name="password_confirmation" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <button type="button" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-6 rounded-md transition" onclick="if(confirm('هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.')) { document.getElementById('delete-account-form').submit(); }">
                            <i class="fas fa-trash-alt ml-1"></i>
                            حذف الحساب
                        </button>

                        <button id="submit-form" type="submit" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-6 rounded-md transition">
                            <i class="fas fa-save ml-1"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>

                <!-- نموذج حذف الحساب -->
                <form id="delete-account-form" action="{{ route('admin.profile.delete') }}" method="POST" class="hidden">
                    @csrf
                    @method('DELETE')
                </form>
            </div>
        </div>
    </div>
</div>
@endsection
