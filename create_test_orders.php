<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\User;
use App\Models\MenuItem;
use Carbon\Carbon;

try {
    // البحث عن مستخدم للطلب أو إنشاء واحد
    $user = User::where('user_type', 'customer')->first();
    if (!$user) {
        $user = User::create([
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'user_type' => 'customer',
            'phone' => '+218 91 234 5678',
            'is_active' => true
        ]);
        echo "تم إنشاء مستخدم جديد: {$user->first_name} {$user->last_name}" . PHP_EOL;
    }

    // البحث عن عناصر القائمة أو إنشاء بعضها
    $menuItems = MenuItem::take(3)->get();
    if ($menuItems->count() == 0) {
        // إنشاء عناصر قائمة تجريبية
        $menuItems = collect([
            MenuItem::create([
                'name' => 'بيتزا مارجريتا',
                'description' => 'بيتزا كلاسيكية بالطماطم والجبن',
                'price' => 45.00,
                'category' => 'البيتزا',
                'is_available' => true,
                'preparation_time' => 20
            ]),
            MenuItem::create([
                'name' => 'برجر لحم',
                'description' => 'برجر لحم طازج مع الخضار',
                'price' => 35.00,
                'category' => 'البرجر',
                'is_available' => true,
                'preparation_time' => 15
            ]),
            MenuItem::create([
                'name' => 'سلطة قيصر',
                'description' => 'سلطة قيصر كلاسيكية',
                'price' => 25.00,
                'category' => 'السلطات',
                'is_available' => true,
                'preparation_time' => 10
            ])
        ]);
        echo "تم إنشاء عناصر قائمة تجريبية" . PHP_EOL;
    }

    // إنشاء طلب مكتمل
    $completedOrder = Order::create([
        'user_id' => $user->user_id,
        'customer_name' => $user->first_name . ' ' . $user->last_name,
        'customer_phone' => $user->phone,
        'total_amount' => 105.00,
        'status' => 'completed',
        'order_type' => 'delivery',
        'delivery_address' => 'طرابلس، شارع الجمهورية',
        'payment_method' => 'card',
        'notes' => 'طلب تجريبي مكتمل',
        'created_at' => Carbon::now()->subHours(2),
        'updated_at' => Carbon::now()->subHour(1)
    ]);

    // إضافة عناصر للطلب المكتمل
    foreach ($menuItems as $index => $item) {
        OrderItem::create([
            'order_id' => $completedOrder->order_id,
            'menu_item_id' => $item->item_id,
            'quantity' => $index + 1,
            'price' => $item->price
        ]);
    }

    echo "تم إنشاء طلب مكتمل - ID: {$completedOrder->order_id}" . PHP_EOL;

    // إنشاء طلب قيد التحضير
    $pendingOrder = Order::create([
        'user_id' => $user->user_id,
        'customer_name' => $user->first_name . ' ' . $user->last_name,
        'customer_phone' => $user->phone,
        'total_amount' => 80.00,
        'status' => 'pending',
        'order_type' => 'pickup',
        'payment_method' => 'cash',
        'notes' => 'طلب تجريبي قيد التحضير',
        'created_at' => Carbon::now()->subMinutes(30),
        'updated_at' => Carbon::now()->subMinutes(30)
    ]);

    // إضافة عناصر للطلب قيد التحضير
    OrderItem::create([
        'order_id' => $pendingOrder->order_id,
        'menu_item_id' => $menuItems->first()->item_id,
        'quantity' => 2,
        'price' => $menuItems->first()->price
    ]);

    echo "تم إنشاء طلب قيد التحضير - ID: {$pendingOrder->order_id}" . PHP_EOL;

    // إنشاء طلب ملغي
    $canceledOrder = Order::create([
        'user_id' => $user->user_id,
        'customer_name' => $user->first_name . ' ' . $user->last_name,
        'customer_phone' => $user->phone,
        'total_amount' => 60.00,
        'status' => 'canceled',
        'order_type' => 'delivery',
        'delivery_address' => 'طرابلس، شارع الجمهورية',
        'payment_method' => 'wallet',
        'notes' => 'طلب تجريبي ملغي',
        'created_at' => Carbon::now()->subDays(1),
        'updated_at' => Carbon::now()->subDays(1)->addHours(1)
    ]);

    // إضافة عناصر للطلب الملغي
    OrderItem::create([
        'order_id' => $canceledOrder->order_id,
        'menu_item_id' => $menuItems->last()->item_id,
        'quantity' => 1,
        'price' => $menuItems->last()->price
    ]);

    echo "تم إنشاء طلب ملغي - ID: {$canceledOrder->order_id}" . PHP_EOL;

    echo PHP_EOL . "تم إنشاء جميع الطلبات التجريبية بنجاح!" . PHP_EOL;
    echo "معرف المستخدم: {$user->user_id}" . PHP_EOL;
    echo "البريد الإلكتروني: {$user->email}" . PHP_EOL;
    echo "كلمة المرور: password" . PHP_EOL;

} catch (Exception $e) {
    echo "خطأ: " . $e->getMessage() . PHP_EOL;
    echo "التفاصيل: " . $e->getTraceAsString() . PHP_EOL;
}
