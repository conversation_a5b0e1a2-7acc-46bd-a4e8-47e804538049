@extends('layouts.admin')

@section('title', 'إعدادات النظام - لوحة تحكم Eat Hub')

@section('page-title', 'إعدادات النظام')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">إعدادات النظام</h2>

    @if(session('error'))
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
            <span class="block sm:inline">{{ session('error') }}</span>
        </div>
    @endif

    <form action="{{ route('admin.settings.update') }}" method="POST">
        @csrf

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <span class="block sm:inline">{{ session('success') }}</span>
            </div>
        @endif

        @if($errors->any())
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                <ul>
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- إعدادات عامة -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-cog text-primary ml-2"></i>
                    إعدادات عامة
                </h3>

                <div class="space-y-4">
                    <div>
                        <label for="restaurant_name" class="block text-gray-700 dark:text-gray-300 mb-1">اسم المطعم</label>
                        <input type="text" id="restaurant_name" name="restaurant_name" value="{{ $settings['restaurant_name'] ?? 'Eat Hub' }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div>
                        <label for="restaurant_phone" class="block text-gray-700 dark:text-gray-300 mb-1">رقم الهاتف</label>
                        <input type="text" id="restaurant_phone" name="restaurant_phone" value="{{ $settings['restaurant_phone'] ?? '0123456789' }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div>
                        <label for="restaurant_email" class="block text-gray-700 dark:text-gray-300 mb-1">البريد الإلكتروني</label>
                        <input type="email" id="restaurant_email" name="restaurant_email" value="{{ $settings['restaurant_email'] ?? '<EMAIL>' }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div>
                        <label for="restaurant_address" class="block text-gray-700 dark:text-gray-300 mb-1">العنوان</label>
                        <textarea id="restaurant_address" name="restaurant_address" rows="2" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">{{ $settings['restaurant_address'] ?? 'شارع عمر المختار، طرابلس، ليبيا' }}</textarea>
                    </div>
                </div>
            </div>

            <!-- إعدادات الضرائب والرسوم -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-percentage text-primary ml-2"></i>
                    الضرائب والرسوم
                </h3>

                <div class="space-y-4">
                    <div>
                        <label for="tax_rate" class="block text-gray-700 dark:text-gray-300 mb-1">نسبة ضريبة القيمة المضافة (%)</label>
                        <input type="number" id="tax_rate" name="tax_rate" value="{{ $settings['tax_rate'] ?? '15' }}" min="0" max="100" step="0.01" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div>
                        <label for="service_fee" class="block text-gray-700 dark:text-gray-300 mb-1">رسوم الخدمة (%)</label>
                        <input type="number" id="service_fee" name="service_fee" value="{{ $settings['service_fee'] ?? '5' }}" min="0" max="100" step="0.01" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="include_tax_in_price" name="include_tax_in_price" {{ isset($settings['include_tax_in_price']) && $settings['include_tax_in_price'] == 'نعم' ? 'checked' : '' }} class="w-5 h-5 text-primary focus:ring-primary rounded">
                        <label for="include_tax_in_price" class="mr-2 text-gray-700 dark:text-gray-300">تضمين الضريبة في أسعار القائمة</label>
                    </div>
                </div>
            </div>

            <!-- إعدادات النظام -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-sliders-h text-primary ml-2"></i>
                    إعدادات النظام
                </h3>

                <div class="space-y-4">
                    <div>
                        <label for="currency" class="block text-gray-700 dark:text-gray-300 mb-1">العملة</label>
                        <select id="currency" name="currency" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="LYD" {{ ($settings['currency'] ?? '') == 'LYD' ? 'selected' : '' }}>دينار ليبي (LYD)</option>
                            <option value="USD" {{ ($settings['currency'] ?? '') == 'USD' ? 'selected' : '' }}>دولار أمريكي (USD)</option>
                            <option value="EUR" {{ ($settings['currency'] ?? '') == 'EUR' ? 'selected' : '' }}>يورو (EUR)</option>
                        </select>
                    </div>

                    <div>
                        <label for="timezone" class="block text-gray-700 dark:text-gray-300 mb-1">المنطقة الزمنية</label>
                        <select id="timezone" name="timezone" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="Africa/Tripoli" {{ ($settings['timezone'] ?? '') == 'Africa/Tripoli' ? 'selected' : '' }}>طرابلس (GMT+2)</option>
                            <option value="Asia/Dubai" {{ ($settings['timezone'] ?? '') == 'Asia/Dubai' ? 'selected' : '' }}>دبي (GMT+4)</option>
                            <option value="Europe/London" {{ ($settings['timezone'] ?? '') == 'Europe/London' ? 'selected' : '' }}>لندن (GMT+0)</option>
                        </select>
                    </div>

                    <div>
                        <label for="date_format" class="block text-gray-700 dark:text-gray-300 mb-1">تنسيق التاريخ</label>
                        <select id="date_format" name="date_format" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="d/m/Y" {{ ($settings['date_format'] ?? '') == 'd/m/Y' ? 'selected' : '' }}>DD/MM/YYYY</option>
                            <option value="m/d/Y" {{ ($settings['date_format'] ?? '') == 'm/d/Y' ? 'selected' : '' }}>MM/DD/YYYY</option>
                            <option value="Y-m-d" {{ ($settings['date_format'] ?? '') == 'Y-m-d' ? 'selected' : '' }}>YYYY-MM-DD</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-6 flex justify-center">
            <button type="submit" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-6 rounded-md transition">
                حفظ جميع الإعدادات
            </button>
        </div>
    </form>
</div>
@endsection
