<!-- صفحة الملف الشخصي -->
@auth
<div id="profile-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- عنوان الصفحة -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">الملف الشخصي</h1>
                <p class="text-gray-600 dark:text-gray-400">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- معلومات المستخدم -->
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                        <div class="flex items-center mb-6">
                            <div class="w-20 h-20 bg-primary rounded-full flex items-center justify-center text-white text-2xl font-bold ml-4">
                                {{ auth()->check() ? substr(auth()->user()->first_name ?? 'أ', 0, 1) : 'أ' }}
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">
                                    {{ auth()->check() ? (auth()->user()->first_name ?? 'المستخدم') . ' ' . (auth()->user()->last_name ?? '') : 'المستخدم' }}
                                </h2>
                                <p class="text-gray-600 dark:text-gray-400">{{ auth()->check() ? (auth()->user()->email ?? '<EMAIL>') : '<EMAIL>' }}</p>
                                <span class="inline-block px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm mt-2">
                                    حساب نشط
                                </span>
                            </div>
                        </div>

                        <!-- رسائل النجاح والخطأ -->
                        @if(session('success'))
                            <div class="mb-6 bg-green-100 dark:bg-green-900/30 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle ml-2"></i>
                                    {{ session('success') }}
                                </div>
                            </div>
                        @endif

                        @if($errors->any())
                            <div class="mb-6 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-exclamation-circle ml-2"></i>
                                    <span class="font-medium">يرجى تصحيح الأخطاء التالية:</span>
                                </div>
                                <ul class="list-disc list-inside space-y-1">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <!-- نموذج تحديث المعلومات -->
                        <form action="{{ route('customer.profile.update') }}" method="POST" class="space-y-6">
                            @csrf
                            @method('PUT')

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأول</label>
                                    <input type="text" id="first_name" name="first_name" value="{{ old('first_name', auth()->user()->first_name ?? '') }}" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('first_name') border-red-500 @enderror">
                                    @error('first_name')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الاسم الأخير</label>
                                    <input type="text" id="last_name" name="last_name" value="{{ old('last_name', auth()->user()->last_name ?? '') }}" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('last_name') border-red-500 @enderror">
                                    @error('last_name')
                                        <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                                <input type="email" id="email" name="email" value="{{ old('email', auth()->user()->email ?? '') }}" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('email') border-red-500 @enderror">
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم الهاتف</label>
                                <input type="tel" id="phone" name="phone" value="{{ old('phone', auth()->user()->phone ?? '') }}" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('phone') border-red-500 @enderror">
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العنوان</label>
                                <textarea id="address" name="address" rows="3" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('address') border-red-500 @enderror" placeholder="أدخل عنوانك الكامل">{{ old('address', auth()->user()->address ?? '') }}</textarea>
                                @error('address')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>

                            <div class="flex justify-end space-x-4 space-x-reverse">
                                <button type="button" onclick="window.location.reload()" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition">
                                    إلغاء
                                </button>
                                <button type="submit" class="px-6 py-3 bg-primary hover:bg-primary/90 text-white rounded-lg transition">
                                    <i class="fas fa-save ml-2"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- تغيير كلمة المرور -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-6">تغيير كلمة المرور</h3>

                        <!-- رسائل خاصة بكلمة المرور -->
                        @if(session('password_success'))
                            <div class="mb-6 bg-green-100 dark:bg-green-900/30 border border-green-400 dark:border-green-600 text-green-700 dark:text-green-300 px-4 py-3 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle ml-2"></i>
                                    {{ session('password_success') }}
                                </div>
                            </div>
                        @endif

                        @if(session('password_error'))
                            <div class="mb-6 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle ml-2"></i>
                                    {{ session('password_error') }}
                                </div>
                            </div>
                        @endif

                        <form action="{{ route('customer.profile.update') }}" method="POST" class="space-y-6">
                            @csrf
                            @method('PUT')
                            <input type="hidden" name="update_password" value="1">

                            <div>
                                <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة المرور الحالية</label>
                                <input type="password" id="current_password" name="current_password" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('current_password') border-red-500 @enderror">
                                @error('current_password')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة المرور الجديدة</label>
                                <input type="password" id="password" name="password" required minlength="8" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('password') border-red-500 @enderror">
                                @error('password')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">يجب أن تكون كلمة المرور 8 أحرف على الأقل</p>
                            </div>
                            <div>
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تأكيد كلمة المرور</label>
                                <input type="password" id="password_confirmation" name="password_confirmation" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white @error('password_confirmation') border-red-500 @enderror">
                                @error('password_confirmation')
                                    <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                            <div class="flex justify-end">
                                <button type="submit" class="px-6 py-3 bg-primary hover:bg-primary/90 text-white rounded-lg transition">
                                    <i class="fas fa-key ml-2"></i>
                                    تحديث كلمة المرور
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- الشريط الجانبي -->
                <div class="space-y-6">
                    <!-- إحصائيات الحساب -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إحصائيات الحساب</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400 text-sm">تاريخ الانضمام</span>
                                <span class="font-medium text-gray-800 dark:text-white">
                                    {{ auth()->user()->created_at ? auth()->user()->created_at->format('Y/m/d') : 'غير محدد' }}
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الطلبات</span>
                                <span class="font-medium text-gray-800 dark:text-white">
                                    {{ \App\Models\Order::where('user_id', auth()->user()->user_id)->count() }} طلب
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي الإنفاق</span>
                                <span class="font-medium text-primary">
                                    {{ number_format(\App\Models\Order::where('user_id', auth()->user()->user_id)->sum('total_amount'), 2) }} د.ل
                                </span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600 dark:text-gray-400 text-sm">الحجوزات النشطة</span>
                                <span class="font-medium text-primary">
                                    {{ \App\Models\Reservation::where('user_id', auth()->user()->user_id)->where('status', 'confirmed')->where('reservation_time', '>=', now())->count() }} حجز
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- التفضيلات -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">التفضيلات</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 dark:text-gray-300">إشعارات البريد الإلكتروني</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 dark:text-gray-300">إشعارات الجوال</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700 dark:text-gray-300">العروض والخصومات</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- إجراءات الحساب -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">إجراءات الحساب</h3>
                        <div class="space-y-3">
                            <button class="w-full text-right px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition">
                                <i class="fas fa-download ml-2"></i>
                                تحميل بيانات الحساب
                            </button>
                            <button class="w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition">
                                <i class="fas fa-trash ml-2"></i>
                                حذف الحساب
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endauth

@guest
<!-- صفحة تسجيل الدخول للمستخدمين غير المسجلين -->
<div id="profile-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div class="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user-lock text-primary text-3xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">تسجيل الدخول مطلوب</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                يجب تسجيل الدخول للوصول إلى الملف الشخصي
            </p>
            <div class="space-y-4">
                <button id="loginBtn" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
                <button data-page="home" class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-home ml-2"></i>
                    العودة للرئيسية
                </button>
            </div>
        </div>
    </div>
</div>
@endguest

<script>
// إخفاء رسائل النجاح تلقائياً بعد 5 ثوان
document.addEventListener('DOMContentLoaded', function() {
    const successMessages = document.querySelectorAll('.bg-green-100, .bg-green-900\\/30');
    successMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.transition = 'opacity 0.5s ease-out';
            message.style.opacity = '0';
            setTimeout(function() {
                message.remove();
            }, 500);
        }, 5000);
    });

    // التحقق من تطابق كلمة المرور
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('password_confirmation');

    if (passwordField && confirmPasswordField) {
        function checkPasswordMatch() {
            if (passwordField.value && confirmPasswordField.value) {
                if (passwordField.value !== confirmPasswordField.value) {
                    confirmPasswordField.setCustomValidity('كلمة المرور غير متطابقة');
                } else {
                    confirmPasswordField.setCustomValidity('');
                }
            }
        }

        passwordField.addEventListener('input', checkPasswordMatch);
        confirmPasswordField.addEventListener('input', checkPasswordMatch);
    }
});
</script>
