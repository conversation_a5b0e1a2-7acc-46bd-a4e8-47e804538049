<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    // إيقاف التغليف ضمن معاملة لضمان تسلسل الأوامر
    public $withinTransaction = false;

    public function up()
    {
        // إنشاء الجدول أولاً
        Schema::create('menu_items', function (Blueprint $table) {
            $table->id('item_id');
            $table->string('name', 191)->unique();
            $table->decimal('price', 10, 2);
            $table->enum('category', ['main', 'appetizer', 'dessert', 'beverage']);
            $table->string('image_path')->nullable();
            $table->boolean('is_available')->default(true);
            $table->timestamps();
        });

        // ثمّ أضف قيد CHECK بعد التأكد من وجود الجدول
        DB::statement(<<<SQL
            ALTER TABLE `menu_items`
            ADD CONSTRAINT `chk_price_positive`
            CHECK (`price` > 0)
        SQL
        );
    }

    public function down()
    {
        Schema::table('menu_items', function (Blueprint $table) {
            $table->dropColumn('image_path');
        });
    }
};
