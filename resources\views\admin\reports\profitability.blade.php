@extends('layouts.admin')

@section('title', 'تقرير الربحية الشامل')

@section('page-title', 'تقرير الربحية الشامل')

@section('content')
<!-- العنوان والفلاتر -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-4">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">💰 تقرير الربحية الشامل</h2>
            <p class="text-gray-600 dark:text-gray-400">تحليل مفصل للأرباح والخسائر وربحية المنتجات</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <button onclick="exportReport('profitability', 'excel')" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-file-excel mr-2"></i>
                تصدير Excel
            </button>
            <button onclick="exportReport('profitability', 'pdf')" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                <i class="fas fa-file-pdf mr-2"></i>
                تصدير PDF
            </button>
        </div>
    </div>

    <!-- فلاتر التاريخ -->
    <form method="GET" action="{{ route('admin.reports.profitability') }}" class="flex flex-wrap items-end gap-4">
        <div class="flex-1 min-w-48">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
            <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}" 
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        <div class="flex-1 min-w-48">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
            <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}" 
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
        </div>
        <div>
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg flex items-center">
                <i class="fas fa-search mr-2"></i>
                فلترة
            </button>
        </div>
    </form>
</div>

<!-- البطاقات الإحصائية -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- إجمالي المبيعات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي المبيعات</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($financialData['sales']['total'], 2) }}</h3>
                <p class="text-blue-500 text-sm mt-2">دينار ليبي</p>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-chart-line text-blue-500 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- تكلفة المواد الخام -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">تكلفة المواد الخام</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($financialData['costs']['cogs'], 2) }}</h3>
                <p class="text-orange-500 text-sm mt-2">دينار ليبي</p>
            </div>
            <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3">
                <i class="fas fa-boxes text-orange-500 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- إجمالي الربح -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي الربح</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($financialData['profits']['gross'], 2) }}</h3>
                <p class="text-green-500 text-sm mt-2 flex items-center">
                    <span>{{ number_format($financialData['profits']['gross_margin'], 1) }}% هامش</span>
                </p>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-chart-pie text-green-500 text-xl"></i>
            </div>
        </div>
    </div>

    <!-- صافي الربح -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border-2 border-green-200 dark:border-green-600">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">صافي الربح</p>
                <h3 class="text-2xl font-bold text-{{ $financialData['profits']['net'] >= 0 ? 'green' : 'red' }}-600">
                    {{ number_format($financialData['profits']['net'], 2) }}
                </h3>
                <p class="text-{{ $financialData['profits']['net'] >= 0 ? 'green' : 'red' }}-500 text-sm mt-2 flex items-center">
                    <i class="fas fa-{{ $financialData['profits']['net'] >= 0 ? 'arrow-up' : 'arrow-down' }} mr-1"></i>
                    <span>{{ number_format($financialData['profits']['net_margin'], 1) }}% هامش</span>
                </p>
            </div>
            <div class="rounded-full bg-{{ $financialData['profits']['net'] >= 0 ? 'green' : 'red' }}-100 dark:bg-{{ $financialData['profits']['net'] >= 0 ? 'green' : 'red' }}-900/30 p-3">
                <i class="fas fa-coins text-{{ $financialData['profits']['net'] >= 0 ? 'green' : 'red' }}-500 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- تحليل ربحية المنتجات -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white">📊 تحليل ربحية المنتجات</h3>
        <div class="text-sm text-gray-600 dark:text-gray-400">
            إجمالي المنتجات: {{ count($productProfitability) }}
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">#</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">اسم المنتج</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية المباعة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي المبيعات</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">إجمالي التكلفة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الربح</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">هامش الربح</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">متوسط السعر</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @foreach($productProfitability as $index => $product)
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30 {{ $index < 3 ? 'bg-green-50 dark:bg-green-900/20' : '' }}">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        @if($index === 0)
                            <i class="fas fa-crown text-yellow-500 mr-1"></i>
                        @elseif($index === 1)
                            <i class="fas fa-medal text-gray-400 mr-1"></i>
                        @elseif($index === 2)
                            <i class="fas fa-award text-orange-500 mr-1"></i>
                        @endif
                        {{ $index + 1 }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $product['product_name'] }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {{ number_format($product['quantity_sold']) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                        {{ number_format($product['total_revenue'], 2) }} د.ل
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-orange-600">
                        {{ number_format($product['total_cost'], 2) }} د.ل
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-{{ $product['profit'] >= 0 ? 'green' : 'red' }}-600">
                        {{ number_format($product['profit'], 2) }} د.ل
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2 mr-2">
                                <div class="bg-{{ $product['profit_margin'] >= 50 ? 'green' : ($product['profit_margin'] >= 30 ? 'yellow' : 'red') }}-500 h-2 rounded-full" 
                                     style="width: {{ min(abs($product['profit_margin']), 100) }}%"></div>
                            </div>
                            <span class="text-sm font-medium text-gray-600 dark:text-gray-300">{{ number_format($product['profit_margin'], 1) }}%</span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                        {{ number_format($product['avg_price'], 2) }} د.ل
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

<!-- ملخص المصروفات -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-6">📋 تفصيل المصروفات</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h4 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">المصروفات حسب الفئة</h4>
            <div class="space-y-3">
                @foreach($financialData['expenses']['by_category'] as $expense)
                <div class="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">{{ $expense->category }}</span>
                    <span class="font-semibold text-red-600">{{ number_format($expense->total, 2) }} د.ل</span>
                </div>
                @endforeach
            </div>
        </div>
        
        <div>
            <h4 class="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-4">نسب التكاليف</h4>
            <div class="space-y-3">
                <div class="flex justify-between items-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">تكلفة المواد الخام</span>
                    <span class="font-semibold text-orange-600">
                        {{ $financialData['sales']['total'] > 0 ? number_format(($financialData['costs']['cogs'] / $financialData['sales']['total']) * 100, 1) : 0 }}%
                    </span>
                </div>
                <div class="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">المصروفات التشغيلية</span>
                    <span class="font-semibold text-red-600">
                        {{ $financialData['sales']['total'] > 0 ? number_format(($financialData['expenses']['total'] / $financialData['sales']['total']) * 100, 1) : 0 }}%
                    </span>
                </div>
                <div class="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <span class="text-gray-700 dark:text-gray-300">هامش الربح الصافي</span>
                    <span class="font-semibold text-green-600">{{ number_format($financialData['profits']['net_margin'], 1) }}%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport(type, format) {
    const startDate = '{{ $startDate->format("Y-m-d") }}';
    const endDate = '{{ $endDate->format("Y-m-d") }}';
    const url = `/admin/reports/export/${type}?format=${format}&start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}
</script>

@endsection
