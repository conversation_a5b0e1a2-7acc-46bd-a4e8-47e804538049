<!-- صفحة حجوزاتي -->
@auth
<div id="reservations-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <!-- عنوان الصفحة -->
            <div class="flex justify-between items-center mb-8">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">حجوزاتي</h1>
                    <p class="text-gray-600 dark:text-gray-400">إدارة جميع حجوزاتك وحجز طاولات جديدة</p>
                </div>
                <button id="newReservationBtn" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    حجز جديد
                </button>
            </div>

            <!-- فلاتر الحجوزات -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <div class="flex flex-wrap gap-4 items-center">
                    <div class="flex space-x-2 space-x-reverse">
                        <button class="reservation-filter-btn active px-4 py-2 rounded-full bg-primary text-white text-sm" data-status="all">
                            جميع الحجوزات
                        </button>
                        <button class="reservation-filter-btn px-4 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition text-sm" data-status="upcoming">
                            قادمة
                        </button>
                        <button class="reservation-filter-btn px-4 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition text-sm" data-status="completed">
                            مكتملة
                        </button>
                        <button class="reservation-filter-btn px-4 py-2 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white transition text-sm" data-status="cancelled">
                            ملغية
                        </button>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse mr-auto">
                        <input type="date" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white text-sm">
                        <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white text-sm">
                            <option>جميع الطاولات</option>
                            <option>طاولة لشخصين</option>
                            <option>طاولة لأربعة أشخاص</option>
                            <option>طاولة لستة أشخاص</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- قائمة الحجوزات -->
            <div class="space-y-6" id="reservations-container">
                <!-- حجز قادم -->
                <div class="reservation-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="upcoming">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">حجز #R1001</h3>
                                    <span class="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-sm">
                                        قادم
                                    </span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الحجز في 15 ديسمبر 2024</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-800 dark:text-white">غداً 7:00 م</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">19 ديسمبر 2024</p>
                            </div>
                        </div>

                        <!-- تفاصيل الحجز -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-chair text-primary text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-800 dark:text-white">طاولة #8</p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">منطقة النافذة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-users text-primary text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-800 dark:text-white">4 أشخاص</p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">عائلة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-clock text-primary text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-800 dark:text-white">ساعتان</p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">7:00 - 9:00 م</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ملاحظات خاصة -->
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
                            <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                                <i class="fas fa-sticky-note ml-1"></i>
                                <strong>ملاحظة خاصة:</strong> طاولة بجانب النافذة، احتفال بعيد ميلاد
                            </p>
                        </div>

                        <!-- العد التنازلي -->
                        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 mb-4">
                            <div class="flex items-center justify-between">
                                <span class="text-blue-800 dark:text-blue-200 text-sm">
                                    <i class="fas fa-hourglass-half ml-1"></i>
                                    الوقت المتبقي للحجز:
                                </span>
                                <span class="font-bold text-blue-800 dark:text-blue-200">1 يوم و 3 ساعات</span>
                            </div>
                        </div>

                        <!-- إجراءات الحجز -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-3 space-x-reverse">
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-edit ml-1"></i>تعديل الحجز
                                    </button>
                                    <button class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-share ml-1"></i>مشاركة التفاصيل
                                    </button>
                                    <button class="text-red-600 hover:text-red-700 text-sm">
                                        <i class="fas fa-times ml-1"></i>إلغاء الحجز
                                    </button>
                                </div>
                                <button class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-lg text-sm transition">
                                    <i class="fas fa-directions ml-1"></i>
                                    الاتجاهات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- حجز مكتمل -->
                <div class="reservation-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="completed">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">حجز #R1000</h3>
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">
                                        مكتمل
                                    </span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الحجز في 10 ديسمبر 2024</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-800 dark:text-white">15 ديسمبر 2024</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">6:30 - 8:30 م</p>
                            </div>
                        </div>

                        <!-- تفاصيل الحجز -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-chair text-primary text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-800 dark:text-white">طاولة #5</p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">منطقة الحديقة</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-users text-primary text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-800 dark:text-white">2 أشخاص</p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">رومانسي</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center">
                                    <i class="fas fa-clock text-primary text-xl ml-3"></i>
                                    <div>
                                        <p class="font-medium text-gray-800 dark:text-white">ساعتان</p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">6:30 - 8:30 م</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تقييم التجربة -->
                        <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3 mb-4">
                            <div class="flex items-center justify-between">
                                <span class="text-green-800 dark:text-green-200 text-sm">
                                    <i class="fas fa-check-circle ml-1"></i>
                                    تم إكمال الحجز بنجاح
                                </span>
                                <div class="flex items-center text-yellow-400">
                                    <span class="text-green-800 dark:text-green-200 text-sm ml-2">تقييمك:</span>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>

                        <!-- إجراءات الحجز -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex space-x-3 space-x-reverse">
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-redo ml-1"></i>حجز مماثل
                                </button>
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-star ml-1"></i>تقييم التجربة
                                </button>
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-download ml-1"></i>تحميل التأكيد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- حجز ملغي -->
                <div class="reservation-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden" data-status="cancelled">
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                    <h3 class="text-lg font-bold text-gray-800 dark:text-white">حجز #R999</h3>
                                    <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm">
                                        ملغي
                                    </span>
                                </div>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الحجز في 5 ديسمبر 2024</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-500 line-through">8 ديسمبر 2024</p>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">7:00 - 9:00 م</p>
                            </div>
                        </div>

                        <!-- سبب الإلغاء -->
                        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
                            <p class="text-red-600 dark:text-red-400 text-sm">
                                <i class="fas fa-info-circle ml-1"></i>
                                تم إلغاء الحجز بناءً على طلب العميل - ظروف طارئة
                            </p>
                        </div>

                        <!-- إجراءات الحجز -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <div class="flex space-x-3 space-x-reverse">
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-redo ml-1"></i>حجز جديد
                                </button>
                                <button class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-eye ml-1"></i>عرض التفاصيل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- رسالة عدم وجود حجوزات -->
            <div id="no-reservations" class="text-center py-12 hidden">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-calendar-times text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد حجوزات</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">لم تقم بأي حجوزات بعد</p>
                <button id="newReservationBtn2" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    احجز الآن
                </button>
            </div>
        </div>
    </div>
</div>
@endauth

@guest
<!-- صفحة تسجيل الدخول للمستخدمين غير المسجلين -->
<div id="reservations-page" class="page hidden">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
            <div class="w-20 h-20 bg-primary/10 dark:bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-user-lock text-primary text-3xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">تسجيل الدخول مطلوب</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                يجب تسجيل الدخول لعرض حجوزاتك أو إنشاء حجز جديد
            </p>
            <div class="space-y-4">
                <button id="loginBtn" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
                <button data-page="home" class="w-full bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-3 px-6 rounded-lg transition">
                    <i class="fas fa-home ml-2"></i>
                    العودة للرئيسية
                </button>
            </div>
        </div>
    </div>
</div>
@endguest

<script>
// فلترة الحجوزات
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.reservation-filter-btn');
    const reservationCards = document.querySelectorAll('.reservation-card');

    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const status = this.getAttribute('data-status');

            // تحديث حالة الأزرار
            filterButtons.forEach(btn => {
                btn.classList.remove('active', 'bg-primary', 'text-white');
                btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');
            });

            this.classList.add('active', 'bg-primary', 'text-white');
            this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-gray-700', 'dark:text-gray-300');

            // فلترة الحجوزات
            let visibleCount = 0;
            reservationCards.forEach(card => {
                if (status === 'all' || card.getAttribute('data-status') === status) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            // إظهار رسالة عدم وجود حجوزات
            const noReservationsMessage = document.getElementById('no-reservations');
            if (visibleCount === 0) {
                noReservationsMessage.classList.remove('hidden');
            } else {
                noReservationsMessage.classList.add('hidden');
            }
        });
    });

    // أزرار الحجز الجديد
    const newReservationBtns = document.querySelectorAll('#newReservationBtn, #newReservationBtn2');
    newReservationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // التوجه إلى صفحة الحجز الجديد
            window.location.href = '/customer/reservations/create';
        });
    });

    // وظائف أزرار الإجراءات
    setupActionButtons();
});

// إعداد أزرار الإجراءات
function setupActionButtons() {
    // البحث عن جميع الأزرار وإضافة المستمعات حسب النص
    document.querySelectorAll('button').forEach(btn => {
        const buttonText = btn.textContent.trim();

        if (buttonText.includes('تعديل الحجز')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                editReservation(reservationId);
            });
        }

        else if (buttonText.includes('مشاركة التفاصيل')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                shareReservation(reservationCard);
            });
        }

        else if (buttonText.includes('إلغاء الحجز')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                cancelReservation(reservationId);
            });
        }

        else if (buttonText.includes('الاتجاهات')) {
            btn.addEventListener('click', function() {
                getDirections();
            });
        }

        else if (buttonText.includes('حجز مماثل')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                repeatReservation(reservationCard);
            });
        }

        else if (buttonText.includes('تقييم التجربة')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                rateExperience(reservationId);
            });
        }

        else if (buttonText.includes('تحميل التأكيد')) {
            btn.addEventListener('click', function() {
                const reservationCard = this.closest('.reservation-card');
                const reservationId = getReservationId(reservationCard);
                downloadConfirmation(reservationId);
            });
        }
    });
}

// الحصول على معرف الحجز من البطاقة
function getReservationId(card) {
    const titleElement = card.querySelector('h3');
    if (titleElement) {
        const match = titleElement.textContent.match(/#(\w+)/);
        return match ? match[1] : null;
    }
    return null;
}

// تعديل الحجز
function editReservation(reservationId) {
    if (confirm('هل تريد تعديل هذا الحجز؟')) {
        // التوجه إلى صفحة التعديل
        window.location.href = `/customer/reservations/${reservationId}/edit`;
    }
}

// مشاركة تفاصيل الحجز
function shareReservation(card) {
    const reservationId = getReservationId(card);
    const dateTime = card.querySelector('.text-xl.font-bold').textContent;
    const tableInfo = card.querySelector('.text-gray-600').textContent;

    const shareText = `حجزي في المطعم:\n` +
                     `رقم الحجز: ${reservationId}\n` +
                     `التاريخ والوقت: ${dateTime}\n` +
                     `${tableInfo}`;

    if (navigator.share) {
        navigator.share({
            title: 'تفاصيل الحجز',
            text: shareText,
            url: window.location.href
        });
    } else {
        // نسخ النص إلى الحافظة
        navigator.clipboard.writeText(shareText).then(() => {
            showNotification('تم نسخ تفاصيل الحجز إلى الحافظة', 'success');
        });
    }
}

// إلغاء الحجز
function cancelReservation(reservationId) {
    if (confirm('هل أنت متأكد من إلغاء هذا الحجز؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        // إرسال طلب الإلغاء
        fetch(`/customer/reservations/${reservationId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم إلغاء الحجز بنجاح', 'success');
                // إعادة تحميل الصفحة أو إزالة البطاقة
                location.reload();
            } else {
                showNotification('حدث خطأ أثناء إلغاء الحجز', 'error');
            }
        })
        .catch(error => {
            showNotification('حدث خطأ في الاتصال', 'error');
        });
    }
}

// الحصول على الاتجاهات
function getDirections() {
    // إحداثيات المطعم (يمكن تخصيصها)
    const restaurantLat = 32.8872;
    const restaurantLng = 13.1913;

    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const userLat = position.coords.latitude;
                const userLng = position.coords.longitude;

                // فتح خرائط جوجل مع الاتجاهات
                const googleMapsUrl = `https://www.google.com/maps/dir/${userLat},${userLng}/${restaurantLat},${restaurantLng}`;
                window.open(googleMapsUrl, '_blank');
            },
            function(error) {
                // في حالة عدم توفر الموقع، فتح الخريطة بموقع المطعم فقط
                const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${restaurantLat},${restaurantLng}`;
                window.open(googleMapsUrl, '_blank');
            }
        );
    } else {
        // المتصفح لا يدعم تحديد الموقع
        const googleMapsUrl = `https://www.google.com/maps/search/?api=1&query=${restaurantLat},${restaurantLng}`;
        window.open(googleMapsUrl, '_blank');
    }
}

// حجز مماثل
function repeatReservation(card) {
    const tableInfo = card.querySelector('.text-gray-600').textContent;
    const guestCount = card.querySelector('.text-gray-600:nth-of-type(2)').textContent;

    if (confirm('هل تريد إنشاء حجز مماثل لهذا الحجز؟')) {
        // التوجه إلى صفحة الحجز مع البيانات المملوءة مسبقاً
        const params = new URLSearchParams({
            repeat: 'true',
            table: tableInfo,
            guests: guestCount
        });
        window.location.href = `/customer/reservations/create?${params.toString()}`;
    }
}

// تقييم التجربة
function rateExperience(reservationId) {
    // فتح نافذة التقييم
    const ratingModal = `
        <div id="ratingModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">تقييم التجربة</h3>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التقييم</label>
                    <div class="flex space-x-1 space-x-reverse" id="starRating">
                        ${[1,2,3,4,5].map(i => `<button class="star text-2xl text-gray-300 hover:text-yellow-400" data-rating="${i}">⭐</button>`).join('')}
                    </div>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التعليق</label>
                    <textarea id="reviewComment" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white" rows="3" placeholder="اكتب تعليقك هنا..."></textarea>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <button onclick="submitRating('${reservationId}')" class="flex-1 bg-primary text-white py-2 px-4 rounded-lg hover:bg-primary/90">إرسال التقييم</button>
                    <button onclick="closeRatingModal()" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-400">إلغاء</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', ratingModal);

    // إعداد نجوم التقييم
    document.querySelectorAll('#starRating .star').forEach(star => {
        star.addEventListener('click', function() {
            const rating = this.getAttribute('data-rating');
            document.querySelectorAll('#starRating .star').forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('text-yellow-400');
                    s.classList.remove('text-gray-300');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-gray-300');
                }
            });
        });
    });
}

// إرسال التقييم
function submitRating(reservationId) {
    const rating = document.querySelectorAll('#starRating .star.text-yellow-400').length;
    const comment = document.getElementById('reviewComment').value;

    if (rating === 0) {
        alert('يرجى اختيار تقييم');
        return;
    }

    // إرسال التقييم
    fetch('/customer/reviews/store', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            reservation_id: reservationId,
            rating: rating,
            comment: comment
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إرسال التقييم بنجاح', 'success');
            closeRatingModal();
        } else {
            showNotification('حدث خطأ أثناء إرسال التقييم', 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

// إغلاق نافذة التقييم
function closeRatingModal() {
    const modal = document.getElementById('ratingModal');
    if (modal) {
        modal.remove();
    }
}

// تحميل تأكيد الحجز
function downloadConfirmation(reservationId) {
    // إنشاء رابط التحميل
    const downloadUrl = `/customer/reservations/${reservationId}/download`;

    // إنشاء عنصر رابط مؤقت للتحميل
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `reservation-${reservationId}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('جاري تحميل تأكيد الحجز...', 'info');
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 3 ثوان
    setTimeout(() => {
        notification.remove();
    }, 3000);
}


</script>
