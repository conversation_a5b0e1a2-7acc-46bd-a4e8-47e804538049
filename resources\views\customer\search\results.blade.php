@extends('customer.layouts.simple')

@section('title', 'نتائج البحث - Eat Hub')

@section('content')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">نتائج البحث</h1>
            @if($query)
                <p class="text-gray-600 dark:text-gray-400">
                    نتائج البحث عن: "<span class="font-semibold text-primary">{{ $query }}</span>"
                </p>
            @endif
        </div>

        @if($query)
            <!-- فلاتر البحث -->
            <div class="mb-6 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <div class="flex flex-wrap gap-2">
                    <a href="{{ route('search', ['q' => $query, 'category' => 'all']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'all' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        الكل
                    </a>
                    <a href="{{ route('search', ['q' => $query, 'category' => 'menu']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'menu' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        قائمة الطعام
                    </a>
                    @auth
                    <a href="{{ route('search', ['q' => $query, 'category' => 'orders']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'orders' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        طلباتي
                    </a>
                    <a href="{{ route('search', ['q' => $query, 'category' => 'reservations']) }}"
                       class="px-4 py-2 rounded-full text-sm {{ $category === 'reservations' ? 'bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600' }}">
                        حجوزاتي
                    </a>
                    @endauth
                </div>
            </div>

            <!-- نتائج قائمة الطعام -->
            @if($results['menu']->count() > 0)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-utensils text-primary ml-2"></i>
                        قائمة الطعام ({{ $results['menu']->total() }})
                    </h2>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($results['menu'] as $item)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition">
                                @if($item->image_path)
                                    <img src="{{ asset($item->image_path) }}" alt="{{ $item->name }}" class="w-full h-48 object-cover">
                                @else
                                    <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                        <i class="fas fa-utensils text-4xl text-gray-400"></i>
                                    </div>
                                @endif

                                <div class="p-4">
                                    <h3 class="font-semibold text-gray-800 dark:text-white mb-2">{{ $item->name }}</h3>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">{{ $item->description }}</p>

                                    <div class="flex justify-between items-center">
                                        <span class="text-lg font-bold text-primary">{{ $item->price }} د.ل</span>
                                        <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded">
                                            {{ $item->category }}
                                        </span>
                                    </div>

                                    @auth
                                    <button class="w-full mt-3 bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-lg transition">
                                        <i class="fas fa-plus ml-2"></i>
                                        إضافة للسلة
                                    </button>
                                    @endauth
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $results['menu']->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif

            <!-- نتائج الطلبات -->
            @auth
            @if($results['orders']->count() > 0)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-shopping-bag text-primary ml-2"></i>
                        طلباتي ({{ $results['orders']->total() }})
                    </h2>

                    <div class="space-y-4">
                        @foreach($results['orders'] as $order)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-semibold text-gray-800 dark:text-white">طلب #{{ $order->order_id }}</h3>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $order->created_at->format('Y-m-d H:i') }}</p>
                                    </div>
                                    <div class="text-left">
                                        <span class="text-lg font-bold text-primary">{{ $order->total_amount }} د.ل</span>
                                        <span class="block text-xs px-2 py-1 rounded-full {{ $order->status === 'completed' ? 'bg-green-100 text-green-600' : ($order->status === 'pending' ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-600') }}">
                                            {{ $order->status === 'pending' ? 'في الانتظار' : ($order->status === 'preparing' ? 'قيد التحضير' : ($order->status === 'completed' ? 'مكتمل' : 'ملغي')) }}
                                        </span>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                        عرض التفاصيل <i class="fas fa-chevron-left mr-1"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $results['orders']->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif

            <!-- نتائج الحجوزات -->
            @if($results['reservations']->count() > 0)
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                        <i class="fas fa-calendar-check text-primary ml-2"></i>
                        حجوزاتي ({{ $results['reservations']->total() }})
                    </h2>

                    <div class="space-y-4">
                        @foreach($results['reservations'] as $reservation)
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-semibold text-gray-800 dark:text-white">حجز #{{ $reservation->reservation_id }}</h3>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                                            طاولة #{{ $reservation->table->table_number ?? 'غير محدد' }} - {{ $reservation->party_size }} أشخاص
                                        </p>
                                        <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $reservation->reservation_time->format('Y-m-d H:i') }}</p>
                                    </div>
                                    <div class="text-left">
                                        <span class="text-xs px-2 py-1 rounded-full {{ $reservation->status === 'confirmed' ? 'bg-green-100 text-green-600' : ($reservation->status === 'pending' ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-600') }}">
                                            {{ $reservation->status === 'pending' ? 'في الانتظار' : ($reservation->status === 'confirmed' ? 'مؤكد' : 'ملغي') }}
                                        </span>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <a href="{{ route('customer.reservations.show', $reservation->reservation_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                        عرض التفاصيل <i class="fas fa-chevron-left mr-1"></i>
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="mt-6">
                        {{ $results['reservations']->appends(request()->query())->links() }}
                    </div>
                </div>
            @endif
            @endauth

            <!-- رسالة عدم وجود نتائج -->
            @if($results['menu']->count() === 0 && (!auth()->check() || ($results['orders']->count() === 0 && $results['reservations']->count() === 0)))
                <div class="text-center py-12">
                    <i class="fas fa-search text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد نتائج</h3>
                    <p class="text-gray-500 dark:text-gray-500">لم نجد أي نتائج تطابق بحثك. جرب كلمات مختلفة.</p>
                </div>
            @endif
        @else
            <!-- صفحة البحث الفارغة -->
            <div class="text-center py-12">
                <i class="fas fa-search text-6xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">ابحث عن أي شيء</h3>
                <p class="text-gray-500 dark:text-gray-500">استخدم شريط البحث أعلاه للعثور على الأطباق والطلبات والحجوزات.</p>
            </div>
        @endif
    </div>
@endsection
