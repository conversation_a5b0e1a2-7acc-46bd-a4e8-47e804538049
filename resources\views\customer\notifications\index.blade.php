@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">الإشعارات</h1>
                <p class="text-gray-600 dark:text-gray-400">تابع آخر التحديثات والإشعارات الخاصة بك</p>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="flex space-x-2 space-x-reverse">
                <form action="{{ route('customer.notifications.read-all') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
                        <i class="fas fa-check-double ml-2"></i>
                        تحديد الكل كمقروء
                    </button>
                </form>
            </div>
        </div>

        <!-- رسائل النجاح والخطأ -->
        @if(session('success'))
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                {{ session('error') }}
            </div>
        @endif

        <!-- قائمة الإشعارات -->
        @if($notifications->count() > 0)
            <div class="space-y-4">
                @foreach($notifications as $notification)
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border-r-4 {{ !$notification->is_read ? 'border-primary bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600' }}">
                        <div class="p-6">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-4 space-x-reverse flex-1">
                                    <!-- أيقونة الإشعار -->
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 rounded-full {{ !$notification->is_read ? 'bg-primary' : 'bg-gray-400' }} flex items-center justify-center">
                                            @if($notification->type === 'order')
                                                <i class="fas fa-shopping-bag text-white text-sm"></i>
                                            @elseif($notification->type === 'reservation')
                                                <i class="fas fa-calendar-check text-white text-sm"></i>
                                            @elseif($notification->type === 'system')
                                                <i class="fas fa-cog text-white text-sm"></i>
                                            @else
                                                <i class="fas fa-bell text-white text-sm"></i>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- محتوى الإشعار -->
                                    <div class="flex-1 min-w-0">
                                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-1">
                                            {{ $notification->title ?? 'إشعار' }}
                                        </h3>

                                        <p class="text-gray-600 dark:text-gray-400 mb-2">
                                            {{ $notification->message }}
                                        </p>

                                        <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500 dark:text-gray-400">
                                            <span>
                                                <i class="fas fa-clock ml-1"></i>
                                                {{ $notification->created_at->diffForHumans() }}
                                            </span>

                                            @if($notification->type)
                                                <span class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-xs">
                                                    @if($notification->type === 'order')
                                                        طلب
                                                    @elseif($notification->type === 'reservation')
                                                        حجز
                                                    @elseif($notification->type === 'system')
                                                        نظام
                                                    @else
                                                        عام
                                                    @endif
                                                </span>
                                            @endif

                                            @if(!$notification->is_read)
                                                <span class="px-2 py-1 bg-primary text-white rounded-full text-xs">
                                                    جديد
                                                </span>
                                            @endif
                                        </div>

                                        <!-- رابط الإجراء -->
                                        @if($notification->action_url && $notification->action_text)
                                            <div class="mt-3">
                                                <a href="{{ $notification->action_url }}"
                                                   class="inline-flex items-center text-primary hover:text-primary/80 text-sm font-medium">
                                                    {{ $notification->action_text }}
                                                    <i class="fas fa-chevron-left mr-1"></i>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- أزرار الإجراءات -->
                                <div class="flex items-center space-x-2 space-x-reverse">
                                    @if(!$notification->is_read)
                                        <form action="{{ route('customer.notifications.read', $notification->notification_id) }}" method="POST" class="inline">
                                            @csrf
                                            <button type="submit"
                                                    class="p-2 text-gray-400 hover:text-primary transition"
                                                    title="تحديد كمقروء">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    @endif

                                    <form action="{{ route('customer.notifications.delete', $notification->notification_id) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit"
                                                class="p-2 text-gray-400 hover:text-red-500 transition"
                                                title="حذف الإشعار"
                                                onclick="return confirm('هل أنت متأكد من حذف هذا الإشعار؟')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $notifications->links() }}
            </div>
        @else
            <!-- رسالة عدم وجود إشعارات -->
            <div class="text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                    <i class="fas fa-bell-slash text-3xl text-gray-400"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد إشعارات</h3>
                <p class="text-gray-500 dark:text-gray-500">ستظهر هنا جميع الإشعارات والتحديثات الخاصة بك.</p>

                <div class="mt-6">
                    <a href="{{ route('customer.index') }}"
                       class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg transition">
                        <i class="fas fa-home ml-2"></i>
                        العودة للرئيسية
                    </a>
                </div>
            </div>
        @endif
    </div>
</main>

@include('customer.partials.footer')
@include('customer.partials.scripts')

</body>
</html>
