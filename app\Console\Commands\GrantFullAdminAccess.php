<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;

class GrantFullAdminAccess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:grant-full-access {email? : البريد الإلكتروني للمدير}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'منح المديرين جميع الصلاحيات المتاحة في النظام';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');

        if ($email) {
            // منح صلاحيات لمدير محدد
            $this->grantFullAccessToAdmin($email);
        } else {
            // منح صلاحيات لجميع المديرين
            $this->grantFullAccessToAllAdmins();
        }
    }

    /**
     * منح صلاحيات كاملة لمدير محدد
     */
    private function grantFullAccessToAdmin($email)
    {
        $admin = User::where('email', $email)->first();

        if (!$admin) {
            $this->error("المستخدم غير موجود: {$email}");
            return;
        }

        if ($admin->user_type !== 'admin') {
            $this->error("المستخدم ليس مديراً: {$email}");
            return;
        }

        $this->grantAllPermissions($admin);
        $this->info("تم منح جميع الصلاحيات للمدير: {$admin->first_name} {$admin->last_name} ({$email})");
    }

    /**
     * منح صلاحيات كاملة لجميع المديرين
     */
    private function grantFullAccessToAllAdmins()
    {
        $admins = User::where('user_type', 'admin')->get();

        if ($admins->isEmpty()) {
            $this->warn('لا يوجد مديرين في النظام');
            return;
        }

        $this->info("تم العثور على {$admins->count()} مدير/مديرة");

        foreach ($admins as $admin) {
            $this->grantAllPermissions($admin);
            $this->info("✓ تم منح الصلاحيات للمدير: {$admin->first_name} {$admin->last_name} ({$admin->email})");
        }

        $this->info('تم منح جميع الصلاحيات لجميع المديرين بنجاح!');
    }

    /**
     * منح جميع الصلاحيات المتاحة للمستخدم
     */
    private function grantAllPermissions(User $user)
    {
        // الحصول على جميع الصلاحيات المتاحة
        $allPermissions = Permission::all();

        // منح جميع الصلاحيات
        $user->givePermissionTo($allPermissions);

        // مسح cache الصلاحيات
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
    }
}
