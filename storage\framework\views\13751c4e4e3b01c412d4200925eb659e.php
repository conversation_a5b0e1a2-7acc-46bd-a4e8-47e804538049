<!-- الهيدر -->
<header class="bg-white dark:bg-gray-800 shadow-lg z-10 border-b border-gray-200 dark:border-gray-700">
    <div class="px-6 py-4 flex justify-between items-center">
        <!-- زر فتح/إغلاق القائمة الجانبية -->
        <button id="sidebarToggle" class="md:hidden p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
            <i class="fas fa-bars text-xl group-hover:scale-110 transition-transform"></i>
        </button>

        <!-- عنوان الصفحة - سيتم تغييره ديناميكياً -->
        <h1 id="pageTitle" class="text-2xl font-bold hidden md:block text-gray-800 dark:text-white">لوحة التحكم</h1>

        <!-- الأدوات والإشعارات -->
        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- الإشعارات -->
            <div class="relative">
                <button id="notificationBtn" class="p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group relative">
                    <i class="fas fa-bell text-xl group-hover:animate-bounce"></i>
                    <?php if(isset($unreadNotifications) && $unreadNotifications > 0): ?>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center animate-pulse">
                        <?php echo e($unreadNotifications > 9 ? '9+' : $unreadNotifications); ?>

                    </span>
                    <?php endif; ?>
                </button>

                <!-- قائمة الإشعارات المنسدلة -->
                <div id="notificationDropdown" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transform scale-95 transition-all duration-300 z-50">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                            <?php if(isset($unreadNotifications) && $unreadNotifications > 0): ?>
                            <span class="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 text-xs px-2 py-1 rounded-full">
                                <?php echo e($unreadNotifications); ?> جديد
                            </span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="max-h-96 overflow-y-auto">
                        <?php if(isset($notifications) && $notifications->count() > 0): ?>
                            <?php $__currentLoopData = $notifications->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                                <div class="flex items-start space-x-3 space-x-reverse">
                                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-info text-white text-sm"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-800 dark:text-white truncate">
                                            <?php echo e($notification->title ?? 'إشعار جديد'); ?>

                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            <?php echo e($notification->created_at->diffForHumans()); ?>

                                        </p>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                        <div class="p-8 text-center">
                            <i class="fas fa-bell-slash text-4xl text-gray-300 dark:text-gray-600 mb-3"></i>
                            <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات جديدة</p>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="p-4 border-t border-gray-200 dark:border-gray-700">
                        <a href="<?php echo e(route('admin.notifications')); ?>" class="block w-full text-center py-2 text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors">
                            عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
            </div>

            <!-- زر تبديل الثيم -->
            <button data-theme-toggle class="theme-toggle p-3 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group" title="تبديل الثيم">
                <i class="theme-icon fas fa-adjust group-hover:rotate-180 transition-transform duration-500"></i>
            </button>

            <!-- صورة المستخدم -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center p-2 rounded-xl bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white transition-all duration-300 group">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center text-white font-bold overflow-hidden ring-2 ring-white/20 group-hover:ring-white/40 transition-all duration-300">
                        <?php if(Auth::user()->profile_image): ?>
                            <img src="<?php echo e(asset('storage/' . Auth::user()->profile_image)); ?>" alt="<?php echo e(Auth::user()->first_name); ?>" class="w-full h-full object-cover">
                        <?php else: ?>
                            <?php echo e(substr(Auth::user()->first_name, 0, 1)); ?>

                        <?php endif; ?>
                    </div>
                    <div class="mr-3 text-right hidden lg:block">
                        <p class="text-sm font-bold"><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></p>
                        <p class="text-xs opacity-70">مدير النظام</p>
                    </div>
                    <i class="fas fa-chevron-down text-xs mr-2 group-hover:rotate-180 transition-transform duration-300"></i>
                </button>

                <!-- قائمة المستخدم المنسدلة -->
                <div id="userDropdown" class="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-gray-200 dark:border-gray-700 opacity-0 invisible transform scale-95 transition-all duration-300 z-50">
                    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center text-white font-bold overflow-hidden">
                                <?php if(Auth::user()->profile_image): ?>
                                    <img src="<?php echo e(asset('storage/' . Auth::user()->profile_image)); ?>" alt="<?php echo e(Auth::user()->first_name); ?>" class="w-full h-full object-cover">
                                <?php else: ?>
                                    <?php echo e(substr(Auth::user()->first_name, 0, 1)); ?>

                                <?php endif; ?>
                            </div>
                            <div class="flex-1">
                                <p class="font-bold text-gray-800 dark:text-white"><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></p>
                                <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e(Auth::user()->email); ?></p>
                                <div class="flex items-center mt-1">
                                    <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                                    <span class="text-xs text-red-600 dark:text-red-400 font-bold">مدير النظام</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="p-2">
                        <a href="<?php echo e(route('admin.profile')); ?>" class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">الملف الشخصي</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">إدارة حسابك</p>
                            </div>
                        </a>

                        <a href="<?php echo e(route('admin.settings')); ?>" class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-cog text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">الإعدادات</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">إعدادات النظام</p>
                            </div>
                        </a>

                        <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>
                        <div class="px-6 py-2">
                            <span class="text-xs text-gray-500 dark:text-gray-400 font-medium uppercase tracking-wider">التنقل السريع</span>
                        </div>
                        <a href="<?php echo e(route('employee.dashboard')); ?>" class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-user-tie text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">واجهة الموظف</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">عرض النظام كموظف</p>
                            </div>
                        </a>

                        <a href="<?php echo e(route('home')); ?>" class="flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 group">
                            <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                <i class="fas fa-home text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-medium">الموقع الرئيسي</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">العودة للموقع</p>
                            </div>
                        </a>

                        <div class="border-t border-gray-200 dark:border-gray-600 my-2"></div>

                        <form method="POST" action="<?php echo e(route('logout')); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="w-full flex items-center px-4 py-3 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-xl transition-all duration-300 group">
                                <div class="w-8 h-8 rounded-lg bg-gradient-to-br from-red-500 to-pink-500 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
                                    <i class="fas fa-sign-out-alt text-white text-sm"></i>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium">تسجيل الخروج</p>
                                    <p class="text-xs opacity-70">إنهاء الجلسة</p>
                                </div>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // قائمة الإشعارات
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationDropdown = document.getElementById('notificationDropdown');

    if (notificationBtn && notificationDropdown) {
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdown.classList.toggle('opacity-0');
            notificationDropdown.classList.toggle('invisible');
            notificationDropdown.classList.toggle('scale-95');
        });
    }

    // قائمة المستخدم
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userDropdown = document.getElementById('userDropdown');

    if (userMenuBtn && userDropdown) {
        userMenuBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('opacity-0');
            userDropdown.classList.toggle('invisible');
            userDropdown.classList.toggle('scale-95');
        });
    }

    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function() {
        if (notificationDropdown) {
            notificationDropdown.classList.add('opacity-0', 'invisible', 'scale-95');
        }
        if (userDropdown) {
            userDropdown.classList.add('opacity-0', 'invisible', 'scale-95');
        }
    });
});
</script>
<?php /**PATH D:\ccss450\cs450level10\resources\views/admin/layouts/header.blade.php ENDPATH**/ ?>