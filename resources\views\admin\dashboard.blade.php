@extends('admin.layouts.app')

@section('content')
<!-- Hero Section -->
<div class="relative mb-8 overflow-hidden">
    <div class="bg-white dark:bg-gray-800 rounded-3xl p-8 border border-gray-200 dark:border-gray-700 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-4xl font-black text-gray-800 dark:text-white mb-3">
                    مرحباً، <span class="bg-gradient-to-r from-red-600 to-pink-600 bg-clip-text text-transparent">{{ Auth::user()->first_name ?? 'المدير' }}</span>!
                    <span class="inline-block animate-bounce">👑</span>
                </h1>
                <p class="text-gray-600 dark:text-gray-400 text-lg font-medium">
                    إليك نظرة عامة شاملة على أداء المطعم
                </p>
                @if(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin'))
                <p class="text-sm text-amber-600 dark:text-amber-400 mt-2 flex items-center">
                    <i class="fas fa-info-circle ml-2"></i>
                    أنت موظف بصلاحيات إدارية
                </p>
                @endif
            </div>
            <div class="hidden md:block">
                <div class="w-32 h-32 rounded-full bg-gradient-to-br from-red-400 via-pink-500 to-purple-600 flex items-center justify-center animate-float shadow-2xl">
                    <i class="fas fa-crown text-white text-4xl"></i>
                </div>
            </div>
        </div>

        <!-- أزرار التنقل السريع -->
        <div class="flex flex-wrap gap-3 mt-6">
            @if(Auth::user()->user_type == 'admin')
            <a href="{{ route('employee.dashboard') }}"
               class="flex items-center px-4 py-2 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors">
                <i class="fas fa-user-tie ml-2"></i>
                <span class="text-sm font-medium">واجهة الموظف</span>
            </a>
            @elseif(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin'))
            <a href="{{ route('employee.dashboard') }}"
               class="flex items-center px-4 py-2 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                <i class="fas fa-arrow-left ml-2"></i>
                <span class="text-sm font-medium">العودة لواجهة الموظف</span>
            </a>
            @endif
        </div>
        <div class="text-right">
            <div class="text-md font-semibold text-gray-900 dark:text-white">
                {{ now()->format('Y/m/d') }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ now()->format('H:i') }}
            </div>
        </div>
    </div>

    @if(Auth::user()->user_type == 'employee' && Auth::user()->can('dashboard.admin'))
    <!-- إشعار للموظف بالصلاحيات الإدارية -->
    <div class="bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20 border border-amber-200 dark:border-amber-700 rounded-lg p-4 mb-6">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <i class="fas fa-user-shield text-amber-500 text-xl"></i>
            </div>
            <div class="mr-3 flex-1">
                <h3 class="text-sm font-medium text-amber-800 dark:text-amber-200">
                    صلاحيات إدارية خاصة
                </h3>
                <div class="mt-2 text-sm text-amber-700 dark:text-amber-300">
                    <p>لديك صلاحيات للوصول لواجهة الإدارة. يمكنك التنقل بين واجهة الموظف والإدارة حسب الحاجة.</p>
                </div>
                <div class="mt-3 flex space-x-2 space-x-reverse">
                    <a href="{{ route('employee.dashboard') }}"
                       class="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-amber-700 bg-amber-100 hover:bg-amber-200 dark:bg-amber-800 dark:text-amber-200 dark:hover:bg-amber-700 transition-colors">
                        <i class="fas fa-arrow-left ml-1"></i>
                        العودة لواجهة الموظف
                    </a>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- البطاقات الإحصائية -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- بطاقة إحصائية - الإيرادات -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg p-4 text-white card-hover">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <p class="text-blue-100 text-sm mb-1">إجمالي الإيرادات</p>
                    <h3 class="text-2xl font-bold mb-2">{{ number_format($totalRevenue, 0) }}</h3>
                    <div class="flex items-center text-sm">
                        <div class="flex items-center {{ $revenuePercentChange >= 0 ? 'text-green-200' : 'text-red-200' }}">
                            <i class="fas fa-arrow-{{ $revenuePercentChange >= 0 ? 'up' : 'down' }} mr-1"></i>
                            <span>{{ number_format(abs($revenuePercentChange), 1) }}%</span>
                        </div>
                        <span class="text-blue-100 mr-2">من الشهر الماضي</span>
                    </div>
                </div>
                <div class="bg-white/20 rounded-lg p-2">
                    <i class="fas fa-chart-line text-xl"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة إحصائية - الطلبات -->
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl shadow-lg p-4 text-white card-hover">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <p class="text-green-100 text-sm mb-1">إجمالي الطلبات</p>
                    <h3 class="text-2xl font-bold mb-2">{{ number_format($totalOrders) }}</h3>
                    <div class="flex items-center text-sm">
                        <div class="flex items-center {{ $orderPercentChange >= 0 ? 'text-green-200' : 'text-red-200' }}">
                            <i class="fas fa-arrow-{{ $orderPercentChange >= 0 ? 'up' : 'down' }} mr-1"></i>
                            <span>{{ number_format(abs($orderPercentChange), 1) }}%</span>
                        </div>
                        <span class="text-green-100 mr-2">من الشهر الماضي</span>
                    </div>
                </div>
                <div class="bg-white/20 rounded-lg p-2">
                    <i class="fas fa-shopping-cart text-xl"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة إحصائية - العملاء -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg p-4 text-white card-hover">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <p class="text-purple-100 text-sm mb-1">إجمالي العملاء</p>
                    <h3 class="text-2xl font-bold mb-2">{{ number_format($totalCustomers) }}</h3>
                    <div class="flex items-center text-sm">
                        <div class="flex items-center {{ $customerPercentChange >= 0 ? 'text-green-200' : 'text-red-200' }}">
                            <i class="fas fa-arrow-{{ $customerPercentChange >= 0 ? 'up' : 'down' }} mr-1"></i>
                            <span>{{ number_format(abs($customerPercentChange), 1) }}%</span>
                        </div>
                        <span class="text-purple-100 mr-2">من الشهر الماضي</span>
                    </div>
                </div>
                <div class="bg-white/20 rounded-lg p-2">
                    <i class="fas fa-users text-xl"></i>
                </div>
            </div>
        </div>

        <!-- بطاقة إحصائية - التقييمات -->
        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl shadow-lg p-4 text-white card-hover">
            <div class="flex justify-between items-start">
                <div class="flex-1">
                    <p class="text-orange-100 text-sm mb-1">متوسط التقييم</p>
                    <h3 class="text-2xl font-bold mb-2">{{ number_format($averageRating, 1) }}</h3>
                    <div class="flex items-center text-sm">
                        <div class="flex items-center text-yellow-200">
                            <i class="fas fa-star mr-1"></i>
                            <span>{{ $totalMenuItems }} عنصر</span>
                        </div>
                        <span class="text-orange-100 mr-2">في القائمة</span>
                    </div>
                </div>
                <div class="bg-white/20 rounded-lg p-2">
                    <i class="fas fa-star text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية والتقارير -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- مخطط المبيعات -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">تحليل المبيعات</h3>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <a href="{{ route('admin.reports') }}" class="text-primary text-sm hover:underline flex items-center">
                        <i class="fas fa-chart-bar ml-1"></i>
                        <span>التقارير</span>
                    </a>
                    <div class="relative">
                        <select id="dashboardPeriodFilter" onchange="updateDashboardChart()" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary">
                            <option value="7">آخر 7 أيام</option>
                            <option value="30" selected>آخر 30 يوم</option>
                            <option value="90">آخر 3 أشهر</option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="salesChart" class="w-full h-80 min-h-80"></div>
        </div>

        <!-- أفضل المنتجات مبيعاً -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">الأكثر مبيعاً</h3>
                <a href="{{ route('admin.reports.sales') }}" class="text-primary text-sm hover:underline flex items-center">
                    <span>عرض التقرير</span>
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
            </div>
            <div class="space-y-3">
                @forelse($topSellingItems as $index => $item)
                <div class="flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-primary to-orange-500 flex items-center justify-center text-white font-bold text-sm">
                        {{ $index + 1 }}
                    </div>
                    <div class="mr-3 flex-1">
                        <h4 class="font-medium text-gray-800 dark:text-white text-sm">{{ $item->name }}</h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400">{{ $item->total_ordered }} طلب</p>
                    </div>
                    <div class="text-primary font-semibold">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                @empty
                <div class="text-center py-6 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-chart-bar text-3xl mb-2"></i>
                    <p>لا توجد بيانات مبيعات</p>
                </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- أحدث الطلبات والمخزون -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <!-- أحدث الطلبات -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">أحدث الطلبات</h3>
                <a href="{{ route('admin.orders') }}" class="text-primary text-sm hover:underline flex items-center">
                    <span>عرض الكل</span>
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
            </div>
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                        <th class="py-2 px-3 text-right">#</th>
                        <th class="py-2 px-3 text-right">العميل</th>
                        <th class="py-2 px-3 text-right">المبلغ</th>
                        <th class="py-2 px-3 text-right">الحالة</th>
                        <th class="py-2 px-3 text-right">التاريخ</th>
                        <th class="py-2 px-3 text-right">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($recentOrders as $order)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="py-3 px-3">#ORD-{{ $order->order_id }}</td>
                        <td class="py-3 px-3">{{ $order->user->first_name }} {{ $order->user->last_name }}</td>
                        <td class="py-3 px-3">{{ number_format($order->total_amount, 2) }} د.ل</td>
                        <td class="py-3 px-3">
                            @if($order->status == 'completed')
                            <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs">مكتمل</span>
                            @elseif($order->status == 'preparing')
                            <span class="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full text-xs">قيد التحضير</span>
                            @elseif($order->status == 'pending')
                            <span class="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full text-xs">قيد الانتظار</span>
                            @else
                            <span class="px-2 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-xs">ملغي</span>
                            @endif
                        </td>
                        <td class="py-3 px-3">{{ $order->created_at->diffForHumans() }}</td>
                        <td class="py-3 px-3">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.orders.show', $order->order_id) }}" class="text-blue-500 hover:text-blue-700"><i class="fas fa-eye"></i></a>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="py-4 px-3 text-center text-gray-500">لا توجد طلبات حديثة</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

        <!-- المخزون منخفض المستوى -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">تنبيهات المخزون</h3>
                <a href="{{ route('admin.inventory') }}" class="text-primary text-sm hover:underline flex items-center">
                    <span>إدارة المخزون</span>
                    <i class="fas fa-arrow-left mr-1"></i>
                </a>
            </div>

            <!-- المخزون المنخفض -->
            <div class="mb-4">
                <h4 class="text-sm font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                    <i class="fas fa-exclamation-triangle text-yellow-500 ml-2"></i>
                    مخزون منخفض
                </h4>
                <div class="space-y-2">
                    @forelse($lowStockItems as $item)
                    <div class="flex items-center justify-between p-2 rounded-lg {{ $item->quantity < 5 ? 'bg-red-50 dark:bg-red-900/20' : 'bg-yellow-50 dark:bg-yellow-900/20' }}">
                        <div class="flex items-center">
                            <div class="h-2 w-2 rounded-full {{ $item->quantity < 5 ? 'bg-red-500' : 'bg-yellow-500' }} ml-2"></div>
                            <span class="text-gray-800 dark:text-white font-medium text-sm">{{ $item->name }}</span>
                        </div>
                        <span class="{{ $item->quantity < 5 ? 'text-red-600 dark:text-red-400' : 'text-yellow-600 dark:text-yellow-400' }} text-xs font-semibold">
                            {{ $item->quantity }} {{ $item->unit }}
                        </span>
                    </div>
                    @empty
                    <div class="text-center py-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-1"></i>
                        <p class="text-sm">جميع المكونات متوفرة</p>
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- انتهاء الصلاحية -->
            <div class="pt-3 border-t border-gray-200 dark:border-gray-700">
                <h4 class="text-sm font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                    <i class="fas fa-clock text-orange-500 ml-2"></i>
                    قاربت على انتهاء الصلاحية
                </h4>
                <div class="space-y-2">
                    @forelse($expiringItems as $item)
                    <div class="flex items-center justify-between p-2 rounded-lg bg-orange-50 dark:bg-orange-900/20">
                        <span class="text-gray-800 dark:text-white font-medium text-sm">{{ $item->name }}</span>
                        <span class="text-orange-600 dark:text-orange-400 text-xs font-semibold">
                            {{ number_format(now()->floatDiffInDays($item->expiry_date), 0) }} أيام
                        </span>
                    </div>
                    @empty
                    <div class="text-center py-3 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-check-circle text-green-500 text-xl mb-1"></i>
                        <p class="text-sm">لا توجد مكونات قاربت على الانتهاء</p>
                    </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // مخطط المبيعات مع دعم كامل للوضع المظلم
        console.log('🔄 تحميل بيانات المخطط...');

        // التأكد من وجود البيانات
        const rawSalesData = {!! json_encode($salesData ?? []) !!};
        console.log('📊 البيانات الخام:', rawSalesData);

        var salesData = rawSalesData.map(item => parseFloat(item.amount || 0));
        var salesDays = rawSalesData.map(item => item.day || 'غير محدد');

        console.log('📈 بيانات المبيعات:', salesData);
        console.log('📅 أيام المبيعات:', salesDays);

        // دالة لإنشاء المخطط مع دعم كامل للوضع المظلم
        function createSalesChart() {
            // التحقق من وجود مدير الوضع المظلم
            if (!window.simpleDarkModeFix) {
                console.warn('⚠️ مدير الوضع المظلم غير متاح، استخدام الإعدادات الافتراضية');
                return createBasicChart();
            }

            const colors = window.simpleDarkModeFix.getThemeColors();
            const isDark = window.simpleDarkModeFix.getCurrentTheme() === 'dark';

            var salesChartOptions = {
                series: [{
                    name: 'المبيعات (د.ل)',
                    data: salesData
                }],
                chart: {
                    height: 320,
                    type: 'area',
                    fontFamily: 'Cairo, sans-serif',
                    background: 'transparent',
                    toolbar: {
                        show: false
                    },
                    zoom: {
                        enabled: false
                    },
                    animations: {
                        enabled: true,
                        easing: 'easeinout',
                        speed: 800
                    }
                },
                theme: {
                    mode: isDark ? 'dark' : 'light'
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                colors: [colors.primary],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.1,
                        stops: [0, 100],
                        colorStops: [
                            {
                                offset: 0,
                                color: colors.primary,
                                opacity: 0.7
                            },
                            {
                                offset: 100,
                                color: colors.primary,
                                opacity: 0.1
                            }
                        ]
                    }
                },
                grid: {
                    borderColor: colors.border,
                    strokeDashArray: 2
                },
                xaxis: {
                    categories: salesDays,
                    labels: {
                        style: {
                            colors: colors.textSecondary,
                            fontFamily: 'Cairo, sans-serif'
                        },
                        rotate: -45,
                        rotateAlways: false
                    },
                    axisBorder: {
                        color: colors.border
                    },
                    axisTicks: {
                        color: colors.border
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(value) {
                            return value.toFixed(0) + ' د.ل';
                        },
                        style: {
                            colors: colors.textSecondary,
                            fontFamily: 'Cairo, sans-serif'
                        }
                    }
                },
                tooltip: {
                    theme: isDark ? 'dark' : 'light',
                    style: {
                        fontSize: '12px',
                        fontFamily: 'Cairo, sans-serif'
                    },
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' د.ل';
                        }
                    }
                },
                legend: {
                    show: false
                },
                responsive: [{
                    breakpoint: 768,
                    options: {
                        chart: {
                            height: 250
                        }
                    }
                }]
            };

            return salesChartOptions;
        }

        // دالة احتياطية للمخطط
        function createBasicChart() {
            const isDark = document.documentElement.classList.contains('dark');

            return {
                series: [{
                    name: 'المبيعات (د.ل)',
                    data: salesData.length > 0 ? salesData : [0, 0, 0, 0, 0, 0, 0]
                }],
                chart: {
                    height: 320,
                    type: 'area',
                    fontFamily: 'Cairo, sans-serif',
                    background: 'transparent',
                    toolbar: { show: false },
                    zoom: { enabled: false }
                },
                theme: {
                    mode: isDark ? 'dark' : 'light'
                },
                dataLabels: { enabled: false },
                stroke: { curve: 'smooth', width: 3 },
                colors: [isDark ? '#3b82f6' : '#f97316'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.1,
                        stops: [0, 100]
                    }
                },
                grid: {
                    borderColor: isDark ? '#374151' : '#e5e7eb',
                    strokeDashArray: 2
                },
                xaxis: {
                    categories: salesDays.length > 0 ? salesDays : ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'],
                    labels: {
                        style: {
                            colors: isDark ? '#9ca3af' : '#6b7280',
                            fontFamily: 'Cairo, sans-serif'
                        }
                    },
                    axisBorder: { color: isDark ? '#374151' : '#e5e7eb' },
                    axisTicks: { color: isDark ? '#374151' : '#e5e7eb' }
                },
                yaxis: {
                    labels: {
                        formatter: function(value) {
                            return value.toFixed(0) + ' د.ل';
                        },
                        style: {
                            colors: isDark ? '#9ca3af' : '#6b7280',
                            fontFamily: 'Cairo, sans-serif'
                        }
                    }
                },
                tooltip: {
                    theme: isDark ? 'dark' : 'light',
                    style: { fontSize: '12px', fontFamily: 'Cairo, sans-serif' },
                    y: {
                        formatter: function(value) {
                            return value.toFixed(2) + ' د.ل';
                        }
                    }
                },
                legend: { show: false },
                responsive: [{
                    breakpoint: 768,
                    options: { chart: { height: 250 } }
                }]
            };
        }

        // إنشاء المخطط
        let salesChart;

        function initSalesChart() {
            const chartElement = document.getElementById('salesChart');
            if (!chartElement) {
                console.error('❌ عنصر المخطط غير موجود');
                return;
            }

            try {
                // تدمير المخطط السابق إن وجد
                if (salesChart) {
                    salesChart.destroy();
                    salesChart = null;
                }

                // التحقق من وجود البيانات
                if (salesData.length === 0) {
                    console.warn('⚠️ لا توجد بيانات مبيعات، عرض مخطط فارغ');
                    chartElement.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-80 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-chart-area text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium mb-2">لا توجد بيانات مبيعات</h3>
                            <p class="text-sm text-center">لم يتم العثور على بيانات مبيعات لعرضها في المخطط</p>
                        </div>
                    `;
                    return;
                }

                // إنشاء المخطط
                const chartOptions = createSalesChart();
                salesChart = new ApexCharts(chartElement, chartOptions);

                salesChart.render().then(() => {
                    console.log('✅ تم إنشاء مخطط المبيعات بنجاح');
                }).catch(error => {
                    console.error('❌ خطأ في رسم المخطط:', error);
                    showChartError(chartElement);
                });

            } catch (error) {
                console.error('❌ خطأ في إنشاء المخطط:', error);
                showChartError(chartElement);
            }
        }

        // دالة عرض خطأ المخطط
        function showChartError(element) {
            element.innerHTML = `
                <div class="flex flex-col items-center justify-center h-80 text-red-500">
                    <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium mb-2">خطأ في تحميل المخطط</h3>
                    <p class="text-sm text-center">حدث خطأ أثناء تحميل مخطط المبيعات</p>
                    <button onclick="initSalesChart()" class="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                        إعادة المحاولة
                    </button>
                </div>
            `;
        }

        // إنشاء المخطط الأولي
        initSalesChart();

        // تسجيل المخطط مع مدير الوضع المظلم
        setTimeout(function() {
            if (salesChart && window.simpleDarkModeFix) {
                window.simpleDarkModeFix.registerChart('salesChart', salesChart);
                console.log('✅ تم تسجيل مخطط المبيعات مع مدير الوضع المظلم');
            }
        }, 1000);
    });

    // دالة تحديث مخطط لوحة التحكم
    function updateDashboardChart() {
        const period = document.getElementById('dashboardPeriodFilter').value;

        // إظهار مؤشر التحميل
        const chartElement = document.getElementById('salesChart');
        if (chartElement) {
            chartElement.innerHTML = `
                <div class="flex items-center justify-center h-64">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span class="mr-3 text-gray-600 dark:text-gray-400">جاري التحديث...</span>
                </div>
            `;
        }

        // طلب البيانات الجديدة من الخادم
        fetch(`/admin/dashboard-chart-data?period=${period}`)
            .then(response => response.json())
            .then(data => {
                // إعادة إنشاء المخطط بالبيانات الجديدة
                updateDashboardChartData(data);
            })
            .catch(error => {
                console.error('خطأ في تحديث مخطط لوحة التحكم:', error);
                chartElement.innerHTML = `
                    <div class="flex items-center justify-center h-64 text-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span>حدث خطأ في تحديث المخطط</span>
                    </div>
                `;
            });
    }

    function updateDashboardChartData(data) {
        // تحديث البيانات العامة
        salesData = data.amounts || [];
        salesDays = data.days || [];

        // إعادة إنشاء المخطط بالبيانات الجديدة
        initSalesChart();
        console.log('✅ تم تحديث بيانات المخطط');
    }
</script>

<style>
/* إصلاح مشاكل المخططات */
#salesChart {
    position: relative;
    z-index: 1;
    overflow: hidden;
}

#salesChart .apexcharts-canvas {
    position: relative !important;
}

#salesChart .apexcharts-svg {
    background: transparent !important;
}

/* تحسين المخططات في الوضع المظلم */
.dark #salesChart .apexcharts-text {
    fill: #9ca3af !important;
}

.dark #salesChart .apexcharts-gridline {
    stroke: #374151 !important;
}

.dark #salesChart .apexcharts-xaxis-line,
.dark #salesChart .apexcharts-yaxis-line {
    stroke: #374151 !important;
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    #salesChart {
        height: 250px !important;
        min-height: 250px !important;
    }
}

/* إصلاح تداخل العناصر */
.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

/* تحسين الألوان في الوضع المظلم */
.dark .apexcharts-tooltip {
    background: #374151 !important;
    border: 1px solid #4b5563 !important;
    color: #e5e7eb !important;
}

.dark .apexcharts-tooltip-title {
    background: #4b5563 !important;
    border-bottom: 1px solid #6b7280 !important;
    color: #f3f4f6 !important;
}

/* تحسين شريط الأدوات */
.dark .apexcharts-toolbar {
    background: #374151 !important;
}

.dark .apexcharts-toolbar svg {
    fill: #9ca3af !important;
}
</style>

@endsection