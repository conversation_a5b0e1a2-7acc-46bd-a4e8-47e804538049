# دليل تخصيص الخريطة - Eat Hub

## 📍 تغيير موقع المطعم

### 1. الحصول على الإحداثيات الصحيحة
لتغيير موقع المطعم على الخريطة، تحتاج إلى الحصول على الإحداثيات الصحيحة:

**طرق الحصول على الإحداثيات:**
- افتح Google Maps
- ابحث عن موقع مطعمك
- انقر بزر الماوس الأيمن على الموقع
- اختر "ما هذا المكان؟"
- ستظهر الإحداثيات في الأسفل (مثال: 32.8872, 13.1913)

### 2. تحديث الإحداثيات في الكود
في ملف `resources/views/contact.blade.php`، ابحث عن هذا السطر:

```javascript
// إحداثيات طرابلس، ليبيا (يمكنك تغييرها لموقعك الفعلي)
const lat = 32.8872;  // خط العرض
const lng = 13.1913;  // خط الطول
```

غيّر القيم إلى إحداثيات موقعك الفعلي.

## 🏪 تخصيص معلومات المطعم

### 1. تغيير اسم المطعم
ابحث عن `Eat Hub` في الكود واستبدله باسم مطعمك.

### 2. تحديث العنوان
ابحث عن `شارع الجمهورية، طرابلس، ليبيا` واستبدله بعنوانك الفعلي.

### 3. تحديث رقم الهاتف
ابحث عن `+218 91 234 5678` واستبدله برقم هاتفك.

## 🎨 تخصيص شكل الخريطة

### 1. تغيير لون العلامة
في الكود، ابحث عن:
```javascript
background: #ff6b35;
```
غيّر `#ff6b35` إلى اللون المطلوب.

### 2. تغيير حجم منطقة الخدمة
ابحث عن:
```javascript
radius: 500  // 500 متر
```
غيّر القيمة حسب منطقة خدمتك.

### 3. تغيير مستوى التكبير
ابحث عن:
```javascript
.setView([lat, lng], 15);  // 15 هو مستوى التكبير
```

## 🔧 خيارات متقدمة

### 1. إضافة عدة مواقع (فروع)
```javascript
const branches = [
    {lat: 32.8872, lng: 13.1913, name: "الفرع الرئيسي"},
    {lat: 32.9000, lng: 13.2000, name: "فرع المدينة"}
];

branches.forEach(branch => {
    L.marker([branch.lat, branch.lng], { icon: restaurantIcon })
     .addTo(map)
     .bindPopup(`<h3>${branch.name}</h3>`);
});
```

### 2. تغيير نوع الخريطة
يمكنك استخدام خرائط مختلفة:

```javascript
// خريطة الأقمار الصناعية
L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}')

// خريطة طبوغرافية
L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png')
```

## 🌐 استخدام Google Maps بدلاً من OpenStreetMap

إذا كنت تفضل Google Maps، احصل على API key من Google Cloud Console:

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Maps JavaScript API
4. أنشئ API key
5. استبدل `YOUR_API_KEY` في الكود بالـ key الخاص بك

## 📱 تحسين الخريطة للهواتف المحمولة

الخريطة الحالية متجاوبة تلقائياً، لكن يمكنك إضافة تحسينات:

```css
@media (max-width: 768px) {
    #map {
        height: 300px; /* ارتفاع أقل للهواتف */
    }
}
```

## 🔍 إضافة البحث في الخريطة

```javascript
// إضافة مربع البحث
const searchControl = new L.Control.Search({
    url: 'https://nominatim.openstreetmap.org/search?format=json&q={s}',
    jsonpParam: 'json_callback',
    propertyName: 'display_name',
    propertyLoc: ['lat','lon']
});
map.addControl(searchControl);
```

## 🎯 نصائح مهمة

1. **اختبر الخريطة**: تأكد من أن الموقع صحيح قبل النشر
2. **سرعة التحميل**: OpenStreetMap أسرع من Google Maps
3. **الدقة**: Google Maps أكثر دقة في بعض المناطق
4. **التكلفة**: OpenStreetMap مجاني، Google Maps له حدود استخدام

## 🆘 حل المشاكل الشائعة

### الخريطة لا تظهر
- تحقق من اتصال الإنترنت
- تأكد من صحة الإحداثيات
- افتح Developer Tools وتحقق من الأخطاء

### العلامة في مكان خاطئ
- تأكد من ترتيب الإحداثيات (lat, lng)
- تحقق من دقة الإحداثيات

### الخريطة بطيئة
- قلل من عدد العلامات
- استخدم مستوى تكبير أقل
- فكر في استخدام خدمة أخرى

---

**ملاحظة**: هذا الدليل يغطي الأساسيات. لمزيد من التخصيص، راجع وثائق [Leaflet](https://leafletjs.com/) أو [Google Maps API](https://developers.google.com/maps).
