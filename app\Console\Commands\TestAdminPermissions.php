<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Models\Permission;

class TestAdminPermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:test-permissions {email : البريد الإلكتروني للمدير}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'اختبار صلاحيات المدير للتأكد من عملها بشكل صحيح';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("المستخدم غير موجود: {$email}");
            return;
        }

        $this->info("اختبار صلاحيات المستخدم: {$user->first_name} {$user->last_name}");
        $this->info("نوع المستخدم: {$user->user_type}");
        $this->newLine();

        // اختبار الدوال الأساسية
        $this->testBasicMethods($user);

        // اختبار الصلاحيات العشوائية
        $this->testRandomPermissions($user);

        // اختبار Gates المخصصة
        $this->testCustomGates($user);

        // اختبار جميع الصلاحيات الموجودة
        $this->testAllExistingPermissions($user);

        $this->newLine();
        if ($user->user_type === 'admin') {
            $this->info('✅ النتيجة: المدير يملك صلاحيات كاملة كما هو متوقع');
        } else {
            $this->info('ℹ️  النتيجة: المستخدم ليس مديراً، الصلاحيات محدودة حسب ما تم منحه');
        }
    }

    /**
     * اختبار الدوال الأساسية
     */
    private function testBasicMethods(User $user)
    {
        $this->info('🔍 اختبار الدوال الأساسية:');

        $tests = [
            'isAdmin()' => $user->isAdmin(),
            'isEmployee()' => $user->isEmployee(),
            'isCustomer()' => $user->isCustomer(),
        ];

        foreach ($tests as $method => $result) {
            $status = $result ? '✅' : '❌';
            $this->line("  {$status} {$method}: " . ($result ? 'true' : 'false'));
        }

        $this->newLine();
    }

    /**
     * اختبار صلاحيات عشوائية
     */
    private function testRandomPermissions(User $user)
    {
        $this->info('🔍 اختبار صلاحيات عشوائية:');

        $randomPermissions = [
            'orders.view',
            'orders.create',
            'orders.delete',
            'users.view',
            'users.create',
            'users.delete',
            'menu.view',
            'menu.create',
            'reports.financial',
            'settings.edit',
            'non.existent.permission'
        ];

        foreach ($randomPermissions as $permission) {
            $canAccess = $user->can($permission);
            $status = $canAccess ? '✅' : '❌';
            $this->line("  {$status} {$permission}: " . ($canAccess ? 'مسموح' : 'غير مسموح'));
        }

        $this->newLine();
    }

    /**
     * اختبار Gates المخصصة
     */
    private function testCustomGates(User $user)
    {
        $this->info('🔍 اختبار Gates المخصصة:');

        // تسجيل دخول المستخدم مؤقتاً للاختبار
        auth()->login($user);

        $gates = [
            'is-admin',
            'full-data-access',
            'manage-users',
            'manage-permissions',
            'access-all-reports',
            'access-all-orders',
            'delete-any-data',
            'edit-any-data'
        ];

        foreach ($gates as $gate) {
            try {
                $canAccess = Gate::allows($gate);
                $status = $canAccess ? '✅' : '❌';
                $this->line("  {$status} {$gate}: " . ($canAccess ? 'مسموح' : 'غير مسموح'));
            } catch (\Exception $e) {
                $this->line("  ⚠️  {$gate}: خطأ - {$e->getMessage()}");
            }
        }

        // تسجيل خروج
        auth()->logout();
        $this->newLine();
    }

    /**
     * اختبار جميع الصلاحيات الموجودة في النظام
     */
    private function testAllExistingPermissions(User $user)
    {
        $this->info('🔍 اختبار جميع الصلاحيات الموجودة في النظام:');

        $permissions = Permission::all();

        if ($permissions->isEmpty()) {
            $this->warn('  لا توجد صلاحيات في النظام');
            return;
        }

        $allowed = 0;
        $denied = 0;

        foreach ($permissions as $permission) {
            $canAccess = $user->can($permission->name);
            if ($canAccess) {
                $allowed++;
            } else {
                $denied++;
            }
        }

        $total = $permissions->count();
        $this->line("  📊 إجمالي الصلاحيات: {$total}");
        $this->line("  ✅ مسموحة: {$allowed}");
        $this->line("  ❌ مرفوضة: {$denied}");

        if ($user->user_type === 'admin' && $denied > 0) {
            $this->warn("  ⚠️  تحذير: المدير لديه صلاحيات مرفوضة! هذا غير متوقع.");
        }

        if ($user->user_type === 'admin' && $allowed === $total) {
            $this->info("  🎉 ممتاز: المدير يملك جميع الصلاحيات!");
        }

        $this->newLine();
    }
}
